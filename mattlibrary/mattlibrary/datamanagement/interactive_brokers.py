import math
import polars as pl
import logging
from typing import Callable, Union
from datetime import datetime, date, timedelta
from ib_async import IB, util
from ib_async.contract import Contract, Forex, Index, Future, ContFuture, Stock, Option, FuturesOption, ContractDetails
from ib_async.ticker import Ticker
from mattlibrary.trading.quote import Quote, QuoteType
from mattlibrary.trading.symbol import Symbol


class InteractiveBrokersAPI:

    def __init__(self, address, port, client_id, is_async_environment: bool):

        #constants
        self.TIMEOUT = 10 #seconds

        #logging
        self.logger = logging.getLogger(__name__)
        self.logger.info("Initializing InteractiveBrokersAPI")

        #connectivity
        self.address = address
        self.port = port
        self.client_id = client_id

        #check if we are in an async environment
        if is_async_environment:
            self.logger.info("Async environment detected, starting event loop")
            util.startLoop()

        #initialize ib api
        self.ib = IB()

        #subscriptions
        self.external_subscriptions = dict() #key=uniqueId, value=dict(symbol, qualified contract)
        self.internal_subscriptions = dict() #key=qualified contract, value=dict(symbol, callback)
        self.ib.pendingTickersEvent += self.on_incoming_tickers

    async def connect_async(self):
        try:
            await self.ib.connectAsync(self.address, self.port, self.client_id, timeout=self.TIMEOUT)
            self.logger.info("Connected to Interactive Brokers")
        except Exception as e:
            self.logger.error(f"Could not connect to Interactive Brokers: {e}")


    def disconnect(self):
        try:
            self.ib.disconnect()
            self.logger.info("Disconnected from Interactive Brokers")
        except Exception as e:
            self.logger.error(f"Could not disconnect from Interactive Brokers: {e}")


    def sleep(self, seconds: int):
        self.ib.sleep(seconds)
    

    def get_account_summary(self) -> pl.DataFrame:
        try:
            account = self.ib.managedAccounts()[0]
            summary = self.ib.accountSummary(account)
            return pl.DataFrame(summary)
        except Exception as e:
            self.logger.error(f"Could not get account summary: {e}")
            return pl.DataFrame()
        

    def get_positions(self) -> pl.DataFrame:
        try:
            positions = self.ib.positions()           
            return pl.DataFrame(positions)
        except Exception as e:
            self.logger.error(f"Could not get positions: {e}")
            return pl.DataFrame()
        
    
    def get_open_orders(self) -> pl.DataFrame:
        try:
            orders = self.ib.openTrades()
            return pl.DataFrame(orders)
        except Exception as e:
            self.logger.error(f"Could not get open orders: {e}")
            return pl.DataFrame()
        

    async def get_historical_bar_data_async(self, symbol: Symbol, start_datetime: Union[datetime, date], end_datetime: Union[datetime, date], bar_size: str, what_to_show: str = "MIDPOINT", use_rth_only: bool = True, format_date: int = 2) -> pl.DataFrame:        
        try:
            #calculate ideal duration string
            duration = self._get_ideal_duration_str(start_datetime, end_datetime, bar_size)
            
            #convert symbol to contract
            contract = self._symbol_to_contract(symbol)
                       
            #request historical bars
            bars = await self.ib.reqHistoricalDataAsync(contract, end_datetime, duration, bar_size, whatToShow=what_to_show, useRTH=use_rth_only, formatDate=format_date)
            
            #convert to polars dataframe
            df = pl.DataFrame(bars)
            
            #filter out bars outside of the requested date range and sort
            df = df.filter((pl.col("date") >= start_datetime) & (pl.col("date") <= end_datetime)).sort("date")
            
            #log
            self.logger.info(f"Received {len(bars)} bars for {symbol.symbol_id} with barSize {bar_size} and duration {duration}")
            
            #return dataframe
            return df
        except Exception as e:
            self.logger.error(f"Could not get historical data: {e}")
            return pl.DataFrame()
        

    async def get_historical_ticks_async(self, symbol: Symbol, start_datetime: datetime, end_datetime: datetime, number_of_ticks: int, what_to_show: str, use_rth_only: bool = True, ignore_update_size: bool = True):
        
        contract = self._symbol_to_contract(symbol)
        timezone = None

        if start_datetime is not None and end_datetime is not None:
            self.logger.error(f"Start and end datetime cannot both be specified for tick data")
            raise ValueError("Start and end datetime cannot both be specified for tick data")
        elif start_datetime is None and end_datetime is None:
            self.logger.error(f"Start or end datetime must be specified for tick data")
            raise ValueError("Start or end datetime must be specified for tick data")
        elif end_datetime.tzinfo is None or (start_datetime is not None and start_datetime.tzinfo != end_datetime.tzinfo):
            self.logger.error(f"Start and end datetime must have the same timezone information and cannot be None")
            raise ValueError("Start and end datetime must have the same timezone information and cannot be None")
        elif start_datetime is not None:
            end_datetime = ""
            timezone = start_datetime.tzinfo
        else:
            start_datetime = ""
            timezone = end_datetime.tzinfo

        #request tick data        
        result = await self.ib.reqHistoricalTicksAsync(contract, start_datetime, end_datetime, number_of_ticks, what_to_show, use_rth_only, ignore_update_size, [])
        
        #process returned data
        if len(result) == 0:
            return []

        if what_to_show.upper() == "MIDPOINT":
            converter = lambda x: Quote(symbol.symbol_id, QuoteType.MID, x.time.astimezone(timezone), mid=x.price, size=x.size)
        elif what_to_show.upper() == "BID_ASK":
            converter = lambda x: Quote(symbol.symbol_id, QuoteType.BID_ASK, x.time.astimezone(timezone), bid=x.priceBid, ask=x.priceAsk, bidSize=x.sizeBid, askSize=x.sizeAsk)
        elif what_to_show.upper() == "TRADES":
            converter = lambda x: Quote(symbol.symbol_id, QuoteType.TRADE, x.time.astimezone(timezone), trade=x.price, size=x.size)
        else:
            raise ValueError(f"Unsupported whatToShow value: {what_to_show}")

        return [converter(x) for x in result]
    

    def on_incoming_tickers(self, tickers: list[Ticker]):
        for ticker in tickers:
            if ticker.contract in self.internal_subscriptions:
                symbol = self.internal_subscriptions[ticker.contract]["symbol"]
                callback = self.internal_subscriptions[ticker.contract]["callback"]
                quote = Quote(symbol.symbol_id, QuoteType.BID_ASK, ticker.time, bid=ticker.bid, ask=ticker.ask, bidSize=ticker.bidSize, askSize=ticker.askSize)
                callback(quote)
            else:
                self.logger.error(f"Received tickers for untracked contract: {ticker.contract}")


    async def subscribe_realtime_data_async(self, symbol: Symbol, callback: Callable[[Quote], None], genericTickList: str="", snapshot: bool=False, regulatorySnapshot: bool=False):
        
        if symbol.uniqueId in self.external_subscriptions:
            self.logger.error(f"Request Ignored - Already subscribed to realtime data for {symbol.symbol_id}")
            return
        
        #convert symbol to contract
        contract = self._symbol_to_contract(symbol)
        
        #qualify contact
        qualified_contract = await self._qualify_contract_async(contract)

        #add to subscription dictionaries
        self.external_subscriptions[symbol.uniqueId] = dict(symbol=symbol, qualified_contract=qualified_contract)
        self.internal_subscriptions[qualified_contract] = dict(symbol=symbol, callback=callback)

        #subscribe to market data
        self.ib.reqMktData(qualified_contract, genericTickList, snapshot, regulatorySnapshot)
        

    def unsubscribe_realtime_data(self, symbol: Symbol):
        
        if symbol.uniqueId not in self.external_subscriptions:
            self.logger.error(f"Request Ignored - Not subscribed to realtime data for {symbol.symbol_id}")
            return
        
        #get qualified contract
        qualified_contract = self.external_subscriptions[symbol.uniqueId]["qualified_contract"]

        #unsubscribe from market data
        result = self.ib.cancelMktData(qualified_contract)

        #remove from subscription dictionaries
        del self.external_subscriptions[symbol.uniqueId]
        del self.internal_subscriptions[qualified_contract]

        if result == True:
            self.logger.info(f"Successfully unsubscribed from realtime data for {symbol.symbol_id}")
        else:
            self.logger.error(f"Failed to unsubscribe from realtime data for {symbol.symbol_id}")
       
    
    def get_matching_symbols(self, symbol_string: str) -> pl.DataFrame:
        try:
            matches = self.ib.reqMatchingSymbols(symbol_string)
            matchContracts = [m.contract for m in matches]
            return pl.DataFrame(matchContracts)
        except Exception as e:
            self.logger.error(f"Could not get matching symbols: {e}")
            return pl.DataFrame()
        
    
    def get_ticker(self, symbol: Symbol) -> pl.DataFrame:
        try:
            contract = self._symbol_to_contract(symbol)
            tickers = self.ib.reqTickers(contract)
            return pl.DataFrame(tickers)
        except Exception as e:
            self.logger.error(f"Could not get ticker: {e}")
            return pl.DataFrame()
    

    async def update_symbol_async(self, symbol: Symbol) -> Symbol:
        try:
            #convert symbol to contract
            contract = self._symbol_to_contract(symbol)

            #qualify contract
            qualified_contract = await self._qualify_contract_async(contract)

            #get contract details
            contract_details = await self._get_contract_details_async(qualified_contract)

            #extract details
            contract_details = contract_details[0]
            contract = contract_details.contract
            trading_hours = contract_details.tradingSessions()[0]
            liquid_hours = contract_details.liquidSessions()[0]
            
            contract_details_dict = contract_details.__dict__
            contract_details_dict["tradingHoursFrom"] = trading_hours.start.strftime("%H:%M:%S%z")
            contract_details_dict["tradingHoursTo"] = trading_hours.end.strftime("%H:%M:%S%z")
            contract_details_dict["liquidHoursFrom"] = liquid_hours.start.strftime("%H:%M:%S%z")
            contract_details_dict["liquidHoursTo"] = liquid_hours.end.strftime("%H:%M:%S%z")
            del contract_details_dict["contract"]
            del contract_details_dict["tradingHours"]
            del contract_details_dict["liquidHours"]

            #convert contract to symbol
            symbol = self._contract_to_symbol(contract)

            #add contract details to symbol
            symbol.contractDetails = contract_details_dict

            return symbol
        except Exception as e:
            self.logger.error(f"Could not update symbol: {e}")
            raise


    async def _qualify_contract_async(self, contract: Contract) -> Contract:
        try:
        
            qualified_contracts = await self.ib.qualifyContractsAsync(contract)

            if len(qualified_contracts) != 1 or qualified_contracts[0] is None:
                self.logger.error(f"Failed to qualify contract for {contract}")
                raise
            
            qualified_contract = qualified_contracts[0]
            return qualified_contract
        except Exception as e:
            self.logger.error(f"Failed to qualify contract for {contract}: {e}")
            raise


    async def _get_contract_details_async(self, contract: Contract) -> list[ContractDetails]:
        try:
            #request contract details
            details = await self.ib.reqContractDetailsAsync(contract)            
            return details
        
        except Exception as e:
            self.logger.error(f"Could not get contract details: {e}")
            raise

        
    def _symbol_to_contract(self, symbol: Symbol) -> Contract:
        """
        Convert a Symbol object to an IB Contract object.
        Args:
            symbol (Symbol): The symbol to convert
        Returns:
            Contract: The corresponding IB Contract object
        """
        try:
            contract = Contract(
                secType=symbol.secType,
                conId=symbol.conId,
                symbol=symbol.symbol,
                lastTradeDateOrContractMonth=symbol.lastTradeDateOrContractMonth,
                strike=symbol.strike,
                right=symbol.right,
                multiplier=symbol.multiplier,
                exchange=symbol.exchange,
                primaryExchange=symbol.primaryExchange,
                currency=symbol.currency,
                localSymbol=symbol.localSymbol,
                tradingClass=symbol.tradingClass,
                includeExpired=symbol.includeExpired,
                secIdType=symbol.secIdType,
                secId=symbol.secId,
                description=symbol.description,
                issuerId=symbol.issuerId)
            
            #set exchange to IDEALPRO for FX
            if contract.secType == "CASH":
                contract.exchange = "IDEALPRO"
            
            return contract
        except Exception as e:
            self.logger.error(f"Failed to convert symbol to contract: {e}")
            raise
        

    def _contract_to_symbol(self, contract: Contract) -> Symbol:
        """
        Convert an IB Contract object to a Symbol object. (symbol_id is generated by Symbol class)
        Args:
            contract (Contract): The contract to convert
        Returns:
            Symbol: The corresponding Symbol object
        """
        try:
            symbol = Symbol(**contract.__dict__)
            
            return symbol
        except Exception as e:
            self.logger.error(f"Failed to convert contract to symbol: {e}")
            raise


    def _get_ideal_duration_str(self, start_datetime: datetime, end_datetime: datetime, barSize: str) -> str:
        """
        Calculates the ideal durationStr for IBAPI's reqHistoricalData based on
        the provided start and end datetimes and barSize.

        Args:
            start_datetime (datetime): The start of the historical data period.
            end_datetime (datetime): The end of the historical data period.
            barSize (str): The bar size (e.g., "1 day", "5 mins", "1 secs").

        Returns:
            str: The ideal duration string (e.g., "1 Y", "30 D", "2000 S").

        Raises:
            ValueError: If the barSize is not supported or if the duration cannot
                        be represented within the API's limits for a single request.
        """

        # Define the duration limits based on the provided table
        # 'None' indicates "Not Supported" for that unit and bar size.
        # Note: Assuming "1W" and "1M" are valid barSize strings as per the table.
        DURATION_LIMITS = {
            "1 secs": {"S": 2000, "D": None, "W": None, "M": None, "Y": None},
            "5 secs": {"S": 86400, "D": 365, "W": 52, "M": 12, "Y": 68},
            "10 secs": {"S": 86400, "D": 365, "W": 52, "M": 12, "Y": 68},
            "15 secs": {"S": 86400, "D": 365, "W": 52, "M": 12, "Y": 68},
            "30 secs": {"S": 86400, "D": 365, "W": 52, "M": 12, "Y": 68},
            "1 min": {"S": 86400, "D": 365, "W": 52, "M": 12, "Y": 68},
            "2 mins": {"S": 86400, "D": 365, "W": 52, "M": 12, "Y": 68},
            "3 mins": {"S": 86400, "D": 365, "W": 52, "M": 12, "Y": 68},
            "5 mins": {"S": 86400, "D": 365, "W": 52, "M": 12, "Y": 68},
            "10 mins": {"S": 86400, "D": 365, "W": 52, "M": 12, "Y": 68},
            "15 mins": {"S": 86400, "D": 365, "W": 52, "M": 12, "Y": 68},
            "20 mins": {"S": 86400, "D": 365, "W": 52, "M": 12, "Y": 68},
            "30 mins": {"S": 86400, "D": 365, "W": 52, "M": 12, "Y": 68},
            "1 hour": {"S": 86400, "D": 365, "W": 52, "M": 12, "Y": 68},
            "2 hours": {"S": 86400, "D": 365, "W": 52, "M": 12, "Y": 68},
            "3 hours": {"S": 86400, "D": 365, "W": 52, "M": 12, "Y": 68},
            "4 hours": {"S": 86400, "D": 365, "W": 52, "M": 12, "Y": 68},
            "8 hours": {"S": 86400, "D": 365, "W": 52, "M": 12, "Y": 68},
            "1 day": {"S": 86400, "D": 365, "W": 52, "M": 12, "Y": 68},
            "1W": {"S": 86400, "D": 365, "W": 52, "M": 12, "Y": 68},
            "1M": {"S": 86400, "D": 365, "W": 52, "M": 12, "Y": 68},
        }

        # Define approximate minimum duration (in seconds) that warrants a given unit
        # This prevents short durations from being rounded up to larger units (e.g., 30 mins becoming "1 Y")
        UNIT_MIN_THRESHOLDS_SECS = {
            'Y': 365 * 24 * 3600,  # Roughly 1 year
            'M': 28 * 24 * 3600,   # Roughly 1 month (shortest month's duration)
            'W': 7 * 24 * 3600,    # Exactly 1 week
            'D': 24 * 3600,        # Exactly 1 day
            'S': 1,                # Exactly 1 second
        }

        # Validate barSize
        if barSize not in DURATION_LIMITS:
            raise ValueError(
                f"Unsupported barSize: '{barSize}'. "
                f"Please choose from {list(DURATION_LIMITS.keys())}"
            )

        limits = DURATION_LIMITS[barSize]
        delta = end_datetime - start_datetime

        # Handle zero or negative duration
        if delta.total_seconds() <= 0:
            # A minimum duration is usually required for a valid request.
            # "1 S" is the smallest possible valid duration string.
            if limits.get('S') is not None and limits['S'] >= 1:
                return "1 S"
            else:
                raise ValueError(f"Duration is zero or negative. BarSize '{barSize}' does not support '1 S'.")

        # Calculate needed duration in various units, always rounding up
        # to ensure the entire period is covered.
        total_duration_secs = delta.total_seconds()

        needed_values = {}

        # Calculate and store needed values for units only if the total duration
        # meets a reasonable minimum threshold for that unit.
        if total_duration_secs >= UNIT_MIN_THRESHOLDS_SECS['Y']:
            needed_values['Y'] = math.ceil(total_duration_secs / (365.25 * 24 * 3600))
        if total_duration_secs >= UNIT_MIN_THRESHOLDS_SECS['M']:
            needed_values['M'] = math.ceil(total_duration_secs / (30.44 * 24 * 3600))
        if total_duration_secs >= UNIT_MIN_THRESHOLDS_SECS['W']:
            needed_values['W'] = math.ceil(total_duration_secs / (7 * 24 * 3600))
        if total_duration_secs >= UNIT_MIN_THRESHOLDS_SECS['D']:
            needed_values['D'] = math.ceil(total_duration_secs / (24 * 3600))
        if total_duration_secs >= UNIT_MIN_THRESHOLDS_SECS['S']:
            needed_values['S'] = math.ceil(total_duration_secs)

        # Prioritize larger units if they fit within the limits.
        # The order of checks is crucial: Years -> Months -> Weeks -> Days -> Seconds.
        for unit_char, unit_key in [('Y', 'Y'), ('M', 'M'), ('W', 'W'), ('D', 'D'), ('S', 'S')]:
            if unit_key in needed_values and limits.get(unit_key) is not None:
                needed_val = needed_values[unit_key]
                max_limit = limits[unit_key]

                if needed_val <= max_limit:
                    return f"{int(needed_val)} {unit_char}"
                else:
                    # If needed_val exceeds the limit, return the the maximum allowed
                    # for this unit, as this is the best a single request can do.
                    return f"{int(max_limit)} {unit_char}"

        # If nothing fit (e.g., duration is extremely short and doesn't meet even 'S' threshold
        # or no units are supported for the given barSize, which is unlikely with the table).
        raise ValueError(
            f"Could not determine a suitable durationStr for barSize '{barSize}' "
            f"and duration {delta}. It might be too short or too long for a single request "
            f"with this barSize, or the barSize has no supported duration units."
        )