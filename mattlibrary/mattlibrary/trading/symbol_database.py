"""A class for managing a collection of financial instrument symbols."""

import os
import logging
import polars as pl
from mattlibrary.trading.symbol import Symbol
from mattlibrary.datamanagement.interactive_brokers import InteractiveBrokersAPI


class SymbolDatabase:
    """
    A class for managing a collection of financial instrument symbols.
    
    This class provides methods for adding, removing, and retrieving symbols.
    Symbols are stored in a dictionary for efficient lookup.
    
    Attributes:
        symbol_dictionary (dict): Dictionary containing symbol objects indexed by symbol_id
    """

    def __init__(self, directory: str):
        
        self.logger = logging.getLogger(__name__)
        self.path_filename = os.path.join(directory, "symbol_database.parquet")
        
        #attempt to load data from parquet file
        self.symbol_dictionary = self._load_symbols_from_parquet()


    def _load_symbols_from_parquet(self) -> dict[str, Symbol]:
        
        dictionary = dict()
        
        try:
            symbol_df = pl.read_parquet(self.path_filename)
            rows = symbol_df.to_dicts()
            symbols = [Symbol(**row) for row in rows]

            for symbol in symbols:
                dictionary[symbol.symbol_id] = symbol

            self.logger.info(f"Loaded {len(dictionary)} symbols from {self.path_filename}")
        except Exception as e:
            self.logger.error(f"Could not load symbols from {self.path_filename}: {e}")

        return dictionary


    def _store_symbols_in_parquet(self):
        #convert dictionary to list of dictionaries
        data = [symbol.__dict__ for symbol in self.symbol_dictionary.values()]
        #create dataframe that contains all symbols (each attribute in a column)
        df = pl.from_dicts(data)
        df.write_parquet(self.path_filename)


    def get_symbol_dictionary(self) -> dict[str, Symbol]:
        return self.symbol_dictionary
        

    async def add_update_symbols_async(self, symbols:list[Symbol], validate_symbols:bool):
        
        try:
            #connect to IB
            if validate_symbols:
                ib = InteractiveBrokersAPI("127.0.0.1", 7497, 1, True)
                await ib.connect_async()

            #add/update symbols to dictionary
            for symbol in symbols:
                if validate_symbols:
                    symbol = await ib.update_symbol_async(symbol)
                self.symbol_dictionary[symbol.symbol_id] = symbol

            #write to parquet file
            self._store_symbols_in_parquet()
            self.logger.info(f"Added/updated {len(symbols)} symbols to symbol database - total symbols: {len(self.symbol_dictionary)}")

        except Exception as e:
            self.logger.error(f"Could not add/update symbols: {e}")
        finally:
            #disconnect from IB
            if validate_symbols:
                ib.disconnect()

    
    def remove_symbols(self, symbols:list[Symbol]):

        #remove symbols from dictionary (check whether symbol exists first)
        for symbol in symbols:
            if symbol.symbol_id in self.symbol_dictionary:
                del self.symbol_dictionary[symbol.symbol_id]
        
        #write to parquet file
        self._store_symbols_in_parquet()
        self.logger.info(f"Removed {len(symbols)} symbols from symbol database - remaining symbols: {len(self.symbol_dictionary)}")

    
