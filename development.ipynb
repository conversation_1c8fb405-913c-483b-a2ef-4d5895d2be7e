{"cells": [{"cell_type": "code", "execution_count": null, "id": "9c2da4de", "metadata": {}, "outputs": [], "source": ["%reload_ext autoreload\n", "%autoreload 2\n", "\n", "import os\n", "import sys\n", "import polars as pl\n", "from datetime import datetime, timedelta\n", "from zoneinfo import ZoneInfo\n", "\n", "#config for jupyter cell output\n", "# import polars as pl\n", "# pl.Config.set_tbl_rows(20)\n", "\n", "from mattlibrary.logging.logging_config import setup_logging\n", "from mattlibrary.trading.trading_engine import TradingEngine\n", "from mattlibrary.datamanagement.interactive_brokers import InteractiveBrokersAPI\n", "from mattlibrary.trading.symbol import Symbol, Forex, Stock\n", "from mattlibrary.trading.parent_orders.parent_order_standard import ParentOrderStandard\n", "from mattlibrary.trading.child_order_type import ChildOrderType\n", "from mattlibrary.trading.symbol_database import SymbolDatabase\n", "from mattlibrary.trading.fx_converter import Fx_Converter\n", "\n", "#logging config\n", "setup_logging(True, \"INFO\", True, True, log_file=\"development.log\", clean_log_file=True)"]}, {"cell_type": "code", "execution_count": 9, "id": "2d466299", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO - 2025-08-24 23:07:01,551 - mattlibrary.trading.fx_converter - load_fx_data:69 - Loaded fx conversion data (4559 rows) from /home/<USER>/development/python_development/fx_converter_data.parquet\n"]}], "source": ["fx_converter = Fx_Converter(\"USD\", \"/home/<USER>/development/python_development/\")"]}, {"cell_type": "code", "execution_count": null, "id": "ae3f394e", "metadata": {}, "outputs": [], "source": ["# await fx_converter.reset_data_source_async(\"127.0.0.1\", 7497, 1, True)\n"]}, {"cell_type": "code", "execution_count": 16, "id": "818eb518", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'AUDUSD': {datetime.date(2007, 11, 2): 0.9232,\n", "  datetime.date(2007, 11, 9): 0.911575,\n", "  datetime.date(2007, 11, 29): 0.88135,\n", "  datetime.date(2007, 11, 30): 0.88435,\n", "  datetime.date(2007, 12, 3): 0.8807,\n", "  datetime.date(2007, 12, 4): 0.87375,\n", "  datetime.date(2007, 12, 5): 0.87005,\n", "  datetime.date(2007, 12, 6): 0.8789,\n", "  datetime.date(2007, 12, 7): 0.8761,\n", "  datetime.date(2007, 12, 10): 0.8848,\n", "  datetime.date(2007, 12, 11): 0.8734,\n", "  datetime.date(2007, 12, 12): 0.8829,\n", "  datetime.date(2007, 12, 13): 0.876,\n", "  datetime.date(2007, 12, 14): 0.86065,\n", "  datetime.date(2007, 12, 17): 0.8573,\n", "  datetime.date(2007, 12, 18): 0.8603,\n", "  datetime.date(2007, 12, 19): 0.859,\n", "  datetime.date(2007, 12, 20): 0.8591,\n", "  datetime.date(2007, 12, 21): 0.86735,\n", "  datetime.date(2007, 12, 24): 0.87105,\n", "  datetime.date(2007, 12, 26): 0.874,\n", "  datetime.date(2007, 12, 27): 0.8779,\n", "  datetime.date(2007, 12, 28): 0.87625,\n", "  datetime.date(2007, 12, 31): 0.8751,\n", "  datetime.date(2008, 1, 2): 0.8835,\n", "  datetime.date(2008, 1, 3): 0.880175,\n", "  datetime.date(2008, 1, 4): 0.87215,\n", "  datetime.date(2008, 1, 7): 0.872775,\n", "  datetime.date(2008, 1, 8): 0.8786,\n", "  datetime.date(2008, 1, 9): 0.8831,\n", "  datetime.date(2008, 1, 10): 0.8954,\n", "  datetime.date(2008, 1, 11): 0.8909,\n", "  datetime.date(2008, 1, 14): 0.89965,\n", "  datetime.date(2008, 1, 15): 0.88085,\n", "  datetime.date(2008, 1, 16): 0.8797,\n", "  datetime.date(2008, 1, 17): 0.87665,\n", "  datetime.date(2008, 1, 18): 0.879025,\n", "  datetime.date(2008, 1, 21): 0.8614,\n", "  datetime.date(2008, 1, 22): 0.8684,\n", "  datetime.date(2008, 1, 23): 0.87315,\n", "  datetime.date(2008, 1, 24): 0.8821,\n", "  datetime.date(2008, 1, 25): 0.8796,\n", "  datetime.date(2008, 1, 28): 0.88915,\n", "  datetime.date(2008, 1, 29): 0.88925,\n", "  datetime.date(2008, 1, 30): 0.8931,\n", "  datetime.date(2008, 1, 31): 0.895625,\n", "  datetime.date(2008, 2, 1): 0.90425,\n", "  datetime.date(2008, 2, 4): 0.908075,\n", "  datetime.date(2008, 2, 5): 0.8973,\n", "  datetime.date(2008, 2, 6): 0.895825,\n", "  datetime.date(2008, 2, 7): 0.8947,\n", "  datetime.date(2008, 2, 8): 0.8956,\n", "  datetime.date(2008, 2, 11): 0.9036,\n", "  datetime.date(2008, 2, 12): 0.90305,\n", "  datetime.date(2008, 2, 13): 0.896325,\n", "  datetime.date(2008, 2, 14): 0.902125,\n", "  datetime.date(2008, 2, 15): 0.90895,\n", "  datetime.date(2008, 2, 18): 0.91365,\n", "  datetime.date(2008, 2, 19): 0.919,\n", "  datetime.date(2008, 2, 20): 0.9183,\n", "  datetime.date(2008, 2, 21): 0.91875,\n", "  datetime.date(2008, 2, 22): 0.92385,\n", "  datetime.date(2008, 2, 25): 0.927,\n", "  datetime.date(2008, 2, 26): 0.933825,\n", "  datetime.date(2008, 2, 27): 0.94165,\n", "  datetime.date(2008, 2, 28): 0.94895,\n", "  datetime.date(2008, 2, 29): 0.93085,\n", "  datetime.date(2008, 3, 3): 0.9396,\n", "  datetime.date(2008, 3, 4): 0.9276,\n", "  datetime.date(2008, 3, 5): 0.93565,\n", "  datetime.date(2008, 3, 6): 0.9266,\n", "  datetime.date(2008, 3, 7): 0.926625,\n", "  datetime.date(2008, 3, 10): 0.916475,\n", "  datetime.date(2008, 3, 11): 0.9287,\n", "  datetime.date(2008, 3, 12): 0.93335,\n", "  datetime.date(2008, 3, 13): 0.947,\n", "  datetime.date(2008, 3, 14): 0.93735,\n", "  datetime.date(2008, 3, 17): 0.92165,\n", "  datetime.date(2008, 3, 18): 0.92675,\n", "  datetime.date(2008, 3, 19): 0.91415,\n", "  datetime.date(2008, 3, 20): 0.8997,\n", "  datetime.date(2008, 3, 21): 0.90185,\n", "  datetime.date(2008, 3, 24): 0.90585,\n", "  datetime.date(2008, 3, 25): 0.9177,\n", "  datetime.date(2008, 3, 26): 0.920675,\n", "  datetime.date(2008, 3, 27): 0.9187,\n", "  datetime.date(2008, 3, 28): 0.91745,\n", "  datetime.date(2008, 3, 31): 0.913075,\n", "  datetime.date(2008, 4, 1): 0.90725,\n", "  datetime.date(2008, 4, 2): 0.91425,\n", "  datetime.date(2008, 4, 3): 0.9162,\n", "  datetime.date(2008, 4, 4): 0.92285,\n", "  datetime.date(2008, 4, 7): 0.925775,\n", "  datetime.date(2008, 4, 8): 0.931875,\n", "  datetime.date(2008, 4, 9): 0.928475,\n", "  datetime.date(2008, 4, 10): 0.93205,\n", "  datetime.date(2008, 4, 11): 0.92785,\n", "  datetime.date(2008, 4, 14): 0.92685,\n", "  datetime.date(2008, 4, 15): 0.926575,\n", "  datetime.date(2008, 4, 16): 0.9397,\n", "  datetime.date(2008, 4, 17): 0.93745,\n", "  datetime.date(2008, 4, 18): 0.9348,\n", "  datetime.date(2008, 4, 21): 0.94285,\n", "  datetime.date(2008, 4, 22): 0.9447,\n", "  datetime.date(2008, 4, 23): 0.9493,\n", "  datetime.date(2008, 4, 24): 0.939675,\n", "  datetime.date(2008, 4, 25): 0.9334,\n", "  datetime.date(2008, 4, 28): 0.9388,\n", "  datetime.date(2008, 4, 29): 0.93385,\n", "  datetime.date(2008, 4, 30): 0.943575,\n", "  datetime.date(2008, 5, 1): 0.933475,\n", "  datetime.date(2008, 5, 2): 0.934675,\n", "  datetime.date(2008, 5, 5): 0.946775,\n", "  datetime.date(2008, 5, 6): 0.949675,\n", "  datetime.date(2008, 5, 7): 0.9421,\n", "  datetime.date(2008, 5, 8): 0.94365,\n", "  datetime.date(2008, 5, 9): 0.9436,\n", "  datetime.date(2008, 5, 12): 0.947775,\n", "  datetime.date(2008, 5, 13): 0.9404,\n", "  datetime.date(2008, 5, 14): 0.934275,\n", "  datetime.date(2008, 5, 15): 0.94045,\n", "  datetime.date(2008, 5, 16): 0.955725,\n", "  datetime.date(2008, 5, 19): 0.95345,\n", "  datetime.date(2008, 5, 20): 0.95865,\n", "  datetime.date(2008, 5, 21): 0.96245,\n", "  datetime.date(2008, 5, 22): 0.955725,\n", "  datetime.date(2008, 5, 23): 0.959175,\n", "  datetime.date(2008, 5, 26): 0.96085,\n", "  datetime.date(2008, 5, 27): 0.958525,\n", "  datetime.date(2008, 5, 28): 0.962875,\n", "  datetime.date(2008, 5, 29): 0.955475,\n", "  datetime.date(2008, 5, 30): 0.9559,\n", "  datetime.date(2008, 6, 2): 0.95525,\n", "  datetime.date(2008, 6, 3): 0.952475,\n", "  datetime.date(2008, 6, 4): 0.95755,\n", "  datetime.date(2008, 6, 5): 0.958675,\n", "  datetime.date(2008, 6, 6): 0.96265,\n", "  datetime.date(2008, 6, 9): 0.950375,\n", "  datetime.date(2008, 6, 10): 0.94605,\n", "  datetime.date(2008, 6, 11): 0.947,\n", "  datetime.date(2008, 6, 12): 0.934275,\n", "  datetime.date(2008, 6, 13): 0.93885,\n", "  datetime.date(2008, 6, 16): 0.940375,\n", "  datetime.date(2008, 6, 17): 0.943825,\n", "  datetime.date(2008, 6, 18): 0.9471,\n", "  datetime.date(2008, 6, 19): 0.95115,\n", "  datetime.date(2008, 6, 20): 0.95335,\n", "  datetime.date(2008, 6, 23): 0.95255,\n", "  datetime.date(2008, 6, 24): 0.95535,\n", "  datetime.date(2008, 6, 25): 0.95995,\n", "  datetime.date(2008, 6, 26): 0.955825,\n", "  datetime.date(2008, 6, 27): 0.9609,\n", "  datetime.date(2008, 6, 30): 0.95855,\n", "  datetime.date(2008, 7, 1): 0.955,\n", "  datetime.date(2008, 7, 2): 0.961875,\n", "  datetime.date(2008, 7, 3): 0.96025,\n", "  datetime.date(2008, 7, 4): 0.9634,\n", "  datetime.date(2008, 7, 7): 0.95655,\n", "  datetime.date(2008, 7, 8): 0.953475,\n", "  datetime.date(2008, 7, 9): 0.956925,\n", "  datetime.date(2008, 7, 10): 0.961975,\n", "  datetime.date(2008, 7, 11): 0.9662,\n", "  datetime.date(2008, 7, 14): 0.971725,\n", "  datetime.date(2008, 7, 15): 0.979275,\n", "  datetime.date(2008, 7, 16): 0.974675,\n", "  datetime.date(2008, 7, 17): 0.9723,\n", "  datetime.date(2008, 7, 18): 0.9704,\n", "  datetime.date(2008, 7, 21): 0.9772,\n", "  datetime.date(2008, 7, 22): 0.971275,\n", "  datetime.date(2008, 7, 23): 0.9619,\n", "  datetime.date(2008, 7, 24): 0.95845,\n", "  datetime.date(2008, 7, 25): 0.9563,\n", "  datetime.date(2008, 7, 28): 0.9568,\n", "  datetime.date(2008, 7, 29): 0.952825,\n", "  datetime.date(2008, 7, 30): 0.9443,\n", "  datetime.date(2008, 7, 31): 0.9423,\n", "  datetime.date(2008, 8, 1): 0.92915,\n", "  datetime.date(2008, 8, 4): 0.9292,\n", "  datetime.date(2008, 8, 5): 0.916175,\n", "  datetime.date(2008, 8, 6): 0.9083,\n", "  datetime.date(2008, 8, 7): 0.906625,\n", "  datetime.date(2008, 8, 8): 0.888675,\n", "  datetime.date(2008, 8, 11): 0.883975,\n", "  datetime.date(2008, 8, 12): 0.87345,\n", "  datetime.date(2008, 8, 13): 0.87435,\n", "  datetime.date(2008, 8, 14): 0.8722,\n", "  datetime.date(2008, 8, 15): 0.8662,\n", "  datetime.date(2008, 8, 18): 0.8682,\n", "  datetime.date(2008, 8, 19): 0.8718,\n", "  datetime.date(2008, 8, 20): 0.873625,\n", "  datetime.date(2008, 8, 21): 0.88075,\n", "  datetime.date(2008, 8, 22): 0.86645,\n", "  datetime.date(2008, 8, 25): 0.862775,\n", "  datetime.date(2008, 8, 26): 0.85585,\n", "  datetime.date(2008, 8, 27): 0.8588,\n", "  datetime.date(2008, 8, 28): 0.862525,\n", "  datetime.date(2008, 8, 29): 0.85805,\n", "  datetime.date(2008, 9, 1): 0.850375,\n", "  datetime.date(2008, 9, 2): 0.838,\n", "  datetime.date(2008, 9, 3): 0.83595,\n", "  datetime.date(2008, 9, 4): 0.822775,\n", "  datetime.date(2008, 9, 5): 0.815825,\n", "  datetime.date(2008, 9, 8): 0.816275,\n", "  datetime.date(2008, 9, 9): 0.8024,\n", "  datetime.date(2008, 9, 10): 0.8014,\n", "  datetime.date(2008, 9, 11): 0.80725,\n", "  datetime.date(2008, 9, 12): 0.8237,\n", "  datetime.date(2008, 9, 15): 0.80665,\n", "  datetime.date(2008, 9, 16): 0.8003,\n", "  datetime.date(2008, 9, 17): 0.79075,\n", "  datetime.date(2008, 9, 18): 0.804975,\n", "  datetime.date(2008, 9, 19): 0.8341,\n", "  datetime.date(2008, 9, 22): 0.84415,\n", "  datetime.date(2008, 9, 23): 0.83295,\n", "  datetime.date(2008, 9, 24): 0.83425,\n", "  datetime.date(2008, 9, 25): 0.8353,\n", "  datetime.date(2008, 9, 26): 0.8311,\n", "  datetime.date(2008, 9, 29): 0.80475,\n", "  datetime.date(2008, 9, 30): 0.79255,\n", "  datetime.date(2008, 10, 1): 0.78725,\n", "  datetime.date(2008, 10, 2): 0.772525,\n", "  datetime.date(2008, 10, 3): 0.774,\n", "  datetime.date(2008, 10, 6): 0.72225,\n", "  datetime.date(2008, 10, 7): 0.7056,\n", "  datetime.date(2008, 10, 8): 0.664325,\n", "  datetime.date(2008, 10, 9): 0.684,\n", "  datetime.date(2008, 10, 10): 0.6447,\n", "  datetime.date(2008, 10, 13): 0.6987,\n", "  datetime.date(2008, 10, 14): 0.697,\n", "  datetime.date(2008, 10, 15): 0.6617,\n", "  datetime.date(2008, 10, 16): 0.690775,\n", "  datetime.date(2008, 10, 17): 0.6893,\n", "  datetime.date(2008, 10, 20): 0.70445,\n", "  datetime.date(2008, 10, 21): 0.670975,\n", "  datetime.date(2008, 10, 22): 0.673,\n", "  datetime.date(2008, 10, 23): 0.669675,\n", "  datetime.date(2008, 10, 24): 0.6225,\n", "  datetime.date(2008, 10, 27): 0.601225,\n", "  datetime.date(2008, 10, 28): 0.6413,\n", "  datetime.date(2008, 10, 29): 0.668,\n", "  datetime.date(2008, 10, 30): 0.68225,\n", "  datetime.date(2008, 10, 31): 0.667725,\n", "  datetime.date(2008, 11, 3): 0.6764,\n", "  datetime.date(2008, 11, 4): 0.698775,\n", "  datetime.date(2008, 11, 5): 0.68135,\n", "  datetime.date(2008, 11, 6): 0.665575,\n", "  datetime.date(2008, 11, 7): 0.674225,\n", "  datetime.date(2008, 11, 10): 0.66975,\n", "  datetime.date(2008, 11, 11): 0.65715,\n", "  datetime.date(2008, 11, 12): 0.6404,\n", "  datetime.date(2008, 11, 13): 0.666175,\n", "  datetime.date(2008, 11, 14): 0.647825,\n", "  datetime.date(2008, 11, 17): 0.64885,\n", "  datetime.date(2008, 11, 18): 0.6525,\n", "  datetime.date(2008, 11, 19): 0.63655,\n", "  datetime.date(2008, 11, 20): 0.61115,\n", "  datetime.date(2008, 11, 21): 0.63235,\n", "  datetime.date(2008, 11, 24): 0.65395,\n", "  datetime.date(2008, 11, 25): 0.649225,\n", "  datetime.date(2008, 11, 26): 0.6519,\n", "  datetime.date(2008, 11, 27): 0.656825,\n", "  datetime.date(2008, 11, 28): 0.654825,\n", "  datetime.date(2008, 12, 1): 0.64015,\n", "  datetime.date(2008, 12, 2): 0.642825,\n", "  datetime.date(2008, 12, 3): 0.64895,\n", "  datetime.date(2008, 12, 4): 0.644425,\n", "  datetime.date(2008, 12, 5): 0.646675,\n", "  datetime.date(2008, 12, 8): 0.66445,\n", "  datetime.date(2008, 12, 9): 0.65875,\n", "  datetime.date(2008, 12, 10): 0.656175,\n", "  datetime.date(2008, 12, 11): 0.6718,\n", "  datetime.date(2008, 12, 12): 0.66435,\n", "  datetime.date(2008, 12, 15): 0.668675,\n", "  datetime.date(2008, 12, 16): 0.69425,\n", "  datetime.date(2008, 12, 17): 0.7039,\n", "  datetime.date(2008, 12, 18): 0.686875,\n", "  datetime.date(2008, 12, 19): 0.681075,\n", "  datetime.date(2008, 12, 22): 0.68395,\n", "  datetime.date(2008, 12, 23): 0.680325,\n", "  datetime.date(2008, 12, 24): 0.6816,\n", "  datetime.date(2008, 12, 26): 0.68535,\n", "  datetime.date(2008, 12, 29): 0.685375,\n", "  datetime.date(2008, 12, 30): 0.69155,\n", "  datetime.date(2008, 12, 31): 0.703275,\n", "  datetime.date(2009, 1, 2): 0.711325,\n", "  datetime.date(2009, 1, 5): 0.71745,\n", "  datetime.date(2009, 1, 6): 0.7233,\n", "  datetime.date(2009, 1, 7): 0.712675,\n", "  datetime.date(2009, 1, 8): 0.71225,\n", "  datetime.date(2009, 1, 9): 0.70325,\n", "  datetime.date(2009, 1, 12): 0.6817,\n", "  datetime.date(2009, 1, 13): 0.664625,\n", "  datetime.date(2009, 1, 14): 0.661225,\n", "  datetime.date(2009, 1, 15): 0.66285,\n", "  datetime.date(2009, 1, 16): 0.6737,\n", "  datetime.date(2009, 1, 19): 0.66695,\n", "  datetime.date(2009, 1, 20): 0.6508,\n", "  datetime.date(2009, 1, 21): 0.66175,\n", "  datetime.date(2009, 1, 22): 0.65535,\n", "  datetime.date(2009, 1, 23): 0.653325,\n", "  datetime.date(2009, 1, 26): 0.65965,\n", "  datetime.date(2009, 1, 27): 0.66115,\n", "  datetime.date(2009, 1, 28): 0.666075,\n", "  datetime.date(2009, 1, 29): 0.6522,\n", "  datetime.date(2009, 1, 30): 0.635575,\n", "  datetime.date(2009, 2, 2): 0.631525,\n", "  datetime.date(2009, 2, 3): 0.65105,\n", "  datetime.date(2009, 2, 4): 0.6435,\n", "  datetime.date(2009, 2, 5): 0.65245,\n", "  datetime.date(2009, 2, 6): 0.67565,\n", "  datetime.date(2009, 2, 9): 0.67855,\n", "  datetime.date(2009, 2, 10): 0.65485,\n", "  datetime.date(2009, 2, 11): 0.656375,\n", "  datetime.date(2009, 2, 12): 0.6524,\n", "  datetime.date(2009, 2, 13): 0.65675,\n", "  datetime.date(2009, 2, 16): 0.6504,\n", "  datetime.date(2009, 2, 17): 0.634875,\n", "  datetime.date(2009, 2, 18): 0.637075,\n", "  datetime.date(2009, 2, 19): 0.64365,\n", "  datetime.date(2009, 2, 20): 0.645625,\n", "  datetime.date(2009, 2, 23): 0.6417,\n", "  datetime.date(2009, 2, 24): 0.6511,\n", "  datetime.date(2009, 2, 25): 0.64695,\n", "  datetime.date(2009, 2, 26): 0.647525,\n", "  datetime.date(2009, 2, 27): 0.6392,\n", "  datetime.date(2009, 3, 2): 0.63,\n", "  datetime.date(2009, 3, 3): 0.6378,\n", "  datetime.date(2009, 3, 4): 0.6496,\n", "  datetime.date(2009, 3, 5): 0.638225,\n", "  datetime.date(2009, 3, 6): 0.6404,\n", "  datetime.date(2009, 3, 9): 0.63175,\n", "  datetime.date(2009, 3, 10): 0.645925,\n", "  datetime.date(2009, 3, 11): 0.652225,\n", "  datetime.date(2009, 3, 12): 0.654675,\n", "  datetime.date(2009, 3, 13): 0.658,\n", "  datetime.date(2009, 3, 16): 0.6591,\n", "  datetime.date(2009, 3, 17): 0.66185,\n", "  datetime.date(2009, 3, 18): 0.67645,\n", "  datetime.date(2009, 3, 19): 0.68515,\n", "  datetime.date(2009, 3, 20): 0.68685,\n", "  datetime.date(2009, 3, 23): 0.705225,\n", "  datetime.date(2009, 3, 24): 0.6955,\n", "  datetime.date(2009, 3, 25): 0.69775,\n", "  datetime.date(2009, 3, 26): 0.70155,\n", "  datetime.date(2009, 3, 27): 0.69415,\n", "  datetime.date(2009, 3, 30): 0.6814,\n", "  datetime.date(2009, 3, 31): 0.69125,\n", "  datetime.date(2009, 4, 1): 0.6992,\n", "  datetime.date(2009, 4, 2): 0.7152,\n", "  datetime.date(2009, 4, 3): 0.715025,\n", "  datetime.date(2009, 4, 6): 0.713875,\n", "  datetime.date(2009, 4, 7): 0.71095,\n", "  datetime.date(2009, 4, 8): 0.710175,\n", "  datetime.date(2009, 4, 9): 0.71925,\n", "  datetime.date(2009, 4, 10): 0.718625,\n", "  datetime.date(2009, 4, 13): 0.73185,\n", "  datetime.date(2009, 4, 14): 0.7238,\n", "  datetime.date(2009, 4, 15): 0.72885,\n", "  datetime.date(2009, 4, 16): 0.720625,\n", "  datetime.date(2009, 4, 17): 0.72295,\n", "  datetime.date(2009, 4, 20): 0.69665,\n", "  datetime.date(2009, 4, 21): 0.711475,\n", "  datetime.date(2009, 4, 22): 0.7054,\n", "  datetime.date(2009, 4, 23): 0.714775,\n", "  datetime.date(2009, 4, 24): 0.723225,\n", "  datetime.date(2009, 4, 27): 0.71005,\n", "  datetime.date(2009, 4, 28): 0.70615,\n", "  datetime.date(2009, 4, 29): 0.726675,\n", "  datetime.date(2009, 4, 30): 0.72565,\n", "  datetime.date(2009, 5, 1): 0.7304,\n", "  datetime.date(2009, 5, 4): 0.74,\n", "  datetime.date(2009, 5, 5): 0.74255,\n", "  datetime.date(2009, 5, 6): 0.7483,\n", "  datetime.date(2009, 5, 7): 0.754,\n", "  datetime.date(2009, 5, 8): 0.76865,\n", "  datetime.date(2009, 5, 11): 0.7587,\n", "  datetime.date(2009, 5, 12): 0.7651,\n", "  datetime.date(2009, 5, 13): 0.753225,\n", "  datetime.date(2009, 5, 14): 0.7602,\n", "  datetime.date(2009, 5, 15): 0.7491,\n", "  datetime.date(2009, 5, 18): 0.76575,\n", "  datetime.date(2009, 5, 19): 0.7747,\n", "  datetime.date(2009, 5, 20): 0.775275,\n", "  datetime.date(2009, 5, 21): 0.77835,\n", "  datetime.date(2009, 5, 22): 0.7827,\n", "  datetime.date(2009, 5, 25): 0.78215,\n", "  datetime.date(2009, 5, 26): 0.7862,\n", "  datetime.date(2009, 5, 27): 0.77575,\n", "  datetime.date(2009, 5, 28): 0.784075,\n", "  datetime.date(2009, 5, 29): 0.801125,\n", "  datetime.date(2009, 6, 1): 0.81015,\n", "  datetime.date(2009, 6, 2): 0.82095,\n", "  datetime.date(2009, 6, 3): 0.800725,\n", "  datetime.date(2009, 6, 4): 0.80185,\n", "  datetime.date(2009, 6, 5): 0.79325,\n", "  datetime.date(2009, 6, 8): 0.7892,\n", "  datetime.date(2009, 6, 9): 0.80145,\n", "  datetime.date(2009, 6, 10): 0.80325,\n", "  datetime.date(2009, 6, 11): 0.8194,\n", "  datetime.date(2009, 6, 12): 0.8124,\n", "  datetime.date(2009, 6, 15): 0.7951,\n", "  datetime.date(2009, 6, 16): 0.7934,\n", "  datetime.date(2009, 6, 17): 0.79445,\n", "  datetime.date(2009, 6, 18): 0.798475,\n", "  datetime.date(2009, 6, 19): 0.8062,\n", "  datetime.date(2009, 6, 22): 0.78575,\n", "  datetime.date(2009, 6, 23): 0.7939,\n", "  datetime.date(2009, 6, 24): 0.796675,\n", "  datetime.date(2009, 6, 25): 0.8025,\n", "  datetime.date(2009, 6, 26): 0.80735,\n", "  datetime.date(2009, 6, 29): 0.8083,\n", "  datetime.date(2009, 6, 30): 0.8064,\n", "  datetime.date(2009, 7, 1): 0.808425,\n", "  datetime.date(2009, 7, 2): 0.79375,\n", "  datetime.date(2009, 7, 3): 0.7969,\n", "  datetime.date(2009, 7, 6): 0.7977,\n", "  datetime.date(2009, 7, 7): 0.789225,\n", "  datetime.date(2009, 7, 8): 0.7786,\n", "  datetime.date(2009, 7, 9): 0.78285,\n", "  datetime.date(2009, 7, 10): 0.77875,\n", "  datetime.date(2009, 7, 13): 0.7832,\n", "  datetime.date(2009, 7, 14): 0.7929,\n", "  datetime.date(2009, 7, 15): 0.80305,\n", "  datetime.date(2009, 7, 16): 0.80625,\n", "  datetime.date(2009, 7, 17): 0.802525,\n", "  datetime.date(2009, 7, 20): 0.81655,\n", "  datetime.date(2009, 7, 21): 0.818225,\n", "  datetime.date(2009, 7, 22): 0.815725,\n", "  datetime.date(2009, 7, 23): 0.81205,\n", "  datetime.date(2009, 7, 24): 0.8172,\n", "  datetime.date(2009, 7, 27): 0.8227,\n", "  datetime.date(2009, 7, 28): 0.82685,\n", "  datetime.date(2009, 7, 29): 0.817425,\n", "  datetime.date(2009, 7, 30): 0.8257,\n", "  datetime.date(2009, 7, 31): 0.83585,\n", "  datetime.date(2009, 8, 3): 0.842025,\n", "  datetime.date(2009, 8, 4): 0.8444,\n", "  datetime.date(2009, 8, 5): 0.840575,\n", "  datetime.date(2009, 8, 6): 0.83955,\n", "  datetime.date(2009, 8, 7): 0.8372,\n", "  datetime.date(2009, 8, 10): 0.83735,\n", "  datetime.date(2009, 8, 11): 0.82905,\n", "  datetime.date(2009, 8, 12): 0.83365,\n", "  datetime.date(2009, 8, 13): 0.842725,\n", "  datetime.date(2009, 8, 14): 0.832675,\n", "  datetime.date(2009, 8, 17): 0.820525,\n", "  datetime.date(2009, 8, 18): 0.82685,\n", "  datetime.date(2009, 8, 19): 0.828725,\n", "  datetime.date(2009, 8, 20): 0.83135,\n", "  datetime.date(2009, 8, 21): 0.834775,\n", "  datetime.date(2009, 8, 24): 0.838875,\n", "  datetime.date(2009, 8, 25): 0.83565,\n", "  datetime.date(2009, 8, 26): 0.828275,\n", "  datetime.date(2009, 8, 27): 0.83915,\n", "  datetime.date(2009, 8, 28): 0.841,\n", "  datetime.date(2009, 8, 31): 0.8439,\n", "  datetime.date(2009, 9, 1): 0.82605,\n", "  datetime.date(2009, 9, 2): 0.8337,\n", "  datetime.date(2009, 9, 3): 0.84025,\n", "  datetime.date(2009, 9, 4): 0.85065,\n", "  datetime.date(2009, 9, 7): 0.8557,\n", "  datetime.date(2009, 9, 8): 0.86155,\n", "  datetime.date(2009, 9, 9): 0.8624,\n", "  datetime.date(2009, 9, 10): 0.864,\n", "  datetime.date(2009, 9, 11): 0.86335,\n", "  datetime.date(2009, 9, 14): 0.862175,\n", "  datetime.date(2009, 9, 15): 0.8634,\n", "  datetime.date(2009, 9, 16): 0.8735,\n", "  datetime.date(2009, 9, 17): 0.87275,\n", "  datetime.date(2009, 9, 18): 0.8675,\n", "  datetime.date(2009, 9, 21): 0.863,\n", "  datetime.date(2009, 9, 22): 0.87345,\n", "  datetime.date(2009, 9, 23): 0.86975,\n", "  datetime.date(2009, 9, 24): 0.865,\n", "  datetime.date(2009, 9, 25): 0.867675,\n", "  datetime.date(2009, 9, 28): 0.8725,\n", "  datetime.date(2009, 9, 29): 0.87025,\n", "  datetime.date(2009, 9, 30): 0.88275,\n", "  datetime.date(2009, 10, 1): 0.869675,\n", "  datetime.date(2009, 10, 2): 0.86525,\n", "  datetime.date(2009, 10, 5): 0.87785,\n", "  datetime.date(2009, 10, 6): 0.89035,\n", "  datetime.date(2009, 10, 7): 0.8912,\n", "  datetime.date(2009, 10, 8): 0.906325,\n", "  datetime.date(2009, 10, 9): 0.90395,\n", "  datetime.date(2009, 10, 12): 0.9072,\n", "  datetime.date(2009, 10, 13): 0.90895,\n", "  datetime.date(2009, 10, 14): 0.91495,\n", "  datetime.date(2009, 10, 15): 0.920525,\n", "  datetime.date(2009, 10, 16): 0.91655,\n", "  datetime.date(2009, 10, 19): 0.92915,\n", "  datetime.date(2009, 10, 20): 0.92385,\n", "  datetime.date(2009, 10, 21): 0.9291,\n", "  datetime.date(2009, 10, 22): 0.92685,\n", "  datetime.date(2009, 10, 23): 0.922425,\n", "  datetime.date(2009, 10, 26): 0.916175,\n", "  datetime.date(2009, 10, 27): 0.9166,\n", "  datetime.date(2009, 10, 28): 0.897125,\n", "  datetime.date(2009, 10, 29): 0.915,\n", "  datetime.date(2009, 10, 30): 0.900225,\n", "  datetime.date(2009, 11, 2): 0.903975,\n", "  datetime.date(2009, 11, 3): 0.902575,\n", "  datetime.date(2009, 11, 4): 0.90985,\n", "  datetime.date(2009, 11, 5): 0.910225,\n", "  datetime.date(2009, 11, 6): 0.91915,\n", "  datetime.date(2009, 11, 9): 0.929675,\n", "  datetime.date(2009, 11, 10): 0.93045,\n", "  datetime.date(2009, 11, 11): 0.92985,\n", "  datetime.date(2009, 11, 12): 0.923575,\n", "  datetime.date(2009, 11, 13): 0.93295,\n", "  datetime.date(2009, 11, 16): 0.9369,\n", "  datetime.date(2009, 11, 17): 0.930525,\n", "  datetime.date(2009, 11, 18): 0.929375,\n", "  datetime.date(2009, 11, 19): 0.918575,\n", "  datetime.date(2009, 11, 20): 0.91465,\n", "  datetime.date(2009, 11, 23): 0.92385,\n", "  datetime.date(2009, 11, 24): 0.919225,\n", "  datetime.date(2009, 11, 25): 0.93205,\n", "  datetime.date(2009, 11, 26): 0.913775,\n", "  datetime.date(2009, 11, 27): 0.906425,\n", "  datetime.date(2009, 11, 30): 0.915975,\n", "  datetime.date(2009, 12, 1): 0.92505,\n", "  datetime.date(2009, 12, 2): 0.9248,\n", "  datetime.date(2009, 12, 3): 0.9239,\n", "  datetime.date(2009, 12, 4): 0.91465,\n", "  datetime.date(2009, 12, 7): 0.912675,\n", "  datetime.date(2009, 12, 8): 0.90385,\n", "  datetime.date(2009, 12, 9): 0.908675,\n", "  datetime.date(2009, 12, 10): 0.916575,\n", "  datetime.date(2009, 12, 11): 0.9126,\n", "  datetime.date(2009, 12, 14): 0.9167,\n", "  datetime.date(2009, 12, 15): 0.9062,\n", "  datetime.date(2009, 12, 16): 0.900775,\n", "  datetime.date(2009, 12, 17): 0.8867,\n", "  datetime.date(2009, 12, 18): 0.891325,\n", "  datetime.date(2009, 12, 21): 0.881425,\n", "  datetime.date(2009, 12, 22): 0.875875,\n", "  datetime.date(2009, 12, 23): 0.87995,\n", "  datetime.date(2009, 12, 24): 0.883,\n", "  datetime.date(2009, 12, 28): 0.886925,\n", "  datetime.date(2009, 12, 29): 0.894925,\n", "  datetime.date(2009, 12, 30): 0.89435,\n", "  datetime.date(2009, 12, 31): 0.89745,\n", "  datetime.date(2010, 1, 4): 0.912675,\n", "  datetime.date(2010, 1, 5): 0.9119,\n", "  datetime.date(2010, 1, 6): 0.919775,\n", "  datetime.date(2010, 1, 7): 0.9174,\n", "  datetime.date(2010, 1, 8): 0.92475,\n", "  datetime.date(2010, 1, 11): 0.929625,\n", "  datetime.date(2010, 1, 12): 0.92,\n", "  datetime.date(2010, 1, 13): 0.92415,\n", "  datetime.date(2010, 1, 14): 0.9318,\n", "  datetime.date(2010, 1, 15): 0.9228,\n", "  datetime.date(2010, 1, 18): 0.92625,\n", "  datetime.date(2010, 1, 19): 0.92315,\n", "  datetime.date(2010, 1, 20): 0.9101,\n", "  datetime.date(2010, 1, 21): 0.899825,\n", "  datetime.date(2010, 1, 22): 0.900625,\n", "  datetime.date(2010, 1, 25): 0.904125,\n", "  datetime.date(2010, 1, 26): 0.898575,\n", "  datetime.date(2010, 1, 27): 0.89535,\n", "  datetime.date(2010, 1, 28): 0.8946,\n", "  datetime.date(2010, 1, 29): 0.883775,\n", "  datetime.date(2010, 2, 1): 0.891375,\n", "  datetime.date(2010, 2, 2): 0.8864,\n", "  datetime.date(2010, 2, 3): 0.88295,\n", "  datetime.date(2010, 2, 4): 0.864525,\n", "  datetime.date(2010, 2, 5): 0.8683,\n", "  datetime.date(2010, 2, 8): 0.8647,\n", "  datetime.date(2010, 2, 9): 0.8787,\n", "  datetime.date(2010, 2, 10): 0.87545,\n", "  datetime.date(2010, 2, 11): 0.8904,\n", "  datetime.date(2010, 2, 12): 0.887775,\n", "  datetime.date(2010, 2, 15): 0.888425,\n", "  datetime.date(2010, 2, 16): 0.902125,\n", "  datetime.date(2010, 2, 17): 0.899425,\n", "  datetime.date(2010, 2, 18): 0.894025,\n", "  datetime.date(2010, 2, 19): 0.8989,\n", "  datetime.date(2010, 2, 22): 0.9004,\n", "  datetime.date(2010, 2, 23): 0.89135,\n", "  datetime.date(2010, 2, 24): 0.8936,\n", "  datetime.date(2010, 2, 25): 0.888325,\n", "  datetime.date(2010, 2, 26): 0.89525,\n", "  datetime.date(2010, 3, 1): 0.900925,\n", "  datetime.date(2010, 3, 2): 0.903575,\n", "  datetime.date(2010, 3, 3): 0.9059,\n", "  datetime.date(2010, 3, 4): 0.9001,\n", "  datetime.date(2010, 3, 5): 0.907725,\n", "  datetime.date(2010, 3, 8): 0.90925,\n", "  datetime.date(2010, 3, 9): 0.914025,\n", "  datetime.date(2010, 3, 10): 0.915475,\n", "  datetime.date(2010, 3, 11): 0.915375,\n", "  datetime.date(2010, 3, 12): 0.915275,\n", "  datetime.date(2010, 3, 15): 0.9147,\n", "  datetime.date(2010, 3, 16): 0.91855,\n", "  datetime.date(2010, 3, 17): 0.92365,\n", "  datetime.date(2010, 3, 18): 0.9207,\n", "  datetime.date(2010, 3, 19): 0.915375,\n", "  datetime.date(2010, 3, 22): 0.918325,\n", "  datetime.date(2010, 3, 23): 0.918775,\n", "  datetime.date(2010, 3, 24): 0.90735,\n", "  datetime.date(2010, 3, 25): 0.90735,\n", "  datetime.date(2010, 3, 26): 0.90415,\n", "  datetime.date(2010, 3, 29): 0.917675,\n", "  datetime.date(2010, 3, 30): 0.919575,\n", "  datetime.date(2010, 3, 31): 0.917225,\n", "  datetime.date(2010, 4, 1): 0.92105,\n", "  datetime.date(2010, 4, 2): 0.919325,\n", "  datetime.date(2010, 4, 5): 0.9214,\n", "  datetime.date(2010, 4, 6): 0.928325,\n", "  datetime.date(2010, 4, 7): 0.926775,\n", "  datetime.date(2010, 4, 8): 0.9288,\n", "  datetime.date(2010, 4, 9): 0.933175,\n", "  datetime.date(2010, 4, 12): 0.927475,\n", "  datetime.date(2010, 4, 13): 0.929175,\n", "  datetime.date(2010, 4, 14): 0.9351,\n", "  datetime.date(2010, 4, 15): 0.934475,\n", "  datetime.date(2010, 4, 16): 0.924225,\n", "  datetime.date(2010, 4, 19): 0.92385,\n", "  datetime.date(2010, 4, 20): 0.931775,\n", "  datetime.date(2010, 4, 21): 0.9268,\n", "  datetime.date(2010, 4, 22): 0.92745,\n", "  datetime.date(2010, 4, 23): 0.92775,\n", "  datetime.date(2010, 4, 26): 0.926925,\n", "  datetime.date(2010, 4, 27): 0.915475,\n", "  datetime.date(2010, 4, 28): 0.925175,\n", "  datetime.date(2010, 4, 29): 0.92775,\n", "  datetime.date(2010, 4, 30): 0.9243,\n", "  datetime.date(2010, 5, 3): 0.926125,\n", "  datetime.date(2010, 5, 4): 0.90885,\n", "  datetime.date(2010, 5, 5): 0.90605,\n", "  datetime.date(2010, 5, 6): 0.8851,\n", "  datetime.date(2010, 5, 7): 0.8876,\n", "  datetime.date(2010, 5, 10): 0.9028,\n", "  datetime.date(2010, 5, 11): 0.895125,\n", "  datetime.date(2010, 5, 12): 0.893575,\n", "  datetime.date(2010, 5, 13): 0.896,\n", "  datetime.date(2010, 5, 14): 0.88585,\n", "  datetime.date(2010, 5, 17): 0.87745,\n", "  datetime.date(2010, 5, 18): 0.864475,\n", "  datetime.date(2010, 5, 19): 0.8472,\n", "  datetime.date(2010, 5, 20): 0.8172,\n", "  datetime.date(2010, 5, 21): 0.8326,\n", "  datetime.date(2010, 5, 24): 0.826525,\n", "  datetime.date(2010, 5, 25): 0.8277,\n", "  datetime.date(2010, 5, 26): 0.821525,\n", "  datetime.date(2010, 5, 27): 0.851375,\n", "  datetime.date(2010, 5, 28): 0.8474,\n", "  datetime.date(2010, 5, 31): 0.84585,\n", "  datetime.date(2010, 6, 1): 0.83115,\n", "  datetime.date(2010, 6, 2): 0.842025,\n", "  datetime.date(2010, 6, 3): 0.8448,\n", "  datetime.date(2010, 6, 4): 0.823275,\n", "  datetime.date(2010, 6, 7): 0.810475,\n", "  datetime.date(2010, 6, 8): 0.8278,\n", "  datetime.date(2010, 6, 9): 0.8278,\n", "  datetime.date(2010, 6, 10): 0.850175,\n", "  datetime.date(2010, 6, 11): 0.8504,\n", "  datetime.date(2010, 6, 14): 0.858675,\n", "  datetime.date(2010, 6, 15): 0.86555,\n", "  datetime.date(2010, 6, 16): 0.86395,\n", "  datetime.date(2010, 6, 17): 0.8686,\n", "  datetime.date(2010, 6, 18): 0.87215,\n", "  datetime.date(2010, 6, 21): 0.876475,\n", "  datetime.date(2010, 6, 22): 0.871625,\n", "  datetime.date(2010, 6, 23): 0.873975,\n", "  datetime.date(2010, 6, 24): 0.866725,\n", "  datetime.date(2010, 6, 25): 0.87395,\n", "  datetime.date(2010, 6, 28): 0.872175,\n", "  datetime.date(2010, 6, 29): 0.848725,\n", "  datetime.date(2010, 6, 30): 0.840725,\n", "  datetime.date(2010, 7, 1): 0.8433,\n", "  datetime.date(2010, 7, 2): 0.841675,\n", "  datetime.date(2010, 7, 5): 0.8393,\n", "  datetime.date(2010, 7, 6): 0.852625,\n", "  datetime.date(2010, 7, 7): 0.864325,\n", "  datetime.date(2010, 7, 8): 0.877375,\n", "  datetime.date(2010, 7, 9): 0.877575,\n", "  datetime.date(2010, 7, 12): 0.875925,\n", "  datetime.date(2010, 7, 13): 0.883225,\n", "  datetime.date(2010, 7, 14): 0.885,\n", "  datetime.date(2010, 7, 15): 0.884425,\n", "  datetime.date(2010, 7, 16): 0.868975,\n", "  datetime.date(2010, 7, 19): 0.8684,\n", "  datetime.date(2010, 7, 20): 0.8839,\n", "  datetime.date(2010, 7, 21): 0.8781,\n", "  datetime.date(2010, 7, 22): 0.893325,\n", "  datetime.date(2010, 7, 23): 0.89565,\n", "  datetime.date(2010, 7, 26): 0.902575,\n", "  datetime.date(2010, 7, 27): 0.902475,\n", "  datetime.date(2010, 7, 28): 0.893225,\n", "  datetime.date(2010, 7, 29): 0.900425,\n", "  datetime.date(2010, 7, 30): 0.90445,\n", "  datetime.date(2010, 8, 2): 0.9137,\n", "  datetime.date(2010, 8, 3): 0.912725,\n", "  datetime.date(2010, 8, 4): 0.9166,\n", "  datetime.date(2010, 8, 5): 0.915775,\n", "  datetime.date(2010, 8, 6): 0.918375,\n", "  datetime.date(2010, 8, 9): 0.916525,\n", "  datetime.date(2010, 8, 10): 0.913475,\n", "  datetime.date(2010, 8, 11): 0.897025,\n", "  datetime.date(2010, 8, 12): 0.89635,\n", "  datetime.date(2010, 8, 13): 0.893,\n", "  datetime.date(2010, 8, 16): 0.898175,\n", "  datetime.date(2010, 8, 17): 0.9052,\n", "  datetime.date(2010, 8, 18): 0.8983,\n", "  datetime.date(2010, 8, 19): 0.89265,\n", "  datetime.date(2010, 8, 20): 0.893975,\n", "  datetime.date(2010, 8, 23): 0.891325,\n", "  datetime.date(2010, 8, 24): 0.8817,\n", "  datetime.date(2010, 8, 25): 0.884,\n", "  datetime.date(2010, 8, 26): 0.8863,\n", "  datetime.date(2010, 8, 27): 0.899175,\n", "  datetime.date(2010, 8, 30): 0.89175,\n", "  datetime.date(2010, 8, 31): 0.890675,\n", "  datetime.date(2010, 9, 1): 0.91165,\n", "  datetime.date(2010, 9, 2): 0.911,\n", "  datetime.date(2010, 9, 3): 0.9166,\n", "  datetime.date(2010, 9, 6): 0.917575,\n", "  datetime.date(2010, 9, 7): 0.91065,\n", "  datetime.date(2010, 9, 8): 0.91815,\n", "  datetime.date(2010, 9, 9): 0.923025,\n", "  datetime.date(2010, 9, 10): 0.9264,\n", "  datetime.date(2010, 9, 13): 0.935975,\n", "  datetime.date(2010, 9, 14): 0.9397,\n", "  datetime.date(2010, 9, 15): 0.938175,\n", "  datetime.date(2010, 9, 16): 0.936875,\n", "  datetime.date(2010, 9, 17): 0.9362,\n", "  datetime.date(2010, 9, 20): 0.947225,\n", "  datetime.date(2010, 9, 21): 0.95515,\n", "  datetime.date(2010, 9, 22): 0.956825,\n", "  datetime.date(2010, 9, 23): 0.948975,\n", "  datetime.date(2010, 9, 24): 0.959025,\n", "  datetime.date(2010, 9, 27): 0.960925,\n", "  datetime.date(2010, 9, 28): 0.967675,\n", "  datetime.date(2010, 9, 29): 0.969725,\n", "  datetime.date(2010, 9, 30): 0.967075,\n", "  datetime.date(2010, 10, 1): 0.972775,\n", "  datetime.date(2010, 10, 4): 0.968275,\n", "  datetime.date(2010, 10, 5): 0.971825,\n", "  datetime.date(2010, 10, 6): 0.97745,\n", "  datetime.date(2010, 10, 7): 0.98255,\n", "  datetime.date(2010, 10, 8): 0.984975,\n", "  datetime.date(2010, 10, 11): 0.983875,\n", "  datetime.date(2010, 10, 12): 0.98625,\n", "  datetime.date(2010, 10, 13): 0.990475,\n", "  datetime.date(2010, 10, 14): 0.994175,\n", "  datetime.date(2010, 10, 15): 0.990725,\n", "  datetime.date(2010, 10, 18): 0.988875,\n", "  datetime.date(2010, 10, 19): 0.9686,\n", "  datetime.date(2010, 10, 20): 0.987025,\n", "  datetime.date(2010, 10, 21): 0.977225,\n", "  datetime.date(2010, 10, 22): 0.9827,\n", "  datetime.date(2010, 10, 25): 0.990525,\n", "  datetime.date(2010, 10, 26): 0.985425,\n", "  datetime.date(2010, 10, 27): 0.972,\n", "  datetime.date(2010, 10, 28): 0.979,\n", "  datetime.date(2010, 10, 29): 0.98355,\n", "  datetime.date(2010, 11, 1): 0.9871,\n", "  datetime.date(2010, 11, 2): 0.999525,\n", "  datetime.date(2010, 11, 3): 1.006225,\n", "  datetime.date(2010, 11, 4): 1.014875,\n", "  datetime.date(2010, 11, 5): 1.01585,\n", "  datetime.date(2010, 11, 8): 1.013575,\n", "  datetime.date(2010, 11, 9): 1.003575,\n", "  datetime.date(2010, 11, 10): 1.005325,\n", "  datetime.date(2010, 11, 11): 0.9978,\n", "  datetime.date(2010, 11, 12): 0.9847,\n", "  datetime.date(2010, 11, 15): 0.985225,\n", "  datetime.date(2010, 11, 16): 0.97665,\n", "  datetime.date(2010, 11, 17): 0.979725,\n", "  datetime.date(2010, 11, 18): 0.989925,\n", "  datetime.date(2010, 11, 19): 0.9866,\n", "  datetime.date(2010, 11, 22): 0.988675,\n", "  datetime.date(2010, 11, 23): 0.97235,\n", "  datetime.date(2010, 11, 24): 0.9817,\n", "  datetime.date(2010, 11, 25): 0.980825,\n", "  datetime.date(2010, 11, 26): 0.9645,\n", "  datetime.date(2010, 11, 29): 0.9631,\n", "  datetime.date(2010, 11, 30): 0.958775,\n", "  datetime.date(2010, 12, 1): 0.96835,\n", "  datetime.date(2010, 12, 2): 0.9771,\n", "  datetime.date(2010, 12, 3): 0.993075,\n", "  datetime.date(2010, 12, 6): 0.989875,\n", "  datetime.date(2010, 12, 7): 0.982875,\n", "  datetime.date(2010, 12, 8): 0.9796,\n", "  datetime.date(2010, 12, 9): 0.98375,\n", "  datetime.date(2010, 12, 10): 0.985525,\n", "  datetime.date(2010, 12, 13): 0.996225,\n", "  datetime.date(2010, 12, 14): 0.999025,\n", "  datetime.date(2010, 12, 15): 0.986925,\n", "  datetime.date(2010, 12, 16): 0.99015,\n", "  datetime.date(2010, 12, 17): 0.98815,\n", "  datetime.date(2010, 12, 20): 0.993525,\n", "  datetime.date(2010, 12, 21): 0.997775,\n", "  datetime.date(2010, 12, 22): 0.999025,\n", "  datetime.date(2010, 12, 23): 1.004275,\n", "  datetime.date(2010, 12, 24): 1.0048,\n", "  datetime.date(2010, 12, 27): 1.0046,\n", "  datetime.date(2010, 12, 28): 1.010025,\n", "  datetime.date(2010, 12, 29): 1.017875,\n", "  datetime.date(2010, 12, 30): 1.01735,\n", "  datetime.date(2010, 12, 31): 1.023325,\n", "  datetime.date(2011, 1, 3): 1.016825,\n", "  datetime.date(2011, 1, 4): 1.0051,\n", "  datetime.date(2011, 1, 5): 0.999575,\n", "  datetime.date(2011, 1, 6): 0.994375,\n", "  datetime.date(2011, 1, 7): 0.995925,\n", "  datetime.date(2011, 1, 10): 0.995575,\n", "  datetime.date(2011, 1, 11): 0.987125,\n", "  datetime.date(2011, 1, 12): 0.996575,\n", "  datetime.date(2011, 1, 13): 0.997575,\n", "  datetime.date(2011, 1, 14): 0.988775,\n", "  datetime.date(2011, 1, 17): 0.99375,\n", "  datetime.date(2011, 1, 18): 0.999425,\n", "  datetime.date(2011, 1, 19): 1.000825,\n", "  datetime.date(2011, 1, 20): 0.98705,\n", "  datetime.date(2011, 1, 21): 0.9898,\n", "  datetime.date(2011, 1, 24): 0.9974,\n", "  datetime.date(2011, 1, 25): 0.9966,\n", "  datetime.date(2011, 1, 26): 0.999325,\n", "  datetime.date(2011, 1, 27): 0.991925,\n", "  datetime.date(2011, 1, 28): 0.994,\n", "  datetime.date(2011, 1, 31): 0.997375,\n", "  datetime.date(2011, 2, 1): 1.011125,\n", "  datetime.date(2011, 2, 2): 1.010125,\n", "  datetime.date(2011, 2, 3): 1.01535,\n", "  datetime.date(2011, 2, 4): 1.01395,\n", "  datetime.date(2011, 2, 7): 1.01355,\n", "  datetime.date(2011, 2, 8): 1.014625,\n", "  datetime.date(2011, 2, 9): 1.01245,\n", "  datetime.date(2011, 2, 10): 1.004375,\n", "  datetime.date(2011, 2, 11): 1.002225,\n", "  datetime.date(2011, 2, 14): 1.0029,\n", "  datetime.date(2011, 2, 15): 0.99645,\n", "  datetime.date(2011, 2, 16): 1.0032,\n", "  datetime.date(2011, 2, 17): 1.011875,\n", "  datetime.date(2011, 2, 18): 1.0146,\n", "  datetime.date(2011, 2, 21): 1.009375,\n", "  datetime.date(2011, 2, 22): 0.9988,\n", "  datetime.date(2011, 2, 23): 1.0022,\n", "  datetime.date(2011, 2, 24): 1.0088,\n", "  datetime.date(2011, 2, 25): 1.017675,\n", "  datetime.date(2011, 2, 28): 1.0186,\n", "  datetime.date(2011, 3, 1): 1.01335,\n", "  datetime.date(2011, 3, 2): 1.0168,\n", "  datetime.date(2011, 3, 3): 1.014525,\n", "  datetime.date(2011, 3, 4): 1.013775,\n", "  datetime.date(2011, 3, 7): 1.01185,\n", "  datetime.date(2011, 3, 8): 1.009875,\n", "  datetime.date(2011, 3, 9): 1.0108,\n", "  datetime.date(2011, 3, 10): 1.000725,\n", "  datetime.date(2011, 3, 11): 1.013875,\n", "  datetime.date(2011, 3, 14): 1.009775,\n", "  datetime.date(2011, 3, 15): 0.990925,\n", "  datetime.date(2011, 3, 16): 0.983875,\n", "  datetime.date(2011, 3, 17): 0.980275,\n", "  datetime.date(2011, 3, 18): 0.995875,\n", "  datetime.date(2011, 3, 21): 1.006325,\n", "  datetime.date(2011, 3, 22): 1.010325,\n", "  datetime.date(2011, 3, 23): 1.013225,\n", "  datetime.date(2011, 3, 24): 1.021175,\n", "  datetime.date(2011, 3, 25): 1.026,\n", "  datetime.date(2011, 3, 28): 1.0244,\n", "  datetime.date(2011, 3, 29): 1.02915,\n", "  datetime.date(2011, 3, 30): 1.032875,\n", "  datetime.date(2011, 3, 31): 1.032875,\n", "  datetime.date(2011, 4, 1): 1.038625,\n", "  datetime.date(2011, 4, 4): 1.036325,\n", "  datetime.date(2011, 4, 5): 1.03295,\n", "  datetime.date(2011, 4, 6): 1.044075,\n", "  datetime.date(2011, 4, 7): 1.04695,\n", "  datetime.date(2011, 4, 8): 1.0564,\n", "  datetime.date(2011, 4, 11): 1.049575,\n", "  datetime.date(2011, 4, 12): 1.043725,\n", "  datetime.date(2011, 4, 13): 1.050675,\n", "  datetime.date(2011, 4, 14): 1.054325,\n", "  datetime.date(2011, 4, 15): 1.0568,\n", "  datetime.date(2011, 4, 18): 1.05085,\n", "  datetime.date(2011, 4, 19): 1.052575,\n", "  datetime.date(2011, 4, 20): 1.0714,\n", "  datetime.date(2011, 4, 21): 1.07465,\n", "  datetime.date(2011, 4, 22): 1.073625,\n", "  datetime.date(2011, 4, 25): 1.0721,\n", "  datetime.date(2011, 4, 26): 1.078525,\n", "  datetime.date(2011, 4, 27): 1.087325,\n", "  datetime.date(2011, 4, 28): 1.09285,\n", "  datetime.date(2011, 4, 29): 1.097075,\n", "  datetime.date(2011, 5, 2): 1.09445,\n", "  datetime.date(2011, 5, 3): 1.0846,\n", "  datetime.date(2011, 5, 4): 1.0747,\n", "  datetime.date(2011, 5, 5): 1.05795,\n", "  datetime.date(2011, 5, 6): 1.0701,\n", "  datetime.date(2011, 5, 9): 1.080675,\n", "  datetime.date(2011, 5, 10): 1.083725,\n", "  datetime.date(2011, 5, 11): 1.07005,\n", "  datetime.date(2011, 5, 12): 1.06775,\n", "  datetime.date(2011, 5, 13): 1.05735,\n", "  datetime.date(2011, 5, 16): 1.05545,\n", "  datetime.date(2011, 5, 17): 1.062725,\n", "  datetime.date(2011, 5, 18): 1.0628,\n", "  datetime.date(2011, 5, 19): 1.066825,\n", "  datetime.date(2011, 5, 20): 1.065875,\n", "  datetime.date(2011, 5, 23): 1.05045,\n", "  datetime.date(2011, 5, 24): 1.055875,\n", "  datetime.date(2011, 5, 25): 1.05315,\n", "  datetime.date(2011, 5, 26): 1.06445,\n", "  datetime.date(2011, 5, 27): 1.070875,\n", "  datetime.date(2011, 5, 30): 1.069075,\n", "  datetime.date(2011, 5, 31): 1.067175,\n", "  datetime.date(2011, 6, 1): 1.061425,\n", "  datetime.date(2011, 6, 2): 1.066975,\n", "  datetime.date(2011, 6, 3): 1.071575,\n", "  datetime.date(2011, 6, 6): 1.071275,\n", "  datetime.date(2011, 6, 7): 1.072125,\n", "  datetime.date(2011, 6, 8): 1.06235,\n", "  datetime.date(2011, 6, 9): 1.06265,\n", "  datetime.date(2011, 6, 10): 1.053675,\n", "  datetime.date(2011, 6, 13): 1.060225,\n", "  datetime.date(2011, 6, 14): 1.068575,\n", "  datetime.date(2011, 6, 15): 1.0577,\n", "  datetime.date(2011, 6, 16): 1.05585,\n", "  datetime.date(2011, 6, 17): 1.062225,\n", "  datetime.date(2011, 6, 20): 1.058175,\n", "  datetime.date(2011, 6, 21): 1.06055,\n", "  datetime.date(2011, 6, 22): 1.05755,\n", "  datetime.date(2011, 6, 23): 1.052425,\n", "  datetime.date(2011, 6, 24): 1.049,\n", "  datetime.date(2011, 6, 27): 1.04435,\n", "  datetime.date(2011, 6, 28): 1.054075,\n", "  datetime.date(2011, 6, 29): 1.068275,\n", "  datetime.date(2011, 6, 30): 1.072075,\n", "  datetime.date(2011, 7, 1): 1.077,\n", "  datetime.date(2011, 7, 4): 1.0735,\n", "  datetime.date(2011, 7, 5): 1.069325,\n", "  datetime.date(2011, 7, 6): 1.070025,\n", "  datetime.date(2011, 7, 7): 1.0776,\n", "  datetime.date(2011, 7, 8): 1.0755,\n", "  datetime.date(2011, 7, 11): 1.065625,\n", "  datetime.date(2011, 7, 12): 1.059775,\n", "  datetime.date(2011, 7, 13): 1.075875,\n", "  datetime.date(2011, 7, 14): 1.072375,\n", "  datetime.date(2011, 7, 15): 1.06485,\n", "  datetime.date(2011, 7, 18): 1.060675,\n", "  datetime.date(2011, 7, 19): 1.0733,\n", "  datetime.date(2011, 7, 20): 1.0749,\n", "  datetime.date(2011, 7, 21): 1.084075,\n", "  datetime.date(2011, 7, 22): 1.08525,\n", "  datetime.date(2011, 7, 25): 1.084525,\n", "  datetime.date(2011, 7, 26): 1.0956,\n", "  datetime.date(2011, 7, 27): 1.10235,\n", "  datetime.date(2011, 7, 28): 1.100275,\n", "  datetime.date(2011, 7, 29): 1.099675,\n", "  datetime.date(2011, 8, 1): 1.097075,\n", "  datetime.date(2011, 8, 2): 1.078,\n", "  datetime.date(2011, 8, 3): 1.075525,\n", "  datetime.date(2011, 8, 4): 1.046475,\n", "  datetime.date(2011, 8, 5): 1.044225,\n", "  datetime.date(2011, 8, 8): 1.01875,\n", "  datetime.date(2011, 8, 9): 1.03555,\n", "  datetime.date(2011, 8, 10): 1.017825,\n", "  datetime.date(2011, 8, 11): 1.0352,\n", "  datetime.date(2011, 8, 12): 1.03545,\n", "  datetime.date(2011, 8, 15): 1.050675,\n", "  datetime.date(2011, 8, 16): 1.04855,\n", "  datetime.date(2011, 8, 17): 1.05495,\n", "  datetime.date(2011, 8, 18): 1.03905,\n", "  datetime.date(2011, 8, 19): 1.040675,\n", "  datetime.date(2011, 8, 22): 1.040925,\n", "  datetime.date(2011, 8, 23): 1.052625,\n", "  datetime.date(2011, 8, 24): 1.0474,\n", "  datetime.date(2011, 8, 25): 1.043125,\n", "  datetime.date(2011, 8, 26): 1.0573,\n", "  datetime.date(2011, 8, 29): 1.06575,\n", "  datetime.date(2011, 8, 30): 1.068325,\n", "  datetime.date(2011, 8, 31): 1.07065,\n", "  datetime.date(2011, 9, 1): 1.07215,\n", "  datetime.date(2011, 9, 2): 1.0646,\n", "  datetime.date(2011, 9, 5): 1.05515,\n", "  datetime.date(2011, 9, 6): 1.048425,\n", "  datetime.date(2011, 9, 7): 1.066125,\n", "  datetime.date(2011, 9, 8): 1.057575,\n", "  datetime.date(2011, 9, 9): 1.04705,\n", "  datetime.date(2011, 9, 12): 1.0347,\n", "  datetime.date(2011, 9, 13): 1.03115,\n", "  datetime.date(2011, 9, 14): 1.0284,\n", "  datetime.date(2011, 9, 15): 1.0328,\n", "  datetime.date(2011, 9, 16): 1.0361,\n", "  datetime.date(2011, 9, 19): 1.0222,\n", "  datetime.date(2011, 9, 20): 1.027575,\n", "  datetime.date(2011, 9, 21): 1.0043,\n", "  datetime.date(2011, 9, 22): 0.974225,\n", "  datetime.date(2011, 9, 23): 0.977975,\n", "  datetime.date(2011, 9, 26): 0.98325,\n", "  datetime.date(2011, 9, 27): 0.9914,\n", "  datetime.date(2011, 9, 28): 0.978125,\n", "  datetime.date(2011, 9, 29): 0.97815,\n", "  datetime.date(2011, 9, 30): 0.96615,\n", "  datetime.date(2011, 10, 3): 0.9528,\n", "  datetime.date(2011, 10, 4): 0.957275,\n", "  ...},\n", " 'NZDUSD': {datetime.date(2007, 11, 2): 0.76545,\n", "  datetime.date(2007, 11, 9): 0.835,\n", "  datetime.date(2007, 11, 29): 0.7708,\n", "  datetime.date(2007, 11, 30): 0.7643,\n", "  datetime.date(2007, 12, 3): 0.76375,\n", "  datetime.date(2007, 12, 4): 0.76325,\n", "  datetime.date(2007, 12, 5): 0.76965,\n", "  datetime.date(2007, 12, 6): 0.7775,\n", "  datetime.date(2007, 12, 7): 0.77685,\n", "  datetime.date(2007, 12, 10): 0.7813,\n", "  datetime.date(2007, 12, 11): 0.7731,\n", "  datetime.date(2007, 12, 12): 0.78595,\n", "  datetime.date(2007, 12, 13): 0.7803,\n", "  datetime.date(2007, 12, 14): 0.7652,\n", "  datetime.date(2007, 12, 17): 0.75495,\n", "  datetime.date(2007, 12, 18): 0.7554,\n", "  datetime.date(2007, 12, 19): 0.75575,\n", "  datetime.date(2007, 12, 20): 0.7596,\n", "  datetime.date(2007, 12, 21): 0.76335,\n", "  datetime.date(2007, 12, 24): 0.76695,\n", "  datetime.date(2007, 12, 26): 0.76695,\n", "  datetime.date(2007, 12, 27): 0.7708,\n", "  datetime.date(2007, 12, 28): 0.77465,\n", "  datetime.date(2007, 12, 31): 0.76595,\n", "  datetime.date(2008, 1, 2): 0.77525,\n", "  datetime.date(2008, 1, 3): 0.7705,\n", "  datetime.date(2008, 1, 4): 0.7665,\n", "  datetime.date(2008, 1, 7): 0.76885,\n", "  datetime.date(2008, 1, 8): 0.77145,\n", "  datetime.date(2008, 1, 9): 0.771,\n", "  datetime.date(2008, 1, 10): 0.78585,\n", "  datetime.date(2008, 1, 11): 0.78225,\n", "  datetime.date(2008, 1, 14): 0.7897,\n", "  datetime.date(2008, 1, 15): 0.78165,\n", "  datetime.date(2008, 1, 16): 0.77235,\n", "  datetime.date(2008, 1, 17): 0.76685,\n", "  datetime.date(2008, 1, 18): 0.75955,\n", "  datetime.date(2008, 1, 21): 0.7443,\n", "  datetime.date(2008, 1, 22): 0.75045,\n", "  datetime.date(2008, 1, 23): 0.7597,\n", "  datetime.date(2008, 1, 24): 0.77355,\n", "  datetime.date(2008, 1, 25): 0.76905,\n", "  datetime.date(2008, 1, 28): 0.7764,\n", "  datetime.date(2008, 1, 29): 0.77765,\n", "  datetime.date(2008, 1, 30): 0.79055,\n", "  datetime.date(2008, 1, 31): 0.78895,\n", "  datetime.date(2008, 2, 1): 0.79335,\n", "  datetime.date(2008, 2, 4): 0.7934,\n", "  datetime.date(2008, 2, 5): 0.7831,\n", "  datetime.date(2008, 2, 6): 0.7852,\n", "  datetime.date(2008, 2, 7): 0.78645,\n", "  datetime.date(2008, 2, 8): 0.7877,\n", "  datetime.date(2008, 2, 11): 0.7894,\n", "  datetime.date(2008, 2, 12): 0.79155,\n", "  datetime.date(2008, 2, 13): 0.78285,\n", "  datetime.date(2008, 2, 14): 0.78855,\n", "  datetime.date(2008, 2, 15): 0.78915,\n", "  datetime.date(2008, 2, 18): 0.79455,\n", "  datetime.date(2008, 2, 19): 0.7991,\n", "  datetime.date(2008, 2, 20): 0.79805,\n", "  datetime.date(2008, 2, 21): 0.7999,\n", "  datetime.date(2008, 2, 22): 0.80715,\n", "  datetime.date(2008, 2, 25): 0.81035,\n", "  datetime.date(2008, 2, 26): 0.81505,\n", "  datetime.date(2008, 2, 27): 0.81475,\n", "  datetime.date(2008, 2, 28): 0.8159,\n", "  datetime.date(2008, 2, 29): 0.79835,\n", "  datetime.date(2008, 3, 3): 0.80475,\n", "  datetime.date(2008, 3, 4): 0.7994,\n", "  datetime.date(2008, 3, 5): 0.7964,\n", "  datetime.date(2008, 3, 6): 0.7971,\n", "  datetime.date(2008, 3, 7): 0.7927,\n", "  datetime.date(2008, 3, 10): 0.78955,\n", "  datetime.date(2008, 3, 11): 0.80145,\n", "  datetime.date(2008, 3, 12): 0.8084,\n", "  datetime.date(2008, 3, 13): 0.81725,\n", "  datetime.date(2008, 3, 14): 0.8132,\n", "  datetime.date(2008, 3, 17): 0.7957,\n", "  datetime.date(2008, 3, 18): 0.8079,\n", "  datetime.date(2008, 3, 19): 0.80175,\n", "  datetime.date(2008, 3, 20): 0.7916,\n", "  datetime.date(2008, 3, 21): 0.7925,\n", "  datetime.date(2008, 3, 24): 0.79735,\n", "  datetime.date(2008, 3, 25): 0.80705,\n", "  datetime.date(2008, 3, 26): 0.8033,\n", "  datetime.date(2008, 3, 27): 0.80395,\n", "  datetime.date(2008, 3, 28): 0.7951,\n", "  datetime.date(2008, 3, 31): 0.786,\n", "  datetime.date(2008, 4, 1): 0.78395,\n", "  datetime.date(2008, 4, 2): 0.7897,\n", "  datetime.date(2008, 4, 3): 0.7866,\n", "  datetime.date(2008, 4, 4): 0.7886,\n", "  datetime.date(2008, 4, 7): 0.7984,\n", "  datetime.date(2008, 4, 8): 0.7978,\n", "  datetime.date(2008, 4, 9): 0.79705,\n", "  datetime.date(2008, 4, 10): 0.80035,\n", "  datetime.date(2008, 4, 11): 0.79435,\n", "  datetime.date(2008, 4, 14): 0.7885,\n", "  datetime.date(2008, 4, 15): 0.7851,\n", "  datetime.date(2008, 4, 16): 0.7896,\n", "  datetime.date(2008, 4, 17): 0.7877,\n", "  datetime.date(2008, 4, 18): 0.7903,\n", "  datetime.date(2008, 4, 21): 0.7929,\n", "  datetime.date(2008, 4, 22): 0.79745,\n", "  datetime.date(2008, 4, 23): 0.7992,\n", "  datetime.date(2008, 4, 24): 0.7893,\n", "  datetime.date(2008, 4, 25): 0.7808,\n", "  datetime.date(2008, 4, 28): 0.78605,\n", "  datetime.date(2008, 4, 29): 0.77365,\n", "  datetime.date(2008, 4, 30): 0.7848,\n", "  datetime.date(2008, 5, 1): 0.77835,\n", "  datetime.date(2008, 5, 2): 0.78125,\n", "  datetime.date(2008, 5, 5): 0.78485,\n", "  datetime.date(2008, 5, 6): 0.7912,\n", "  datetime.date(2008, 5, 7): 0.78195,\n", "  datetime.date(2008, 5, 8): 0.7707,\n", "  datetime.date(2008, 5, 9): 0.768,\n", "  datetime.date(2008, 5, 12): 0.76995,\n", "  datetime.date(2008, 5, 13): 0.7652,\n", "  datetime.date(2008, 5, 14): 0.76135,\n", "  datetime.date(2008, 5, 15): 0.7642,\n", "  datetime.date(2008, 5, 16): 0.77255,\n", "  datetime.date(2008, 5, 19): 0.7718,\n", "  datetime.date(2008, 5, 20): 0.7746,\n", "  datetime.date(2008, 5, 21): 0.77635,\n", "  datetime.date(2008, 5, 22): 0.78345,\n", "  datetime.date(2008, 5, 23): 0.78585,\n", "  datetime.date(2008, 5, 26): 0.78725,\n", "  datetime.date(2008, 5, 27): 0.7897,\n", "  datetime.date(2008, 5, 28): 0.7841,\n", "  datetime.date(2008, 5, 29): 0.7786,\n", "  datetime.date(2008, 5, 30): 0.78405,\n", "  datetime.date(2008, 6, 2): 0.7847,\n", "  datetime.date(2008, 6, 3): 0.78175,\n", "  datetime.date(2008, 6, 4): 0.7798,\n", "  datetime.date(2008, 6, 5): 0.7674,\n", "  datetime.date(2008, 6, 6): 0.76735,\n", "  datetime.date(2008, 6, 9): 0.75855,\n", "  datetime.date(2008, 6, 10): 0.75165,\n", "  datetime.date(2008, 6, 11): 0.7563,\n", "  datetime.date(2008, 6, 12): 0.75,\n", "  datetime.date(2008, 6, 13): 0.74935,\n", "  datetime.date(2008, 6, 16): 0.75285,\n", "  datetime.date(2008, 6, 17): 0.756,\n", "  datetime.date(2008, 6, 18): 0.75895,\n", "  datetime.date(2008, 6, 19): 0.7619,\n", "  datetime.date(2008, 6, 20): 0.7617,\n", "  datetime.date(2008, 6, 23): 0.75685,\n", "  datetime.date(2008, 6, 24): 0.7585,\n", "  datetime.date(2008, 6, 25): 0.75865,\n", "  datetime.date(2008, 6, 26): 0.7563,\n", "  datetime.date(2008, 6, 27): 0.7609,\n", "  datetime.date(2008, 6, 30): 0.762,\n", "  datetime.date(2008, 7, 1): 0.7563,\n", "  datetime.date(2008, 7, 2): 0.76055,\n", "  datetime.date(2008, 7, 3): 0.75595,\n", "  datetime.date(2008, 7, 4): 0.75905,\n", "  datetime.date(2008, 7, 7): 0.7537,\n", "  datetime.date(2008, 7, 8): 0.75195,\n", "  datetime.date(2008, 7, 9): 0.7596,\n", "  datetime.date(2008, 7, 10): 0.75765,\n", "  datetime.date(2008, 7, 11): 0.7597,\n", "  datetime.date(2008, 7, 14): 0.76415,\n", "  datetime.date(2008, 7, 15): 0.77185,\n", "  datetime.date(2008, 7, 16): 0.7714,\n", "  datetime.date(2008, 7, 17): 0.76095,\n", "  datetime.date(2008, 7, 18): 0.7611,\n", "  datetime.date(2008, 7, 21): 0.7602,\n", "  datetime.date(2008, 7, 22): 0.75945,\n", "  datetime.date(2008, 7, 23): 0.7503,\n", "  datetime.date(2008, 7, 24): 0.74145,\n", "  datetime.date(2008, 7, 25): 0.7426,\n", "  datetime.date(2008, 7, 28): 0.7449,\n", "  datetime.date(2008, 7, 29): 0.73975,\n", "  datetime.date(2008, 7, 30): 0.7337,\n", "  datetime.date(2008, 7, 31): 0.73435,\n", "  datetime.date(2008, 8, 1): 0.7268,\n", "  datetime.date(2008, 8, 4): 0.73025,\n", "  datetime.date(2008, 8, 5): 0.7262,\n", "  datetime.date(2008, 8, 6): 0.71845,\n", "  datetime.date(2008, 8, 7): 0.71515,\n", "  datetime.date(2008, 8, 8): 0.7047,\n", "  datetime.date(2008, 8, 11): 0.6997,\n", "  datetime.date(2008, 8, 12): 0.7,\n", "  datetime.date(2008, 8, 13): 0.70525,\n", "  datetime.date(2008, 8, 14): 0.6996,\n", "  datetime.date(2008, 8, 15): 0.7067,\n", "  datetime.date(2008, 8, 18): 0.7111,\n", "  datetime.date(2008, 8, 19): 0.71235,\n", "  datetime.date(2008, 8, 20): 0.7118,\n", "  datetime.date(2008, 8, 21): 0.71985,\n", "  datetime.date(2008, 8, 22): 0.70915,\n", "  datetime.date(2008, 8, 25): 0.70445,\n", "  datetime.date(2008, 8, 26): 0.697,\n", "  datetime.date(2008, 8, 27): 0.70085,\n", "  datetime.date(2008, 8, 28): 0.701,\n", "  datetime.date(2008, 8, 29): 0.7003,\n", "  datetime.date(2008, 9, 1): 0.69595,\n", "  datetime.date(2008, 9, 2): 0.6875,\n", "  datetime.date(2008, 9, 3): 0.68515,\n", "  datetime.date(2008, 9, 4): 0.6725,\n", "  datetime.date(2008, 9, 5): 0.6668,\n", "  datetime.date(2008, 9, 8): 0.66815,\n", "  datetime.date(2008, 9, 9): 0.67155,\n", "  datetime.date(2008, 9, 10): 0.66575,\n", "  datetime.date(2008, 9, 11): 0.65115,\n", "  datetime.date(2008, 9, 12): 0.66675,\n", "  datetime.date(2008, 9, 15): 0.65655,\n", "  datetime.date(2008, 9, 16): 0.6597,\n", "  datetime.date(2008, 9, 17): 0.6682,\n", "  datetime.date(2008, 9, 18): 0.6738,\n", "  datetime.date(2008, 9, 19): 0.6871,\n", "  datetime.date(2008, 9, 22): 0.6933,\n", "  datetime.date(2008, 9, 23): 0.6841,\n", "  datetime.date(2008, 9, 24): 0.68535,\n", "  datetime.date(2008, 9, 25): 0.68335,\n", "  datetime.date(2008, 9, 26): 0.68535,\n", "  datetime.date(2008, 9, 29): 0.67425,\n", "  datetime.date(2008, 9, 30): 0.6702,\n", "  datetime.date(2008, 10, 1): 0.6747,\n", "  datetime.date(2008, 10, 2): 0.6591,\n", "  datetime.date(2008, 10, 3): 0.6633,\n", "  datetime.date(2008, 10, 6): 0.62125,\n", "  datetime.date(2008, 10, 7): 0.6292,\n", "  datetime.date(2008, 10, 8): 0.611,\n", "  datetime.date(2008, 10, 9): 0.61675,\n", "  datetime.date(2008, 10, 10): 0.59485,\n", "  datetime.date(2008, 10, 13): 0.60785,\n", "  datetime.date(2008, 10, 14): 0.6206,\n", "  datetime.date(2008, 10, 15): 0.6068,\n", "  datetime.date(2008, 10, 16): 0.6108,\n", "  datetime.date(2008, 10, 17): 0.6169,\n", "  datetime.date(2008, 10, 20): 0.6223,\n", "  datetime.date(2008, 10, 21): 0.61085,\n", "  datetime.date(2008, 10, 22): 0.58905,\n", "  datetime.date(2008, 10, 23): 0.58555,\n", "  datetime.date(2008, 10, 24): 0.55615,\n", "  datetime.date(2008, 10, 27): 0.54675,\n", "  datetime.date(2008, 10, 28): 0.55155,\n", "  datetime.date(2008, 10, 29): 0.58795,\n", "  datetime.date(2008, 10, 30): 0.5873,\n", "  datetime.date(2008, 10, 31): 0.58395,\n", "  datetime.date(2008, 11, 3): 0.5945,\n", "  datetime.date(2008, 11, 4): 0.6076,\n", "  datetime.date(2008, 11, 5): 0.6057,\n", "  datetime.date(2008, 11, 6): 0.59215,\n", "  datetime.date(2008, 11, 7): 0.5895,\n", "  datetime.date(2008, 11, 10): 0.58305,\n", "  datetime.date(2008, 11, 11): 0.5718,\n", "  datetime.date(2008, 11, 12): 0.563,\n", "  datetime.date(2008, 11, 13): 0.5512,\n", "  datetime.date(2008, 11, 14): 0.5589,\n", "  datetime.date(2008, 11, 17): 0.55865,\n", "  datetime.date(2008, 11, 18): 0.5526,\n", "  datetime.date(2008, 11, 19): 0.5504,\n", "  datetime.date(2008, 11, 20): 0.5338,\n", "  datetime.date(2008, 11, 21): 0.53285,\n", "  datetime.date(2008, 11, 24): 0.5449,\n", "  datetime.date(2008, 11, 25): 0.5439,\n", "  datetime.date(2008, 11, 26): 0.55265,\n", "  datetime.date(2008, 11, 27): 0.5537,\n", "  datetime.date(2008, 11, 28): 0.5498,\n", "  datetime.date(2008, 12, 1): 0.53525,\n", "  datetime.date(2008, 12, 2): 0.53085,\n", "  datetime.date(2008, 12, 3): 0.5321,\n", "  datetime.date(2008, 12, 4): 0.53515,\n", "  datetime.date(2008, 12, 5): 0.5243,\n", "  datetime.date(2008, 12, 8): 0.5439,\n", "  datetime.date(2008, 12, 9): 0.54515,\n", "  datetime.date(2008, 12, 10): 0.54635,\n", "  datetime.date(2008, 12, 11): 0.55565,\n", "  datetime.date(2008, 12, 12): 0.54475,\n", "  datetime.date(2008, 12, 15): 0.55385,\n", "  datetime.date(2008, 12, 16): 0.5659,\n", "  datetime.date(2008, 12, 17): 0.58865,\n", "  datetime.date(2008, 12, 18): 0.5908,\n", "  datetime.date(2008, 12, 19): 0.57245,\n", "  datetime.date(2008, 12, 22): 0.57485,\n", "  datetime.date(2008, 12, 23): 0.56735,\n", "  datetime.date(2008, 12, 24): 0.5746,\n", "  datetime.date(2008, 12, 26): 0.5756,\n", "  datetime.date(2008, 12, 29): 0.581,\n", "  datetime.date(2008, 12, 30): 0.57675,\n", "  datetime.date(2008, 12, 31): 0.58655,\n", "  datetime.date(2009, 1, 2): 0.58475,\n", "  datetime.date(2009, 1, 5): 0.58875,\n", "  datetime.date(2009, 1, 6): 0.5954,\n", "  datetime.date(2009, 1, 7): 0.5989,\n", "  datetime.date(2009, 1, 8): 0.5948,\n", "  datetime.date(2009, 1, 9): 0.5937,\n", "  datetime.date(2009, 1, 12): 0.5812,\n", "  datetime.date(2009, 1, 13): 0.55445,\n", "  datetime.date(2009, 1, 14): 0.5426,\n", "  datetime.date(2009, 1, 15): 0.52875,\n", "  datetime.date(2009, 1, 16): 0.54185,\n", "  datetime.date(2009, 1, 19): 0.5457,\n", "  datetime.date(2009, 1, 20): 0.5258,\n", "  datetime.date(2009, 1, 21): 0.52305,\n", "  datetime.date(2009, 1, 22): 0.527,\n", "  datetime.date(2009, 1, 23): 0.5292,\n", "  datetime.date(2009, 1, 26): 0.5277,\n", "  datetime.date(2009, 1, 27): 0.5297,\n", "  datetime.date(2009, 1, 28): 0.535,\n", "  datetime.date(2009, 1, 29): 0.5157,\n", "  datetime.date(2009, 1, 30): 0.5082,\n", "  datetime.date(2009, 2, 2): 0.50635,\n", "  datetime.date(2009, 2, 3): 0.5095,\n", "  datetime.date(2009, 2, 4): 0.51115,\n", "  datetime.date(2009, 2, 5): 0.51965,\n", "  datetime.date(2009, 2, 6): 0.5335,\n", "  datetime.date(2009, 2, 9): 0.5435,\n", "  datetime.date(2009, 2, 10): 0.5284,\n", "  datetime.date(2009, 2, 11): 0.5239,\n", "  datetime.date(2009, 2, 12): 0.52165,\n", "  datetime.date(2009, 2, 13): 0.525,\n", "  datetime.date(2009, 2, 16): 0.5177,\n", "  datetime.date(2009, 2, 17): 0.50925,\n", "  datetime.date(2009, 2, 18): 0.5107,\n", "  datetime.date(2009, 2, 19): 0.51235,\n", "  datetime.date(2009, 2, 20): 0.5109,\n", "  datetime.date(2009, 2, 23): 0.50885,\n", "  datetime.date(2009, 2, 24): 0.5105,\n", "  datetime.date(2009, 2, 25): 0.51025,\n", "  datetime.date(2009, 2, 26): 0.5105,\n", "  datetime.date(2009, 2, 27): 0.5045,\n", "  datetime.date(2009, 3, 2): 0.49375,\n", "  datetime.date(2009, 3, 3): 0.49675,\n", "  datetime.date(2009, 3, 4): 0.50455,\n", "  datetime.date(2009, 3, 5): 0.4997,\n", "  datetime.date(2009, 3, 6): 0.50335,\n", "  datetime.date(2009, 3, 9): 0.49395,\n", "  datetime.date(2009, 3, 10): 0.50095,\n", "  datetime.date(2009, 3, 11): 0.5066,\n", "  datetime.date(2009, 3, 12): 0.5178,\n", "  datetime.date(2009, 3, 13): 0.5249,\n", "  datetime.date(2009, 3, 16): 0.53275,\n", "  datetime.date(2009, 3, 17): 0.52885,\n", "  datetime.date(2009, 3, 18): 0.5291,\n", "  datetime.date(2009, 3, 19): 0.5566,\n", "  datetime.date(2009, 3, 20): 0.55925,\n", "  datetime.date(2009, 3, 23): 0.5692,\n", "  datetime.date(2009, 3, 24): 0.56455,\n", "  datetime.date(2009, 3, 25): 0.56605,\n", "  datetime.date(2009, 3, 26): 0.57545,\n", "  datetime.date(2009, 3, 27): 0.5691,\n", "  datetime.date(2009, 3, 30): 0.5608,\n", "  datetime.date(2009, 3, 31): 0.57035,\n", "  datetime.date(2009, 4, 1): 0.56225,\n", "  datetime.date(2009, 4, 2): 0.5805,\n", "  datetime.date(2009, 4, 3): 0.5822,\n", "  datetime.date(2009, 4, 6): 0.58715,\n", "  datetime.date(2009, 4, 7): 0.5749,\n", "  datetime.date(2009, 4, 8): 0.5772,\n", "  datetime.date(2009, 4, 9): 0.58355,\n", "  datetime.date(2009, 4, 10): 0.582,\n", "  datetime.date(2009, 4, 13): 0.59115,\n", "  datetime.date(2009, 4, 14): 0.58355,\n", "  datetime.date(2009, 4, 15): 0.57755,\n", "  datetime.date(2009, 4, 16): 0.57205,\n", "  datetime.date(2009, 4, 17): 0.5654,\n", "  datetime.date(2009, 4, 20): 0.5519,\n", "  datetime.date(2009, 4, 21): 0.56205,\n", "  datetime.date(2009, 4, 22): 0.5579,\n", "  datetime.date(2009, 4, 23): 0.56,\n", "  datetime.date(2009, 4, 24): 0.5715,\n", "  datetime.date(2009, 4, 27): 0.56605,\n", "  datetime.date(2009, 4, 28): 0.55985,\n", "  datetime.date(2009, 4, 29): 0.5753,\n", "  datetime.date(2009, 4, 30): 0.566,\n", "  datetime.date(2009, 5, 1): 0.57,\n", "  datetime.date(2009, 5, 4): 0.573,\n", "  datetime.date(2009, 5, 5): 0.5785,\n", "  datetime.date(2009, 5, 6): 0.58475,\n", "  datetime.date(2009, 5, 7): 0.59055,\n", "  datetime.date(2009, 5, 8): 0.6042,\n", "  datetime.date(2009, 5, 11): 0.605,\n", "  datetime.date(2009, 5, 12): 0.6062,\n", "  datetime.date(2009, 5, 13): 0.59095,\n", "  datetime.date(2009, 5, 14): 0.5982,\n", "  datetime.date(2009, 5, 15): 0.5844,\n", "  datetime.date(2009, 5, 18): 0.59355,\n", "  datetime.date(2009, 5, 19): 0.60375,\n", "  datetime.date(2009, 5, 20): 0.60855,\n", "  datetime.date(2009, 5, 21): 0.60655,\n", "  datetime.date(2009, 5, 22): 0.62205,\n", "  datetime.date(2009, 5, 25): 0.61975,\n", "  datetime.date(2009, 5, 26): 0.6244,\n", "  datetime.date(2009, 5, 27): 0.61885,\n", "  datetime.date(2009, 5, 28): 0.6231,\n", "  datetime.date(2009, 5, 29): 0.63825,\n", "  datetime.date(2009, 6, 1): 0.6539,\n", "  datetime.date(2009, 6, 2): 0.65865,\n", "  datetime.date(2009, 6, 3): 0.62925,\n", "  datetime.date(2009, 6, 4): 0.634,\n", "  datetime.date(2009, 6, 5): 0.62825,\n", "  datetime.date(2009, 6, 8): 0.61795,\n", "  datetime.date(2009, 6, 9): 0.62735,\n", "  datetime.date(2009, 6, 10): 0.62615,\n", "  datetime.date(2009, 6, 11): 0.64585,\n", "  datetime.date(2009, 6, 12): 0.6418,\n", "  datetime.date(2009, 6, 15): 0.62975,\n", "  datetime.date(2009, 6, 16): 0.63225,\n", "  datetime.date(2009, 6, 17): 0.635,\n", "  datetime.date(2009, 6, 18): 0.63935,\n", "  datetime.date(2009, 6, 19): 0.64365,\n", "  datetime.date(2009, 6, 22): 0.6309,\n", "  datetime.date(2009, 6, 23): 0.6403,\n", "  datetime.date(2009, 6, 24): 0.64045,\n", "  datetime.date(2009, 6, 25): 0.64455,\n", "  datetime.date(2009, 6, 26): 0.6456,\n", "  datetime.date(2009, 6, 29): 0.65125,\n", "  datetime.date(2009, 6, 30): 0.6458,\n", "  datetime.date(2009, 7, 1): 0.6424,\n", "  datetime.date(2009, 7, 2): 0.6303,\n", "  datetime.date(2009, 7, 3): 0.6301,\n", "  datetime.date(2009, 7, 6): 0.63145,\n", "  datetime.date(2009, 7, 7): 0.6314,\n", "  datetime.date(2009, 7, 8): 0.62295,\n", "  datetime.date(2009, 7, 9): 0.6304,\n", "  datetime.date(2009, 7, 10): 0.62755,\n", "  datetime.date(2009, 7, 13): 0.632,\n", "  datetime.date(2009, 7, 14): 0.63465,\n", "  datetime.date(2009, 7, 15): 0.65115,\n", "  datetime.date(2009, 7, 16): 0.6475,\n", "  datetime.date(2009, 7, 17): 0.64475,\n", "  datetime.date(2009, 7, 20): 0.65675,\n", "  datetime.date(2009, 7, 21): 0.65355,\n", "  datetime.date(2009, 7, 22): 0.66105,\n", "  datetime.date(2009, 7, 23): 0.657,\n", "  datetime.date(2009, 7, 24): 0.6563,\n", "  datetime.date(2009, 7, 27): 0.65605,\n", "  datetime.date(2009, 7, 28): 0.65845,\n", "  datetime.date(2009, 7, 29): 0.65335,\n", "  datetime.date(2009, 7, 30): 0.65295,\n", "  datetime.date(2009, 7, 31): 0.66075,\n", "  datetime.date(2009, 8, 3): 0.66655,\n", "  datetime.date(2009, 8, 4): 0.66615,\n", "  datetime.date(2009, 8, 5): 0.67445,\n", "  datetime.date(2009, 8, 6): 0.66975,\n", "  datetime.date(2009, 8, 7): 0.6719,\n", "  datetime.date(2009, 8, 10): 0.6748,\n", "  datetime.date(2009, 8, 11): 0.66855,\n", "  datetime.date(2009, 8, 12): 0.6747,\n", "  datetime.date(2009, 8, 13): 0.6783,\n", "  datetime.date(2009, 8, 14): 0.67535,\n", "  datetime.date(2009, 8, 17): 0.66925,\n", "  datetime.date(2009, 8, 18): 0.6751,\n", "  datetime.date(2009, 8, 19): 0.67385,\n", "  datetime.date(2009, 8, 20): 0.676,\n", "  datetime.date(2009, 8, 21): 0.6826,\n", "  datetime.date(2009, 8, 24): 0.68575,\n", "  datetime.date(2009, 8, 25): 0.6844,\n", "  datetime.date(2009, 8, 26): 0.68105,\n", "  datetime.date(2009, 8, 27): 0.68845,\n", "  datetime.date(2009, 8, 28): 0.68445,\n", "  datetime.date(2009, 8, 31): 0.6849,\n", "  datetime.date(2009, 9, 1): 0.6739,\n", "  datetime.date(2009, 9, 2): 0.67385,\n", "  datetime.date(2009, 9, 3): 0.6766,\n", "  datetime.date(2009, 9, 4): 0.68805,\n", "  datetime.date(2009, 9, 7): 0.69315,\n", "  datetime.date(2009, 9, 8): 0.69535,\n", "  datetime.date(2009, 9, 9): 0.69545,\n", "  datetime.date(2009, 9, 10): 0.70305,\n", "  datetime.date(2009, 9, 11): 0.7069,\n", "  datetime.date(2009, 9, 14): 0.70025,\n", "  datetime.date(2009, 9, 15): 0.70355,\n", "  datetime.date(2009, 9, 16): 0.714,\n", "  datetime.date(2009, 9, 17): 0.7103,\n", "  datetime.date(2009, 9, 18): 0.70995,\n", "  datetime.date(2009, 9, 21): 0.70685,\n", "  datetime.date(2009, 9, 22): 0.71975,\n", "  datetime.date(2009, 9, 23): 0.72495,\n", "  datetime.date(2009, 9, 24): 0.7161,\n", "  datetime.date(2009, 9, 25): 0.71745,\n", "  datetime.date(2009, 9, 28): 0.71745,\n", "  datetime.date(2009, 9, 29): 0.71675,\n", "  datetime.date(2009, 9, 30): 0.72265,\n", "  datetime.date(2009, 10, 1): 0.7162,\n", "  datetime.date(2009, 10, 2): 0.7178,\n", "  datetime.date(2009, 10, 5): 0.72965,\n", "  datetime.date(2009, 10, 6): 0.7326,\n", "  datetime.date(2009, 10, 7): 0.7329,\n", "  datetime.date(2009, 10, 8): 0.74345,\n", "  datetime.date(2009, 10, 9): 0.73285,\n", "  datetime.date(2009, 10, 12): 0.73525,\n", "  datetime.date(2009, 10, 13): 0.7343,\n", "  datetime.date(2009, 10, 14): 0.73745,\n", "  datetime.date(2009, 10, 15): 0.7421,\n", "  datetime.date(2009, 10, 16): 0.73935,\n", "  datetime.date(2009, 10, 19): 0.7536,\n", "  datetime.date(2009, 10, 20): 0.74655,\n", "  datetime.date(2009, 10, 21): 0.7625,\n", "  datetime.date(2009, 10, 22): 0.75435,\n", "  datetime.date(2009, 10, 23): 0.75435,\n", "  datetime.date(2009, 10, 26): 0.7479,\n", "  datetime.date(2009, 10, 27): 0.7429,\n", "  datetime.date(2009, 10, 28): 0.72945,\n", "  datetime.date(2009, 10, 29): 0.7358,\n", "  datetime.date(2009, 10, 30): 0.717,\n", "  datetime.date(2009, 11, 2): 0.71455,\n", "  datetime.date(2009, 11, 3): 0.71865,\n", "  datetime.date(2009, 11, 4): 0.72545,\n", "  datetime.date(2009, 11, 5): 0.7204,\n", "  datetime.date(2009, 11, 6): 0.725,\n", "  datetime.date(2009, 11, 9): 0.741,\n", "  datetime.date(2009, 11, 10): 0.74055,\n", "  datetime.date(2009, 11, 11): 0.7398,\n", "  datetime.date(2009, 11, 12): 0.73355,\n", "  datetime.date(2009, 11, 13): 0.74295,\n", "  datetime.date(2009, 11, 16): 0.74675,\n", "  datetime.date(2009, 11, 17): 0.74415,\n", "  datetime.date(2009, 11, 18): 0.74435,\n", "  datetime.date(2009, 11, 19): 0.7301,\n", "  datetime.date(2009, 11, 20): 0.7229,\n", "  datetime.date(2009, 11, 23): 0.73415,\n", "  datetime.date(2009, 11, 24): 0.72315,\n", "  datetime.date(2009, 11, 25): 0.7282,\n", "  datetime.date(2009, 11, 26): 0.7149,\n", "  datetime.date(2009, 11, 27): 0.70915,\n", "  datetime.date(2009, 11, 30): 0.7126,\n", "  datetime.date(2009, 12, 1): 0.72795,\n", "  datetime.date(2009, 12, 2): 0.72245,\n", "  datetime.date(2009, 12, 3): 0.72385,\n", "  datetime.date(2009, 12, 4): 0.71745,\n", "  datetime.date(2009, 12, 7): 0.71585,\n", "  datetime.date(2009, 12, 8): 0.7088,\n", "  datetime.date(2009, 12, 9): 0.7101,\n", "  datetime.date(2009, 12, 10): 0.72905,\n", "  datetime.date(2009, 12, 11): 0.72395,\n", "  datetime.date(2009, 12, 14): 0.7287,\n", "  datetime.date(2009, 12, 15): 0.72095,\n", "  datetime.date(2009, 12, 16): 0.7202,\n", "  datetime.date(2009, 12, 17): 0.70965,\n", "  datetime.date(2009, 12, 18): 0.70955,\n", "  datetime.date(2009, 12, 21): 0.7073,\n", "  datetime.date(2009, 12, 22): 0.70365,\n", "  datetime.date(2009, 12, 23): 0.7058,\n", "  datetime.date(2009, 12, 24): 0.7059,\n", "  datetime.date(2009, 12, 28): 0.7085,\n", "  datetime.date(2009, 12, 29): 0.7187,\n", "  datetime.date(2009, 12, 30): 0.72165,\n", "  datetime.date(2009, 12, 31): 0.726,\n", "  datetime.date(2010, 1, 4): 0.7324,\n", "  datetime.date(2010, 1, 5): 0.7348,\n", "  datetime.date(2010, 1, 6): 0.73585,\n", "  datetime.date(2010, 1, 7): 0.73355,\n", "  datetime.date(2010, 1, 8): 0.736,\n", "  datetime.date(2010, 1, 11): 0.7419,\n", "  datetime.date(2010, 1, 12): 0.73915,\n", "  datetime.date(2010, 1, 13): 0.73975,\n", "  datetime.date(2010, 1, 14): 0.7421,\n", "  datetime.date(2010, 1, 15): 0.73785,\n", "  datetime.date(2010, 1, 18): 0.73995,\n", "  datetime.date(2010, 1, 19): 0.73955,\n", "  datetime.date(2010, 1, 20): 0.71915,\n", "  datetime.date(2010, 1, 21): 0.71435,\n", "  datetime.date(2010, 1, 22): 0.7127,\n", "  datetime.date(2010, 1, 25): 0.7137,\n", "  datetime.date(2010, 1, 26): 0.7091,\n", "  datetime.date(2010, 1, 27): 0.705,\n", "  datetime.date(2010, 1, 28): 0.70585,\n", "  datetime.date(2010, 1, 29): 0.70475,\n", "  datetime.date(2010, 2, 1): 0.70825,\n", "  datetime.date(2010, 2, 2): 0.7117,\n", "  datetime.date(2010, 2, 3): 0.70795,\n", "  datetime.date(2010, 2, 4): 0.6879,\n", "  datetime.date(2010, 2, 5): 0.68415,\n", "  datetime.date(2010, 2, 8): 0.68665,\n", "  datetime.date(2010, 2, 9): 0.6941,\n", "  datetime.date(2010, 2, 10): 0.694,\n", "  datetime.date(2010, 2, 11): 0.69975,\n", "  datetime.date(2010, 2, 12): 0.6952,\n", "  datetime.date(2010, 2, 15): 0.69795,\n", "  datetime.date(2010, 2, 16): 0.70635,\n", "  datetime.date(2010, 2, 17): 0.70455,\n", "  datetime.date(2010, 2, 18): 0.7033,\n", "  datetime.date(2010, 2, 19): 0.6972,\n", "  datetime.date(2010, 2, 22): 0.70145,\n", "  datetime.date(2010, 2, 23): 0.69155,\n", "  datetime.date(2010, 2, 24): 0.69245,\n", "  datetime.date(2010, 2, 25): 0.68845,\n", "  datetime.date(2010, 2, 26): 0.6987,\n", "  datetime.date(2010, 3, 1): 0.69975,\n", "  datetime.date(2010, 3, 2): 0.69675,\n", "  datetime.date(2010, 3, 3): 0.6943,\n", "  datetime.date(2010, 3, 4): 0.6873,\n", "  datetime.date(2010, 3, 5): 0.6963,\n", "  datetime.date(2010, 3, 8): 0.7007,\n", "  datetime.date(2010, 3, 9): 0.7028,\n", "  datetime.date(2010, 3, 10): 0.7062,\n", "  datetime.date(2010, 3, 11): 0.69935,\n", "  datetime.date(2010, 3, 12): 0.70095,\n", "  datetime.date(2010, 3, 15): 0.7001,\n", "  datetime.date(2010, 3, 16): 0.70635,\n", "  datetime.date(2010, 3, 17): 0.7173,\n", "  datetime.date(2010, 3, 18): 0.7153,\n", "  datetime.date(2010, 3, 19): 0.7088,\n", "  datetime.date(2010, 3, 22): 0.70595,\n", "  datetime.date(2010, 3, 23): 0.70485,\n", "  datetime.date(2010, 3, 24): 0.7026,\n", "  datetime.date(2010, 3, 25): 0.70535,\n", "  datetime.date(2010, 3, 26): 0.70215,\n", "  datetime.date(2010, 3, 29): 0.7081,\n", "  datetime.date(2010, 3, 30): 0.7092,\n", "  datetime.date(2010, 3, 31): 0.7096,\n", "  datetime.date(2010, 4, 1): 0.7068,\n", "  datetime.date(2010, 4, 2): 0.70545,\n", "  datetime.date(2010, 4, 5): 0.70395,\n", "  datetime.date(2010, 4, 6): 0.70615,\n", "  datetime.date(2010, 4, 7): 0.7085,\n", "  datetime.date(2010, 4, 8): 0.7067,\n", "  datetime.date(2010, 4, 9): 0.7145,\n", "  datetime.date(2010, 4, 12): 0.71255,\n", "  datetime.date(2010, 4, 13): 0.71075,\n", "  datetime.date(2010, 4, 14): 0.7139,\n", "  datetime.date(2010, 4, 15): 0.7121,\n", "  datetime.date(2010, 4, 16): 0.7081,\n", "  datetime.date(2010, 4, 19): 0.70925,\n", "  datetime.date(2010, 4, 20): 0.71115,\n", "  datetime.date(2010, 4, 21): 0.70985,\n", "  datetime.date(2010, 4, 22): 0.71105,\n", "  datetime.date(2010, 4, 23): 0.7157,\n", "  datetime.date(2010, 4, 26): 0.7226,\n", "  datetime.date(2010, 4, 27): 0.7118,\n", "  datetime.date(2010, 4, 28): 0.72095,\n", "  datetime.date(2010, 4, 29): 0.72335,\n", "  datetime.date(2010, 4, 30): 0.72785,\n", "  datetime.date(2010, 5, 3): 0.7322,\n", "  datetime.date(2010, 5, 4): 0.71895,\n", "  datetime.date(2010, 5, 5): 0.71625,\n", "  datetime.date(2010, 5, 6): 0.7086,\n", "  datetime.date(2010, 5, 7): 0.7144,\n", "  datetime.date(2010, 5, 10): 0.7217,\n", "  datetime.date(2010, 5, 11): 0.7188,\n", "  datetime.date(2010, 5, 12): 0.71195,\n", "  datetime.date(2010, 5, 13): 0.7159,\n", "  datetime.date(2010, 5, 14): 0.7066,\n", "  datetime.date(2010, 5, 17): 0.6962,\n", "  datetime.date(2010, 5, 18): 0.69315,\n", "  datetime.date(2010, 5, 19): 0.6743,\n", "  datetime.date(2010, 5, 20): 0.6724,\n", "  datetime.date(2010, 5, 21): 0.6756,\n", "  datetime.date(2010, 5, 24): 0.6737,\n", "  datetime.date(2010, 5, 25): 0.66425,\n", "  datetime.date(2010, 5, 26): 0.66395,\n", "  datetime.date(2010, 5, 27): 0.682025,\n", "  datetime.date(2010, 5, 28): 0.67875,\n", "  datetime.date(2010, 5, 31): 0.6789,\n", "  datetime.date(2010, 6, 1): 0.680275,\n", "  datetime.date(2010, 6, 2): 0.679425,\n", "  datetime.date(2010, 6, 3): 0.683225,\n", "  datetime.date(2010, 6, 4): 0.6687,\n", "  datetime.date(2010, 6, 7): 0.6605,\n", "  datetime.date(2010, 6, 8): 0.663075,\n", "  datetime.date(2010, 6, 9): 0.666,\n", "  datetime.date(2010, 6, 10): 0.68595,\n", "  datetime.date(2010, 6, 11): 0.688225,\n", "  datetime.date(2010, 6, 14): 0.697725,\n", "  datetime.date(2010, 6, 15): 0.69795,\n", "  datetime.date(2010, 6, 16): 0.698325,\n", "  datetime.date(2010, 6, 17): 0.702475,\n", "  datetime.date(2010, 6, 18): 0.7054,\n", "  datetime.date(2010, 6, 21): 0.70875,\n", "  datetime.date(2010, 6, 22): 0.705325,\n", "  datetime.date(2010, 6, 23): 0.7139,\n", "  datetime.date(2010, 6, 24): 0.710425,\n", "  datetime.date(2010, 6, 25): 0.7147,\n", "  datetime.date(2010, 6, 28): 0.70905,\n", "  datetime.date(2010, 6, 29): 0.693025,\n", "  datetime.date(2010, 6, 30): 0.6878,\n", "  datetime.date(2010, 7, 1): 0.68815,\n", "  datetime.date(2010, 7, 2): 0.68795,\n", "  datetime.date(2010, 7, 5): 0.687875,\n", "  datetime.date(2010, 7, 6): 0.6923,\n", "  datetime.date(2010, 7, 7): 0.7041,\n", "  datetime.date(2010, 7, 8): 0.70825,\n", "  datetime.date(2010, 7, 9): 0.709925,\n", "  datetime.date(2010, 7, 12): 0.710225,\n", "  datetime.date(2010, 7, 13): 0.718925,\n", "  datetime.date(2010, 7, 14): 0.722825,\n", "  datetime.date(2010, 7, 15): 0.72715,\n", "  datetime.date(2010, 7, 16): 0.7126,\n", "  datetime.date(2010, 7, 19): 0.707125,\n", "  datetime.date(2010, 7, 20): 0.7151,\n", "  datetime.date(2010, 7, 21): 0.71235,\n", "  datetime.date(2010, 7, 22): 0.7255,\n", "  datetime.date(2010, 7, 23): 0.726475,\n", "  datetime.date(2010, 7, 26): 0.733775,\n", "  datetime.date(2010, 7, 27): 0.7324,\n", "  datetime.date(2010, 7, 28): 0.7281,\n", "  datetime.date(2010, 7, 29): 0.725575,\n", "  datetime.date(2010, 7, 30): 0.724525,\n", "  datetime.date(2010, 8, 2): 0.732675,\n", "  datetime.date(2010, 8, 3): 0.73515,\n", "  datetime.date(2010, 8, 4): 0.73545,\n", "  datetime.date(2010, 8, 5): 0.7285,\n", "  datetime.date(2010, 8, 6): 0.731875,\n", "  datetime.date(2010, 8, 9): 0.72885,\n", "  datetime.date(2010, 8, 10): 0.7254,\n", "  datetime.date(2010, 8, 11): 0.71585,\n", "  datetime.date(2010, 8, 12): 0.7074,\n", "  datetime.date(2010, 8, 13): 0.70535,\n", "  datetime.date(2010, 8, 16): 0.706875,\n", "  datetime.date(2010, 8, 17): 0.71305,\n", "  datetime.date(2010, 8, 18): 0.7152,\n", "  datetime.date(2010, 8, 19): 0.7065,\n", "  datetime.date(2010, 8, 20): 0.70645,\n", "  datetime.date(2010, 8, 23): 0.707875,\n", "  datetime.date(2010, 8, 24): 0.7048,\n", "  datetime.date(2010, 8, 25): 0.6977,\n", "  datetime.date(2010, 8, 26): 0.702625,\n", "  datetime.date(2010, 8, 27): 0.7117,\n", "  datetime.date(2010, 8, 30): 0.70855,\n", "  datetime.date(2010, 8, 31): 0.696825,\n", "  datetime.date(2010, 9, 1): 0.711425,\n", "  datetime.date(2010, 9, 2): 0.7148,\n", "  datetime.date(2010, 9, 3): 0.72085,\n", "  datetime.date(2010, 9, 6): 0.722725,\n", "  datetime.date(2010, 9, 7): 0.720475,\n", "  datetime.date(2010, 9, 8): 0.72295,\n", "  datetime.date(2010, 9, 9): 0.726225,\n", "  datetime.date(2010, 9, 10): 0.7283,\n", "  datetime.date(2010, 9, 13): 0.73345,\n", "  datetime.date(2010, 9, 14): 0.737225,\n", "  datetime.date(2010, 9, 15): 0.7312,\n", "  datetime.date(2010, 9, 16): 0.723925,\n", "  datetime.date(2010, 9, 17): 0.726875,\n", "  datetime.date(2010, 9, 20): 0.729675,\n", "  datetime.date(2010, 9, 21): 0.734675,\n", "  datetime.date(2010, 9, 22): 0.737375,\n", "  datetime.date(2010, 9, 23): 0.731825,\n", "  datetime.date(2010, 9, 24): 0.733025,\n", "  datetime.date(2010, 9, 27): 0.73615,\n", "  datetime.date(2010, 9, 28): 0.73885,\n", "  datetime.date(2010, 9, 29): 0.73895,\n", "  datetime.date(2010, 9, 30): 0.7351,\n", "  datetime.date(2010, 10, 1): 0.74435,\n", "  datetime.date(2010, 10, 4): 0.7413,\n", "  datetime.date(2010, 10, 5): 0.7489,\n", "  datetime.date(2010, 10, 6): 0.752475,\n", "  datetime.date(2010, 10, 7): 0.75,\n", "  datetime.date(2010, 10, 8): 0.75355,\n", "  datetime.date(2010, 10, 11): 0.753175,\n", "  datetime.date(2010, 10, 12): 0.75475,\n", "  datetime.date(2010, 10, 13): 0.761825,\n", "  datetime.date(2010, 10, 14): 0.755875,\n", "  datetime.date(2010, 10, 15): 0.7539,\n", "  datetime.date(2010, 10, 18): 0.7589,\n", "  datetime.date(2010, 10, 19): 0.745225,\n", "  datetime.date(2010, 10, 20): 0.754875,\n", "  datetime.date(2010, 10, 21): 0.74645,\n", "  datetime.date(2010, 10, 22): 0.7469,\n", "  datetime.date(2010, 10, 25): 0.75315,\n", "  datetime.date(2010, 10, 26): 0.748725,\n", "  datetime.date(2010, 10, 27): 0.742875,\n", "  datetime.date(2010, 10, 28): 0.753725,\n", "  datetime.date(2010, 10, 29): 0.761325,\n", "  datetime.date(2010, 11, 1): 0.766425,\n", "  datetime.date(2010, 11, 2): 0.77185,\n", "  datetime.date(2010, 11, 3): 0.7776,\n", "  datetime.date(2010, 11, 4): 0.79555,\n", "  datetime.date(2010, 11, 5): 0.794475,\n", "  datetime.date(2010, 11, 8): 0.7881,\n", "  datetime.date(2010, 11, 9): 0.78345,\n", "  datetime.date(2010, 11, 10): 0.783725,\n", "  datetime.date(2010, 11, 11): 0.78035,\n", "  datetime.date(2010, 11, 12): 0.773775,\n", "  datetime.date(2010, 11, 15): 0.77485,\n", "  datetime.date(2010, 11, 16): 0.7676,\n", "  datetime.date(2010, 11, 17): 0.770325,\n", "  datetime.date(2010, 11, 18): 0.777775,\n", "  datetime.date(2010, 11, 19): 0.778375,\n", "  datetime.date(2010, 11, 22): 0.7728,\n", "  datetime.date(2010, 11, 23): 0.7589,\n", "  datetime.date(2010, 11, 24): 0.761075,\n", "  datetime.date(2010, 11, 25): 0.7607,\n", "  datetime.date(2010, 11, 26): 0.750375,\n", "  datetime.date(2010, 11, 29): 0.747225,\n", "  datetime.date(2010, 11, 30): 0.744125,\n", "  datetime.date(2010, 12, 1): 0.750475,\n", "  datetime.date(2010, 12, 2): 0.7543,\n", "  datetime.date(2010, 12, 3): 0.7641,\n", "  datetime.date(2010, 12, 6): 0.761975,\n", "  datetime.date(2010, 12, 7): 0.75875,\n", "  datetime.date(2010, 12, 8): 0.749375,\n", "  datetime.date(2010, 12, 9): 0.74725,\n", "  datetime.date(2010, 12, 10): 0.747225,\n", "  datetime.date(2010, 12, 13): 0.7562,\n", "  datetime.date(2010, 12, 14): 0.752975,\n", "  datetime.date(2010, 12, 15): 0.73855,\n", "  datetime.date(2010, 12, 16): 0.73775,\n", "  datetime.date(2010, 12, 17): 0.7379,\n", "  datetime.date(2010, 12, 20): 0.742675,\n", "  datetime.date(2010, 12, 21): 0.74245,\n", "  datetime.date(2010, 12, 22): 0.741875,\n", "  datetime.date(2010, 12, 23): 0.74745,\n", "  datetime.date(2010, 12, 24): 0.748025,\n", "  datetime.date(2010, 12, 27): 0.7503,\n", "  datetime.date(2010, 12, 28): 0.75555,\n", "  datetime.date(2010, 12, 29): 0.768525,\n", "  datetime.date(2010, 12, 30): 0.770625,\n", "  datetime.date(2010, 12, 31): 0.7792,\n", "  datetime.date(2011, 1, 3): 0.773275,\n", "  datetime.date(2011, 1, 4): 0.767625,\n", "  datetime.date(2011, 1, 5): 0.757125,\n", "  datetime.date(2011, 1, 6): 0.756675,\n", "  datetime.date(2011, 1, 7): 0.759925,\n", "  datetime.date(2011, 1, 10): 0.763575,\n", "  datetime.date(2011, 1, 11): 0.7606,\n", "  datetime.date(2011, 1, 12): 0.76385,\n", "  datetime.date(2011, 1, 13): 0.77135,\n", "  datetime.date(2011, 1, 14): 0.766,\n", "  datetime.date(2011, 1, 17): 0.7723,\n", "  datetime.date(2011, 1, 18): 0.771875,\n", "  datetime.date(2011, 1, 19): 0.768875,\n", "  datetime.date(2011, 1, 20): 0.756925,\n", "  datetime.date(2011, 1, 21): 0.7585,\n", "  datetime.date(2011, 1, 24): 0.76325,\n", "  datetime.date(2011, 1, 25): 0.768725,\n", "  datetime.date(2011, 1, 26): 0.7728,\n", "  datetime.date(2011, 1, 27): 0.772175,\n", "  datetime.date(2011, 1, 28): 0.773675,\n", "  datetime.date(2011, 1, 31): 0.772975,\n", "  datetime.date(2011, 2, 1): 0.781375,\n", "  datetime.date(2011, 2, 2): 0.773325,\n", "  datetime.date(2011, 2, 3): 0.773375,\n", "  datetime.date(2011, 2, 4): 0.769925,\n", "  datetime.date(2011, 2, 7): 0.769875,\n", "  datetime.date(2011, 2, 8): 0.775025,\n", "  datetime.date(2011, 2, 9): 0.772325,\n", "  datetime.date(2011, 2, 10): 0.764425,\n", "  datetime.date(2011, 2, 11): 0.760525,\n", "  datetime.date(2011, 2, 14): 0.7567,\n", "  datetime.date(2011, 2, 15): 0.75185,\n", "  datetime.date(2011, 2, 16): 0.7547,\n", "  datetime.date(2011, 2, 17): 0.75895,\n", "  datetime.date(2011, 2, 18): 0.76155,\n", "  datetime.date(2011, 2, 21): 0.763825,\n", "  datetime.date(2011, 2, 22): 0.746425,\n", "  datetime.date(2011, 2, 23): 0.74575,\n", "  datetime.date(2011, 2, 24): 0.7474,\n", "  datetime.date(2011, 2, 25): 0.75165,\n", "  datetime.date(2011, 2, 28): 0.752425,\n", "  datetime.date(2011, 3, 1): 0.747225,\n", "  datetime.date(2011, 3, 2): 0.7432,\n", "  datetime.date(2011, 3, 3): 0.740975,\n", "  datetime.date(2011, 3, 4): 0.7379,\n", "  datetime.date(2011, 3, 7): 0.736925,\n", "  datetime.date(2011, 3, 8): 0.739475,\n", "  datetime.date(2011, 3, 9): 0.7366,\n", "  datetime.date(2011, 3, 10): 0.735475,\n", "  datetime.date(2011, 3, 11): 0.742625,\n", "  datetime.date(2011, 3, 14): 0.739375,\n", "  datetime.date(2011, 3, 15): 0.73175,\n", "  datetime.date(2011, 3, 16): 0.72815,\n", "  datetime.date(2011, 3, 17): 0.717875,\n", "  datetime.date(2011, 3, 18): 0.731025,\n", "  datetime.date(2011, 3, 21): 0.735375,\n", "  datetime.date(2011, 3, 22): 0.7401,\n", "  datetime.date(2011, 3, 23): 0.7406,\n", "  datetime.date(2011, 3, 24): 0.748975,\n", "  datetime.date(2011, 3, 25): 0.75345,\n", "  datetime.date(2011, 3, 28): 0.75125,\n", "  datetime.date(2011, 3, 29): 0.7562,\n", "  datetime.date(2011, 3, 30): 0.7623,\n", "  datetime.date(2011, 3, 31): 0.76145,\n", "  datetime.date(2011, 4, 1): 0.767725,\n", "  datetime.date(2011, 4, 4): 0.76815,\n", "  datetime.date(2011, 4, 5): 0.768425,\n", "  datetime.date(2011, 4, 6): 0.779525,\n", "  datetime.date(2011, 4, 7): 0.7789,\n", "  datetime.date(2011, 4, 8): 0.7828,\n", "  datetime.date(2011, 4, 11): 0.7797,\n", "  datetime.date(2011, 4, 12): 0.7834,\n", "  datetime.date(2011, 4, 13): 0.789575,\n", "  datetime.date(2011, 4, 14): 0.79375,\n", "  datetime.date(2011, 4, 15): 0.7995,\n", "  datetime.date(2011, 4, 18): 0.786825,\n", "  datetime.date(2011, 4, 19): 0.7888,\n", "  datetime.date(2011, 4, 20): 0.79765,\n", "  datetime.date(2011, 4, 21): 0.800475,\n", "  datetime.date(2011, 4, 22): 0.802175,\n", "  datetime.date(2011, 4, 25): 0.7993,\n", "  datetime.date(2011, 4, 26): 0.80555,\n", "  datetime.date(2011, 4, 27): 0.80805,\n", "  datetime.date(2011, 4, 28): 0.80255,\n", "  datetime.date(2011, 4, 29): 0.809875,\n", "  datetime.date(2011, 5, 2): 0.8064,\n", "  datetime.date(2011, 5, 3): 0.798625,\n", "  datetime.date(2011, 5, 4): 0.78975,\n", "  datetime.date(2011, 5, 5): 0.78535,\n", "  datetime.date(2011, 5, 6): 0.79095,\n", "  datetime.date(2011, 5, 9): 0.79555,\n", "  datetime.date(2011, 5, 10): 0.7953,\n", "  datetime.date(2011, 5, 11): 0.789,\n", "  datetime.date(2011, 5, 12): 0.7956,\n", "  datetime.date(2011, 5, 13): 0.787325,\n", "  datetime.date(2011, 5, 16): 0.7798,\n", "  datetime.date(2011, 5, 17): 0.78485,\n", "  datetime.date(2011, 5, 18): 0.789075,\n", "  datetime.date(2011, 5, 19): 0.790875,\n", "  datetime.date(2011, 5, 20): 0.795975,\n", "  datetime.date(2011, 5, 23): 0.790325,\n", "  datetime.date(2011, 5, 24): 0.79615,\n", "  datetime.date(2011, 5, 25): 0.7987,\n", "  datetime.date(2011, 5, 26): 0.811425,\n", "  datetime.date(2011, 5, 27): 0.819275,\n", "  datetime.date(2011, 5, 30): 0.816325,\n", "  datetime.date(2011, 5, 31): 0.823875,\n", "  datetime.date(2011, 6, 1): 0.8146,\n", "  datetime.date(2011, 6, 2): 0.815625,\n", "  datetime.date(2011, 6, 3): 0.815925,\n", "  datetime.date(2011, 6, 6): 0.814475,\n", "  datetime.date(2011, 6, 7): 0.820425,\n", "  datetime.date(2011, 6, 8): 0.8152,\n", "  datetime.date(2011, 6, 9): 0.82475,\n", "  datetime.date(2011, 6, 10): 0.821475,\n", "  datetime.date(2011, 6, 13): 0.815475,\n", "  datetime.date(2011, 6, 14): 0.8181,\n", "  datetime.date(2011, 6, 15): 0.8066,\n", "  datetime.date(2011, 6, 16): 0.804875,\n", "  datetime.date(2011, 6, 17): 0.81275,\n", "  datetime.date(2011, 6, 20): 0.80995,\n", "  datetime.date(2011, 6, 21): 0.8126,\n", "  datetime.date(2011, 6, 22): 0.8146,\n", "  datetime.date(2011, 6, 23): 0.814,\n", "  datetime.date(2011, 6, 24): 0.81145,\n", "  datetime.date(2011, 6, 27): 0.805525,\n", "  datetime.date(2011, 6, 28): 0.8121,\n", "  datetime.date(2011, 6, 29): 0.825175,\n", "  datetime.date(2011, 6, 30): 0.829175,\n", "  datetime.date(2011, 7, 1): 0.82735,\n", "  datetime.date(2011, 7, 4): 0.829575,\n", "  datetime.date(2011, 7, 5): 0.82515,\n", "  datetime.date(2011, 7, 6): 0.827,\n", "  datetime.date(2011, 7, 7): 0.8334,\n", "  datetime.date(2011, 7, 8): 0.83795,\n", "  datetime.date(2011, 7, 11): 0.829175,\n", "  datetime.date(2011, 7, 12): 0.818025,\n", "  datetime.date(2011, 7, 13): 0.83785,\n", "  datetime.date(2011, 7, 14): 0.842125,\n", "  datetime.date(2011, 7, 15): 0.8457,\n", "  datetime.date(2011, 7, 18): 0.845,\n", "  datetime.date(2011, 7, 19): 0.85585,\n", "  datetime.date(2011, 7, 20): 0.8563,\n", "  datetime.date(2011, 7, 21): 0.8631,\n", "  datetime.date(2011, 7, 22): 0.864775,\n", "  datetime.date(2011, 7, 25): 0.864125,\n", "  datetime.date(2011, 7, 26): 0.870625,\n", "  datetime.date(2011, 7, 27): 0.87,\n", "  datetime.date(2011, 7, 28): 0.87125,\n", "  datetime.date(2011, 7, 29): 0.87935,\n", "  datetime.date(2011, 8, 1): 0.876625,\n", "  datetime.date(2011, 8, 2): 0.8666,\n", "  datetime.date(2011, 8, 3): 0.863525,\n", "  datetime.date(2011, 8, 4): 0.83625,\n", "  datetime.date(2011, 8, 5): 0.843425,\n", "  datetime.date(2011, 8, 8): 0.8209,\n", "  datetime.date(2011, 8, 9): 0.83735,\n", "  datetime.date(2011, 8, 10): 0.8109,\n", "  datetime.date(2011, 8, 11): 0.831825,\n", "  datetime.date(2011, 8, 12): 0.83235,\n", "  datetime.date(2011, 8, 15): 0.832825,\n", "  datetime.date(2011, 8, 16): 0.8362,\n", "  datetime.date(2011, 8, 17): 0.83755,\n", "  datetime.date(2011, 8, 18): 0.821425,\n", "  datetime.date(2011, 8, 19): 0.818125,\n", "  datetime.date(2011, 8, 22): 0.825225,\n", "  datetime.date(2011, 8, 23): 0.833525,\n", "  datetime.date(2011, 8, 24): 0.827125,\n", "  datetime.date(2011, 8, 25): 0.82825,\n", "  datetime.date(2011, 8, 26): 0.840725,\n", "  datetime.date(2011, 8, 29): 0.844725,\n", "  datetime.date(2011, 8, 30): 0.85445,\n", "  datetime.date(2011, 8, 31): 0.8527,\n", "  datetime.date(2011, 9, 1): 0.850925,\n", "  datetime.date(2011, 9, 2): 0.848,\n", "  datetime.date(2011, 9, 5): 0.83135,\n", "  datetime.date(2011, 9, 6): 0.821225,\n", "  datetime.date(2011, 9, 7): 0.831125,\n", "  datetime.date(2011, 9, 8): 0.83285,\n", "  datetime.date(2011, 9, 9): 0.822025,\n", "  datetime.date(2011, 9, 12): 0.81635,\n", "  datetime.date(2011, 9, 13): 0.82505,\n", "  datetime.date(2011, 9, 14): 0.81905,\n", "  datetime.date(2011, 9, 15): 0.8234,\n", "  datetime.date(2011, 9, 16): 0.829225,\n", "  datetime.date(2011, 9, 19): 0.819675,\n", "  datetime.date(2011, 9, 20): 0.8243,\n", "  datetime.date(2011, 9, 21): 0.807275,\n", "  datetime.date(2011, 9, 22): 0.776475,\n", "  datetime.date(2011, 9, 23): 0.77635,\n", "  datetime.date(2011, 9, 26): 0.7736,\n", "  datetime.date(2011, 9, 27): 0.788575,\n", "  datetime.date(2011, 9, 28): 0.77805,\n", "  datetime.date(2011, 9, 29): 0.767625,\n", "  datetime.date(2011, 9, 30): 0.7614,\n", "  datetime.date(2011, 10, 3): 0.75515,\n", "  datetime.date(2011, 10, 4): 0.760075,\n", "  ...},\n", " 'USDCAD': {datetime.date(2007, 11, 2): 0.9347,\n", "  datetime.date(2007, 11, 9): 0.94465,\n", "  datetime.date(2007, 11, 29): 0.99825,\n", "  datetime.date(2007, 11, 30): 0.99865,\n", "  datetime.date(2007, 12, 3): 0.99925,\n", "  datetime.date(2007, 12, 4): 1.0105,\n", "  datetime.date(2007, 12, 5): 1.01165,\n", "  datetime.date(2007, 12, 6): 1.00825,\n", "  datetime.date(2007, 12, 7): 1.00525,\n", "  datetime.date(2007, 12, 10): 1.0072,\n", "  datetime.date(2007, 12, 11): 1.015,\n", "  datetime.date(2007, 12, 12): 1.01235,\n", "  datetime.date(2007, 12, 13): 1.02085,\n", "  datetime.date(2007, 12, 14): 1.01715,\n", "  datetime.date(2007, 12, 17): 1.0053,\n", "  datetime.date(2007, 12, 18): 1.0058,\n", "  datetime.date(2007, 12, 19): 1.0024,\n", "  datetime.date(2007, 12, 20): 1.00145,\n", "  datetime.date(2007, 12, 21): 0.9916,\n", "  datetime.date(2007, 12, 24): 0.98585,\n", "  datetime.date(2007, 12, 26): 0.98155,\n", "  datetime.date(2007, 12, 27): 0.98005,\n", "  datetime.date(2007, 12, 28): 0.9815,\n", "  datetime.date(2007, 12, 31): 0.9984,\n", "  datetime.date(2008, 1, 2): 0.9938,\n", "  datetime.date(2008, 1, 3): 0.98965,\n", "  datetime.date(2008, 1, 4): 1.00285,\n", "  datetime.date(2008, 1, 7): 1.00475,\n", "  datetime.date(2008, 1, 8): 1.00645,\n", "  datetime.date(2008, 1, 9): 1.0101,\n", "  datetime.date(2008, 1, 10): 1.01015,\n", "  datetime.date(2008, 1, 11): 1.0191,\n", "  datetime.date(2008, 1, 14): 1.01705,\n", "  datetime.date(2008, 1, 15): 1.0184,\n", "  datetime.date(2008, 1, 16): 1.02515,\n", "  datetime.date(2008, 1, 17): 1.0295,\n", "  datetime.date(2008, 1, 18): 1.02725,\n", "  datetime.date(2008, 1, 21): 1.0349,\n", "  datetime.date(2008, 1, 22): 1.02945,\n", "  datetime.date(2008, 1, 23): 1.02415,\n", "  datetime.date(2008, 1, 24): 1.00365,\n", "  datetime.date(2008, 1, 25): 1.00805,\n", "  datetime.date(2008, 1, 28): 1.00355,\n", "  datetime.date(2008, 1, 29): 0.9984,\n", "  datetime.date(2008, 1, 30): 0.99315,\n", "  datetime.date(2008, 1, 31): 1.00285,\n", "  datetime.date(2008, 2, 1): 0.99515,\n", "  datetime.date(2008, 2, 4): 0.99345,\n", "  datetime.date(2008, 2, 5): 1.008,\n", "  datetime.date(2008, 2, 6): 1.00605,\n", "  datetime.date(2008, 2, 7): 1.00875,\n", "  datetime.date(2008, 2, 8): 0.9989,\n", "  datetime.date(2008, 2, 11): 1.00165,\n", "  datetime.date(2008, 2, 12): 1.0017,\n", "  datetime.date(2008, 2, 13): 0.99705,\n", "  datetime.date(2008, 2, 14): 1.00015,\n", "  datetime.date(2008, 2, 15): 1.0071,\n", "  datetime.date(2008, 2, 18): 1.0076,\n", "  datetime.date(2008, 2, 19): 1.01715,\n", "  datetime.date(2008, 2, 20): 1.01285,\n", "  datetime.date(2008, 2, 21): 1.01125,\n", "  datetime.date(2008, 2, 22): 1.0128,\n", "  datetime.date(2008, 2, 25): 0.9961,\n", "  datetime.date(2008, 2, 26): 0.98145,\n", "  datetime.date(2008, 2, 27): 0.98025,\n", "  datetime.date(2008, 2, 28): 0.9752,\n", "  datetime.date(2008, 2, 29): 0.9878,\n", "  datetime.date(2008, 3, 3): 0.99005,\n", "  datetime.date(2008, 3, 4): 0.99355,\n", "  datetime.date(2008, 3, 5): 0.98435,\n", "  datetime.date(2008, 3, 6): 0.9856,\n", "  datetime.date(2008, 3, 7): 0.9909,\n", "  datetime.date(2008, 3, 10): 0.99635,\n", "  datetime.date(2008, 3, 11): 0.9921,\n", "  datetime.date(2008, 3, 12): 0.9896,\n", "  datetime.date(2008, 3, 13): 0.9843,\n", "  datetime.date(2008, 3, 14): 0.98915,\n", "  datetime.date(2008, 3, 17): 0.99795,\n", "  datetime.date(2008, 3, 18): 0.99225,\n", "  datetime.date(2008, 3, 19): 1.0137,\n", "  datetime.date(2008, 3, 20): 1.02355,\n", "  datetime.date(2008, 3, 21): 1.0238,\n", "  datetime.date(2008, 3, 24): 1.0173,\n", "  datetime.date(2008, 3, 25): 1.0165,\n", "  datetime.date(2008, 3, 26): 1.0179,\n", "  datetime.date(2008, 3, 27): 1.0187,\n", "  datetime.date(2008, 3, 28): 1.02225,\n", "  datetime.date(2008, 3, 31): 1.0253,\n", "  datetime.date(2008, 4, 1): 1.02125,\n", "  datetime.date(2008, 4, 2): 1.01635,\n", "  datetime.date(2008, 4, 3): 1.00465,\n", "  datetime.date(2008, 4, 4): 1.0085,\n", "  datetime.date(2008, 4, 7): 1.01415,\n", "  datetime.date(2008, 4, 8): 1.0133,\n", "  datetime.date(2008, 4, 9): 1.0183,\n", "  datetime.date(2008, 4, 10): 1.0195,\n", "  datetime.date(2008, 4, 11): 1.0234,\n", "  datetime.date(2008, 4, 14): 1.01895,\n", "  datetime.date(2008, 4, 15): 1.01815,\n", "  datetime.date(2008, 4, 16): 1.0009,\n", "  datetime.date(2008, 4, 17): 1.0104,\n", "  datetime.date(2008, 4, 18): 1.00455,\n", "  datetime.date(2008, 4, 21): 1.0053,\n", "  datetime.date(2008, 4, 22): 1.00805,\n", "  datetime.date(2008, 4, 23): 1.017,\n", "  datetime.date(2008, 4, 24): 1.01425,\n", "  datetime.date(2008, 4, 25): 1.0138,\n", "  datetime.date(2008, 4, 28): 1.0126,\n", "  datetime.date(2008, 4, 29): 1.01265,\n", "  datetime.date(2008, 4, 30): 1.00795,\n", "  datetime.date(2008, 5, 1): 1.0204,\n", "  datetime.date(2008, 5, 2): 1.01945,\n", "  datetime.date(2008, 5, 5): 1.01275,\n", "  datetime.date(2008, 5, 6): 1.00245,\n", "  datetime.date(2008, 5, 7): 1.00935,\n", "  datetime.date(2008, 5, 8): 1.0172,\n", "  datetime.date(2008, 5, 9): 1.0053,\n", "  datetime.date(2008, 5, 12): 1.00445,\n", "  datetime.date(2008, 5, 13): 1.00375,\n", "  datetime.date(2008, 5, 14): 1.00365,\n", "  datetime.date(2008, 5, 15): 0.99975,\n", "  datetime.date(2008, 5, 16): 0.9992,\n", "  datetime.date(2008, 5, 19): 0.9915,\n", "  datetime.date(2008, 5, 20): 0.99235,\n", "  datetime.date(2008, 5, 21): 0.98375,\n", "  datetime.date(2008, 5, 22): 0.9846,\n", "  datetime.date(2008, 5, 23): 0.98975,\n", "  datetime.date(2008, 5, 26): 0.9911,\n", "  datetime.date(2008, 5, 27): 0.9938,\n", "  datetime.date(2008, 5, 28): 0.99005,\n", "  datetime.date(2008, 5, 29): 0.98765,\n", "  datetime.date(2008, 5, 30): 0.9934,\n", "  datetime.date(2008, 6, 2): 1.0013,\n", "  datetime.date(2008, 6, 3): 1.0089,\n", "  datetime.date(2008, 6, 4): 1.0165,\n", "  datetime.date(2008, 6, 5): 1.0178,\n", "  datetime.date(2008, 6, 6): 1.01955,\n", "  datetime.date(2008, 6, 9): 1.02325,\n", "  datetime.date(2008, 6, 10): 1.022,\n", "  datetime.date(2008, 6, 11): 1.01985,\n", "  datetime.date(2008, 6, 12): 1.02405,\n", "  datetime.date(2008, 6, 13): 1.0293,\n", "  datetime.date(2008, 6, 16): 1.02365,\n", "  datetime.date(2008, 6, 17): 1.01865,\n", "  datetime.date(2008, 6, 18): 1.01715,\n", "  datetime.date(2008, 6, 19): 1.0155,\n", "  datetime.date(2008, 6, 20): 1.016,\n", "  datetime.date(2008, 6, 23): 1.0158,\n", "  datetime.date(2008, 6, 24): 1.01125,\n", "  datetime.date(2008, 6, 25): 1.00955,\n", "  datetime.date(2008, 6, 26): 1.0141,\n", "  datetime.date(2008, 6, 27): 1.01065,\n", "  datetime.date(2008, 6, 30): 1.0215,\n", "  datetime.date(2008, 7, 1): 1.02215,\n", "  datetime.date(2008, 7, 2): 1.01245,\n", "  datetime.date(2008, 7, 3): 1.018,\n", "  datetime.date(2008, 7, 4): 1.019,\n", "  datetime.date(2008, 7, 7): 1.0169,\n", "  datetime.date(2008, 7, 8): 1.0191,\n", "  datetime.date(2008, 7, 9): 1.01165,\n", "  datetime.date(2008, 7, 10): 1.0083,\n", "  datetime.date(2008, 7, 11): 1.009,\n", "  datetime.date(2008, 7, 14): 1.00615,\n", "  datetime.date(2008, 7, 15): 1.00155,\n", "  datetime.date(2008, 7, 16): 1.00195,\n", "  datetime.date(2008, 7, 17): 1.00575,\n", "  datetime.date(2008, 7, 18): 1.00545,\n", "  datetime.date(2008, 7, 21): 0.99985,\n", "  datetime.date(2008, 7, 22): 1.00835,\n", "  datetime.date(2008, 7, 23): 1.0105,\n", "  datetime.date(2008, 7, 24): 1.0146,\n", "  datetime.date(2008, 7, 25): 1.0197,\n", "  datetime.date(2008, 7, 28): 1.02265,\n", "  datetime.date(2008, 7, 29): 1.0239,\n", "  datetime.date(2008, 7, 30): 1.02325,\n", "  datetime.date(2008, 7, 31): 1.02465,\n", "  datetime.date(2008, 8, 1): 1.0272,\n", "  datetime.date(2008, 8, 4): 1.0365,\n", "  datetime.date(2008, 8, 5): 1.04335,\n", "  datetime.date(2008, 8, 6): 1.04905,\n", "  datetime.date(2008, 8, 7): 1.053,\n", "  datetime.date(2008, 8, 8): 1.06665,\n", "  datetime.date(2008, 8, 11): 1.06915,\n", "  datetime.date(2008, 8, 12): 1.0637,\n", "  datetime.date(2008, 8, 13): 1.06265,\n", "  datetime.date(2008, 8, 14): 1.06355,\n", "  datetime.date(2008, 8, 15): 1.0592,\n", "  datetime.date(2008, 8, 18): 1.0645,\n", "  datetime.date(2008, 8, 19): 1.0613,\n", "  datetime.date(2008, 8, 20): 1.05985,\n", "  datetime.date(2008, 8, 21): 1.043,\n", "  datetime.date(2008, 8, 22): 1.0472,\n", "  datetime.date(2008, 8, 25): 1.0512,\n", "  datetime.date(2008, 8, 26): 1.0485,\n", "  datetime.date(2008, 8, 27): 1.0462,\n", "  datetime.date(2008, 8, 28): 1.0511,\n", "  datetime.date(2008, 8, 29): 1.06375,\n", "  datetime.date(2008, 9, 1): 1.067,\n", "  datetime.date(2008, 9, 2): 1.06735,\n", "  datetime.date(2008, 9, 3): 1.06255,\n", "  datetime.date(2008, 9, 4): 1.06965,\n", "  datetime.date(2008, 9, 5): 1.06245,\n", "  datetime.date(2008, 9, 8): 1.06495,\n", "  datetime.date(2008, 9, 9): 1.0714,\n", "  datetime.date(2008, 9, 10): 1.07135,\n", "  datetime.date(2008, 9, 11): 1.07525,\n", "  datetime.date(2008, 9, 12): 1.06035,\n", "  datetime.date(2008, 9, 15): 1.07055,\n", "  datetime.date(2008, 9, 16): 1.0686,\n", "  datetime.date(2008, 9, 17): 1.07225,\n", "  datetime.date(2008, 9, 18): 1.061,\n", "  datetime.date(2008, 9, 19): 1.0474,\n", "  datetime.date(2008, 9, 22): 1.0357,\n", "  datetime.date(2008, 9, 23): 1.03825,\n", "  datetime.date(2008, 9, 24): 1.0385,\n", "  datetime.date(2008, 9, 25): 1.03465,\n", "  datetime.date(2008, 9, 26): 1.03355,\n", "  datetime.date(2008, 9, 29): 1.0454,\n", "  datetime.date(2008, 9, 30): 1.0643,\n", "  datetime.date(2008, 10, 1): 1.06245,\n", "  datetime.date(2008, 10, 2): 1.07955,\n", "  datetime.date(2008, 10, 3): 1.0826,\n", "  datetime.date(2008, 10, 6): 1.09925,\n", "  datetime.date(2008, 10, 7): 1.10575,\n", "  datetime.date(2008, 10, 8): 1.1278,\n", "  datetime.date(2008, 10, 9): 1.1499,\n", "  datetime.date(2008, 10, 10): 1.17325,\n", "  datetime.date(2008, 10, 13): 1.1474,\n", "  datetime.date(2008, 10, 14): 1.1625,\n", "  datetime.date(2008, 10, 15): 1.19145,\n", "  datetime.date(2008, 10, 16): 1.17855,\n", "  datetime.date(2008, 10, 17): 1.185,\n", "  datetime.date(2008, 10, 20): 1.1912,\n", "  datetime.date(2008, 10, 21): 1.21385,\n", "  datetime.date(2008, 10, 22): 1.2538,\n", "  datetime.date(2008, 10, 23): 1.2472,\n", "  datetime.date(2008, 10, 24): 1.27725,\n", "  datetime.date(2008, 10, 27): 1.29485,\n", "  datetime.date(2008, 10, 28): 1.27305,\n", "  datetime.date(2008, 10, 29): 1.22365,\n", "  datetime.date(2008, 10, 30): 1.2004,\n", "  datetime.date(2008, 10, 31): 1.21245,\n", "  datetime.date(2008, 11, 3): 1.1815,\n", "  datetime.date(2008, 11, 4): 1.15015,\n", "  datetime.date(2008, 11, 5): 1.16985,\n", "  datetime.date(2008, 11, 6): 1.1982,\n", "  datetime.date(2008, 11, 7): 1.1893,\n", "  datetime.date(2008, 11, 10): 1.19435,\n", "  datetime.date(2008, 11, 11): 1.20745,\n", "  datetime.date(2008, 11, 12): 1.23815,\n", "  datetime.date(2008, 11, 13): 1.21115,\n", "  datetime.date(2008, 11, 14): 1.23695,\n", "  datetime.date(2008, 11, 17): 1.2258,\n", "  datetime.date(2008, 11, 18): 1.2311,\n", "  datetime.date(2008, 11, 19): 1.25475,\n", "  datetime.date(2008, 11, 20): 1.29705,\n", "  datetime.date(2008, 11, 21): 1.26705,\n", "  datetime.date(2008, 11, 24): 1.2322,\n", "  datetime.date(2008, 11, 25): 1.225,\n", "  datetime.date(2008, 11, 26): 1.23215,\n", "  datetime.date(2008, 11, 27): 1.23185,\n", "  datetime.date(2008, 11, 28): 1.2392,\n", "  datetime.date(2008, 12, 1): 1.2484,\n", "  datetime.date(2008, 12, 2): 1.2466,\n", "  datetime.date(2008, 12, 3): 1.2522,\n", "  datetime.date(2008, 12, 4): 1.27585,\n", "  datetime.date(2008, 12, 5): 1.2712,\n", "  datetime.date(2008, 12, 8): 1.2504,\n", "  datetime.date(2008, 12, 9): 1.2626,\n", "  datetime.date(2008, 12, 10): 1.2569,\n", "  datetime.date(2008, 12, 11): 1.23615,\n", "  datetime.date(2008, 12, 12): 1.24785,\n", "  datetime.date(2008, 12, 15): 1.23375,\n", "  datetime.date(2008, 12, 16): 1.20255,\n", "  datetime.date(2008, 12, 17): 1.19155,\n", "  datetime.date(2008, 12, 18): 1.2041,\n", "  datetime.date(2008, 12, 19): 1.21575,\n", "  datetime.date(2008, 12, 22): 1.2216,\n", "  datetime.date(2008, 12, 23): 1.21915,\n", "  datetime.date(2008, 12, 24): 1.2165,\n", "  datetime.date(2008, 12, 26): 1.22145,\n", "  datetime.date(2008, 12, 29): 1.22025,\n", "  datetime.date(2008, 12, 30): 1.2171,\n", "  datetime.date(2008, 12, 31): 1.2176,\n", "  datetime.date(2009, 1, 2): 1.2059,\n", "  datetime.date(2009, 1, 5): 1.18865,\n", "  datetime.date(2009, 1, 6): 1.18425,\n", "  datetime.date(2009, 1, 7): 1.1943,\n", "  datetime.date(2009, 1, 8): 1.17975,\n", "  datetime.date(2009, 1, 9): 1.18675,\n", "  datetime.date(2009, 1, 12): 1.2185,\n", "  datetime.date(2009, 1, 13): 1.22435,\n", "  datetime.date(2009, 1, 14): 1.24865,\n", "  datetime.date(2009, 1, 15): 1.25275,\n", "  datetime.date(2009, 1, 16): 1.24325,\n", "  datetime.date(2009, 1, 19): 1.25365,\n", "  datetime.date(2009, 1, 20): 1.267,\n", "  datetime.date(2009, 1, 21): 1.2556,\n", "  datetime.date(2009, 1, 22): 1.2534,\n", "  datetime.date(2009, 1, 23): 1.2318,\n", "  datetime.date(2009, 1, 26): 1.22265,\n", "  datetime.date(2009, 1, 27): 1.2305,\n", "  datetime.date(2009, 1, 28): 1.21105,\n", "  datetime.date(2009, 1, 29): 1.2265,\n", "  datetime.date(2009, 1, 30): 1.2287,\n", "  datetime.date(2009, 2, 2): 1.2447,\n", "  datetime.date(2009, 2, 3): 1.22955,\n", "  datetime.date(2009, 2, 4): 1.23225,\n", "  datetime.date(2009, 2, 5): 1.23245,\n", "  datetime.date(2009, 2, 6): 1.2205,\n", "  datetime.date(2009, 2, 9): 1.21745,\n", "  datetime.date(2009, 2, 10): 1.2437,\n", "  datetime.date(2009, 2, 11): 1.23835,\n", "  datetime.date(2009, 2, 12): 1.2438,\n", "  datetime.date(2009, 2, 13): 1.2353,\n", "  datetime.date(2009, 2, 16): 1.2426,\n", "  datetime.date(2009, 2, 17): 1.2653,\n", "  datetime.date(2009, 2, 18): 1.26105,\n", "  datetime.date(2009, 2, 19): 1.2579,\n", "  datetime.date(2009, 2, 20): 1.252,\n", "  datetime.date(2009, 2, 23): 1.2503,\n", "  datetime.date(2009, 2, 24): 1.24225,\n", "  datetime.date(2009, 2, 25): 1.2574,\n", "  datetime.date(2009, 2, 26): 1.2534,\n", "  datetime.date(2009, 2, 27): 1.2759,\n", "  datetime.date(2009, 3, 2): 1.29335,\n", "  datetime.date(2009, 3, 3): 1.29305,\n", "  datetime.date(2009, 3, 4): 1.27315,\n", "  datetime.date(2009, 3, 5): 1.29065,\n", "  datetime.date(2009, 3, 6): 1.287,\n", "  datetime.date(2009, 3, 9): 1.3014,\n", "  datetime.date(2009, 3, 10): 1.2792,\n", "  datetime.date(2009, 3, 11): 1.28515,\n", "  datetime.date(2009, 3, 12): 1.2778,\n", "  datetime.date(2009, 3, 13): 1.27185,\n", "  datetime.date(2009, 3, 16): 1.2718,\n", "  datetime.date(2009, 3, 17): 1.27,\n", "  datetime.date(2009, 3, 18): 1.2468,\n", "  datetime.date(2009, 3, 19): 1.23945,\n", "  datetime.date(2009, 3, 20): 1.24075,\n", "  datetime.date(2009, 3, 23): 1.22185,\n", "  datetime.date(2009, 3, 24): 1.2321,\n", "  datetime.date(2009, 3, 25): 1.2318,\n", "  datetime.date(2009, 3, 26): 1.23065,\n", "  datetime.date(2009, 3, 27): 1.24185,\n", "  datetime.date(2009, 3, 30): 1.2623,\n", "  datetime.date(2009, 3, 31): 1.2601,\n", "  datetime.date(2009, 4, 1): 1.26,\n", "  datetime.date(2009, 4, 2): 1.2376,\n", "  datetime.date(2009, 4, 3): 1.22995,\n", "  datetime.date(2009, 4, 6): 1.23825,\n", "  datetime.date(2009, 4, 7): 1.23735,\n", "  datetime.date(2009, 4, 8): 1.23645,\n", "  datetime.date(2009, 4, 9): 1.22365,\n", "  datetime.date(2009, 4, 10): 1.22795,\n", "  datetime.date(2009, 4, 13): 1.22005,\n", "  datetime.date(2009, 4, 14): 1.21525,\n", "  datetime.date(2009, 4, 15): 1.20325,\n", "  datetime.date(2009, 4, 16): 1.20745,\n", "  datetime.date(2009, 4, 17): 1.21315,\n", "  datetime.date(2009, 4, 20): 1.2391,\n", "  datetime.date(2009, 4, 21): 1.2357,\n", "  datetime.date(2009, 4, 22): 1.23985,\n", "  datetime.date(2009, 4, 23): 1.2229,\n", "  datetime.date(2009, 4, 24): 1.20985,\n", "  datetime.date(2009, 4, 27): 1.2206,\n", "  datetime.date(2009, 4, 28): 1.21945,\n", "  datetime.date(2009, 4, 29): 1.2026,\n", "  datetime.date(2009, 4, 30): 1.19275,\n", "  datetime.date(2009, 5, 1): 1.18525,\n", "  datetime.date(2009, 5, 4): 1.17265,\n", "  datetime.date(2009, 5, 5): 1.17505,\n", "  datetime.date(2009, 5, 6): 1.1659,\n", "  datetime.date(2009, 5, 7): 1.17005,\n", "  datetime.date(2009, 5, 8): 1.14945,\n", "  datetime.date(2009, 5, 11): 1.16675,\n", "  datetime.date(2009, 5, 12): 1.1627,\n", "  datetime.date(2009, 5, 13): 1.17415,\n", "  datetime.date(2009, 5, 14): 1.16915,\n", "  datetime.date(2009, 5, 15): 1.1778,\n", "  datetime.date(2009, 5, 18): 1.16345,\n", "  datetime.date(2009, 5, 19): 1.156,\n", "  datetime.date(2009, 5, 20): 1.1417,\n", "  datetime.date(2009, 5, 21): 1.13695,\n", "  datetime.date(2009, 5, 22): 1.11945,\n", "  datetime.date(2009, 5, 25): 1.12405,\n", "  datetime.date(2009, 5, 26): 1.11635,\n", "  datetime.date(2009, 5, 27): 1.12125,\n", "  datetime.date(2009, 5, 28): 1.1135,\n", "  datetime.date(2009, 5, 29): 1.09135,\n", "  datetime.date(2009, 6, 1): 1.09335,\n", "  datetime.date(2009, 6, 2): 1.08105,\n", "  datetime.date(2009, 6, 3): 1.11045,\n", "  datetime.date(2009, 6, 4): 1.097,\n", "  datetime.date(2009, 6, 5): 1.11915,\n", "  datetime.date(2009, 6, 8): 1.11585,\n", "  datetime.date(2009, 6, 9): 1.10435,\n", "  datetime.date(2009, 6, 10): 1.10855,\n", "  datetime.date(2009, 6, 11): 1.1024,\n", "  datetime.date(2009, 6, 12): 1.11865,\n", "  datetime.date(2009, 6, 15): 1.1334,\n", "  datetime.date(2009, 6, 16): 1.13565,\n", "  datetime.date(2009, 6, 17): 1.1316,\n", "  datetime.date(2009, 6, 18): 1.1323,\n", "  datetime.date(2009, 6, 19): 1.1347,\n", "  datetime.date(2009, 6, 22): 1.15495,\n", "  datetime.date(2009, 6, 23): 1.14965,\n", "  datetime.date(2009, 6, 24): 1.15765,\n", "  datetime.date(2009, 6, 25): 1.1545,\n", "  datetime.date(2009, 6, 26): 1.1527,\n", "  datetime.date(2009, 6, 29): 1.15635,\n", "  datetime.date(2009, 6, 30): 1.16235,\n", "  datetime.date(2009, 7, 1): 1.1496,\n", "  datetime.date(2009, 7, 2): 1.16305,\n", "  datetime.date(2009, 7, 3): 1.16075,\n", "  datetime.date(2009, 7, 6): 1.15935,\n", "  datetime.date(2009, 7, 7): 1.16645,\n", "  datetime.date(2009, 7, 8): 1.16735,\n", "  datetime.date(2009, 7, 9): 1.16225,\n", "  datetime.date(2009, 7, 10): 1.16395,\n", "  datetime.date(2009, 7, 13): 1.15035,\n", "  datetime.date(2009, 7, 14): 1.1329,\n", "  datetime.date(2009, 7, 15): 1.11255,\n", "  datetime.date(2009, 7, 16): 1.11725,\n", "  datetime.date(2009, 7, 17): 1.11355,\n", "  datetime.date(2009, 7, 20): 1.10615,\n", "  datetime.date(2009, 7, 21): 1.1038,\n", "  datetime.date(2009, 7, 22): 1.0997,\n", "  datetime.date(2009, 7, 23): 1.08875,\n", "  datetime.date(2009, 7, 24): 1.0868,\n", "  datetime.date(2009, 7, 27): 1.08115,\n", "  datetime.date(2009, 7, 28): 1.08145,\n", "  datetime.date(2009, 7, 29): 1.08945,\n", "  datetime.date(2009, 7, 30): 1.08355,\n", "  datetime.date(2009, 7, 31): 1.07755,\n", "  datetime.date(2009, 8, 3): 1.0659,\n", "  datetime.date(2009, 8, 4): 1.07255,\n", "  datetime.date(2009, 8, 5): 1.07055,\n", "  datetime.date(2009, 8, 6): 1.0777,\n", "  datetime.date(2009, 8, 7): 1.0813,\n", "  datetime.date(2009, 8, 10): 1.0887,\n", "  datetime.date(2009, 8, 11): 1.10175,\n", "  datetime.date(2009, 8, 12): 1.0893,\n", "  datetime.date(2009, 8, 13): 1.08745,\n", "  datetime.date(2009, 8, 14): 1.09995,\n", "  datetime.date(2009, 8, 17): 1.1086,\n", "  datetime.date(2009, 8, 18): 1.10105,\n", "  datetime.date(2009, 8, 19): 1.0953,\n", "  datetime.date(2009, 8, 20): 1.08725,\n", "  datetime.date(2009, 8, 21): 1.08115,\n", "  datetime.date(2009, 8, 24): 1.07635,\n", "  datetime.date(2009, 8, 25): 1.08675,\n", "  datetime.date(2009, 8, 26): 1.09735,\n", "  datetime.date(2009, 8, 27): 1.0876,\n", "  datetime.date(2009, 8, 28): 1.0918,\n", "  datetime.date(2009, 8, 31): 1.0937,\n", "  datetime.date(2009, 9, 1): 1.10415,\n", "  datetime.date(2009, 9, 2): 1.10515,\n", "  datetime.date(2009, 9, 3): 1.10195,\n", "  datetime.date(2009, 9, 4): 1.08835,\n", "  datetime.date(2009, 9, 7): 1.0776,\n", "  datetime.date(2009, 9, 8): 1.0781,\n", "  datetime.date(2009, 9, 9): 1.0785,\n", "  datetime.date(2009, 9, 10): 1.0772,\n", "  datetime.date(2009, 9, 11): 1.0767,\n", "  datetime.date(2009, 9, 14): 1.0828,\n", "  datetime.date(2009, 9, 15): 1.07235,\n", "  datetime.date(2009, 9, 16): 1.06585,\n", "  datetime.date(2009, 9, 17): 1.06485,\n", "  datetime.date(2009, 9, 18): 1.0691,\n", "  datetime.date(2009, 9, 21): 1.079,\n", "  datetime.date(2009, 9, 22): 1.06915,\n", "  datetime.date(2009, 9, 23): 1.07445,\n", "  datetime.date(2009, 9, 24): 1.08925,\n", "  datetime.date(2009, 9, 25): 1.09125,\n", "  datetime.date(2009, 9, 28): 1.08455,\n", "  datetime.date(2009, 9, 29): 1.08455,\n", "  datetime.date(2009, 9, 30): 1.0695,\n", "  datetime.date(2009, 10, 1): 1.08375,\n", "  datetime.date(2009, 10, 2): 1.0798,\n", "  datetime.date(2009, 10, 5): 1.06975,\n", "  datetime.date(2009, 10, 6): 1.05935,\n", "  datetime.date(2009, 10, 7): 1.06125,\n", "  datetime.date(2009, 10, 8): 1.0518,\n", "  datetime.date(2009, 10, 9): 1.04265,\n", "  datetime.date(2009, 10, 12): 1.03485,\n", "  datetime.date(2009, 10, 13): 1.0319,\n", "  datetime.date(2009, 10, 14): 1.02365,\n", "  datetime.date(2009, 10, 15): 1.0338,\n", "  datetime.date(2009, 10, 16): 1.037,\n", "  datetime.date(2009, 10, 19): 1.02835,\n", "  datetime.date(2009, 10, 20): 1.04945,\n", "  datetime.date(2009, 10, 21): 1.0429,\n", "  datetime.date(2009, 10, 22): 1.0475,\n", "  datetime.date(2009, 10, 23): 1.05355,\n", "  datetime.date(2009, 10, 26): 1.0691,\n", "  datetime.date(2009, 10, 27): 1.06475,\n", "  datetime.date(2009, 10, 28): 1.08105,\n", "  datetime.date(2009, 10, 29): 1.06655,\n", "  datetime.date(2009, 10, 30): 1.0846,\n", "  datetime.date(2009, 11, 2): 1.07685,\n", "  datetime.date(2009, 11, 3): 1.0662,\n", "  datetime.date(2009, 11, 4): 1.06275,\n", "  datetime.date(2009, 11, 5): 1.06515,\n", "  datetime.date(2009, 11, 6): 1.0754,\n", "  datetime.date(2009, 11, 9): 1.05545,\n", "  datetime.date(2009, 11, 10): 1.0495,\n", "  datetime.date(2009, 11, 11): 1.04505,\n", "  datetime.date(2009, 11, 12): 1.05575,\n", "  datetime.date(2009, 11, 13): 1.05165,\n", "  datetime.date(2009, 11, 16): 1.0475,\n", "  datetime.date(2009, 11, 17): 1.05095,\n", "  datetime.date(2009, 11, 18): 1.05475,\n", "  datetime.date(2009, 11, 19): 1.06365,\n", "  datetime.date(2009, 11, 20): 1.07045,\n", "  datetime.date(2009, 11, 23): 1.0556,\n", "  datetime.date(2009, 11, 24): 1.0581,\n", "  datetime.date(2009, 11, 25): 1.0453,\n", "  datetime.date(2009, 11, 26): 1.05895,\n", "  datetime.date(2009, 11, 27): 1.06215,\n", "  datetime.date(2009, 11, 30): 1.05625,\n", "  datetime.date(2009, 12, 1): 1.04625,\n", "  datetime.date(2009, 12, 2): 1.05105,\n", "  datetime.date(2009, 12, 3): 1.0572,\n", "  datetime.date(2009, 12, 4): 1.05805,\n", "  datetime.date(2009, 12, 7): 1.0513,\n", "  datetime.date(2009, 12, 8): 1.06385,\n", "  datetime.date(2009, 12, 9): 1.05455,\n", "  datetime.date(2009, 12, 10): 1.0516,\n", "  datetime.date(2009, 12, 11): 1.06005,\n", "  datetime.date(2009, 12, 14): 1.05895,\n", "  datetime.date(2009, 12, 15): 1.06115,\n", "  datetime.date(2009, 12, 16): 1.0613,\n", "  datetime.date(2009, 12, 17): 1.07105,\n", "  datetime.date(2009, 12, 18): 1.06635,\n", "  datetime.date(2009, 12, 21): 1.0622,\n", "  datetime.date(2009, 12, 22): 1.05755,\n", "  datetime.date(2009, 12, 23): 1.0486,\n", "  datetime.date(2009, 12, 24): 1.0501,\n", "  datetime.date(2009, 12, 28): 1.04285,\n", "  datetime.date(2009, 12, 29): 1.0435,\n", "  datetime.date(2009, 12, 30): 1.0553,\n", "  datetime.date(2009, 12, 31): 1.05295,\n", "  datetime.date(2010, 1, 4): 1.04175,\n", "  datetime.date(2010, 1, 5): 1.0389,\n", "  datetime.date(2010, 1, 6): 1.03235,\n", "  datetime.date(2010, 1, 7): 1.03455,\n", "  datetime.date(2010, 1, 8): 1.03,\n", "  datetime.date(2010, 1, 11): 1.0335,\n", "  datetime.date(2010, 1, 12): 1.03895,\n", "  datetime.date(2010, 1, 13): 1.0308,\n", "  datetime.date(2010, 1, 14): 1.02345,\n", "  datetime.date(2010, 1, 15): 1.02905,\n", "  datetime.date(2010, 1, 18): 1.02615,\n", "  datetime.date(2010, 1, 19): 1.03135,\n", "  datetime.date(2010, 1, 20): 1.0465,\n", "  datetime.date(2010, 1, 21): 1.0523,\n", "  datetime.date(2010, 1, 22): 1.0578,\n", "  datetime.date(2010, 1, 25): 1.05765,\n", "  datetime.date(2010, 1, 26): 1.06275,\n", "  datetime.date(2010, 1, 27): 1.0643,\n", "  datetime.date(2010, 1, 28): 1.0664,\n", "  datetime.date(2010, 1, 29): 1.07045,\n", "  datetime.date(2010, 2, 1): 1.06065,\n", "  datetime.date(2010, 2, 2): 1.0571,\n", "  datetime.date(2010, 2, 3): 1.06255,\n", "  datetime.date(2010, 2, 4): 1.07425,\n", "  datetime.date(2010, 2, 5): 1.07125,\n", "  datetime.date(2010, 2, 8): 1.0755,\n", "  datetime.date(2010, 2, 9): 1.0656,\n", "  datetime.date(2010, 2, 10): 1.0622,\n", "  datetime.date(2010, 2, 11): 1.04995,\n", "  datetime.date(2010, 2, 12): 1.0506,\n", "  datetime.date(2010, 2, 15): 1.0493,\n", "  datetime.date(2010, 2, 16): 1.0432,\n", "  datetime.date(2010, 2, 17): 1.04625,\n", "  datetime.date(2010, 2, 18): 1.0471,\n", "  datetime.date(2010, 2, 19): 1.03935,\n", "  datetime.date(2010, 2, 22): 1.043,\n", "  datetime.date(2010, 2, 23): 1.0565,\n", "  datetime.date(2010, 2, 24): 1.0537,\n", "  datetime.date(2010, 2, 25): 1.0608,\n", "  datetime.date(2010, 2, 26): 1.05185,\n", "  datetime.date(2010, 3, 1): 1.0414,\n", "  datetime.date(2010, 3, 2): 1.0355,\n", "  datetime.date(2010, 3, 3): 1.0319,\n", "  datetime.date(2010, 3, 4): 1.03175,\n", "  datetime.date(2010, 3, 5): 1.02925,\n", "  datetime.date(2010, 3, 8): 1.0272,\n", "  datetime.date(2010, 3, 9): 1.02615,\n", "  datetime.date(2010, 3, 10): 1.02455,\n", "  datetime.date(2010, 3, 11): 1.0241,\n", "  datetime.date(2010, 3, 12): 1.0193,\n", "  datetime.date(2010, 3, 15): 1.01925,\n", "  datetime.date(2010, 3, 16): 1.01405,\n", "  datetime.date(2010, 3, 17): 1.0103,\n", "  datetime.date(2010, 3, 18): 1.01395,\n", "  datetime.date(2010, 3, 19): 1.01735,\n", "  datetime.date(2010, 3, 22): 1.019,\n", "  datetime.date(2010, 3, 23): 1.0159,\n", "  datetime.date(2010, 3, 24): 1.02675,\n", "  datetime.date(2010, 3, 25): 1.02405,\n", "  datetime.date(2010, 3, 26): 1.0266,\n", "  datetime.date(2010, 3, 29): 1.0211,\n", "  datetime.date(2010, 3, 30): 1.02005,\n", "  datetime.date(2010, 3, 31): 1.0153,\n", "  datetime.date(2010, 4, 1): 1.0086,\n", "  datetime.date(2010, 4, 2): 1.0111,\n", "  datetime.date(2010, 4, 5): 1.0022,\n", "  datetime.date(2010, 4, 6): 1.0015,\n", "  datetime.date(2010, 4, 7): 1.00615,\n", "  datetime.date(2010, 4, 8): 1.0022,\n", "  datetime.date(2010, 4, 9): 1.00275,\n", "  datetime.date(2010, 4, 12): 1.0025,\n", "  datetime.date(2010, 4, 13): 1.0011,\n", "  datetime.date(2010, 4, 14): 0.9986,\n", "  datetime.date(2010, 4, 15): 1.0021,\n", "  datetime.date(2010, 4, 16): 1.0129,\n", "  datetime.date(2010, 4, 19): 1.01445,\n", "  datetime.date(2010, 4, 20): 0.99865,\n", "  datetime.date(2010, 4, 21): 0.9998,\n", "  datetime.date(2010, 4, 22): 1.00035,\n", "  datetime.date(2010, 4, 23): 0.99915,\n", "  datetime.date(2010, 4, 26): 1.00105,\n", "  datetime.date(2010, 4, 27): 1.0176,\n", "  datetime.date(2010, 4, 28): 1.0097,\n", "  datetime.date(2010, 4, 29): 1.0052,\n", "  datetime.date(2010, 4, 30): 1.01785,\n", "  datetime.date(2010, 5, 3): 1.0107,\n", "  datetime.date(2010, 5, 4): 1.02485,\n", "  datetime.date(2010, 5, 5): 1.0307,\n", "  datetime.date(2010, 5, 6): 1.05265,\n", "  datetime.date(2010, 5, 7): 1.04275,\n", "  datetime.date(2010, 5, 10): 1.02325,\n", "  datetime.date(2010, 5, 11): 1.02205,\n", "  datetime.date(2010, 5, 12): 1.01925,\n", "  datetime.date(2010, 5, 13): 1.0201,\n", "  datetime.date(2010, 5, 14): 1.03535,\n", "  datetime.date(2010, 5, 17): 1.03255,\n", "  datetime.date(2010, 5, 18): 1.03935,\n", "  datetime.date(2010, 5, 19): 1.0438,\n", "  datetime.date(2010, 5, 20): 1.07025,\n", "  datetime.date(2010, 5, 21): 1.0596,\n", "  datetime.date(2010, 5, 24): 1.06255,\n", "  datetime.date(2010, 5, 25): 1.0676,\n", "  datetime.date(2010, 5, 26): 1.0709,\n", "  datetime.date(2010, 5, 27): 1.0482,\n", "  datetime.date(2010, 5, 28): 1.054625,\n", "  datetime.date(2010, 5, 31): 1.044575,\n", "  datetime.date(2010, 6, 1): 1.0552,\n", "  datetime.date(2010, 6, 2): 1.038325,\n", "  datetime.date(2010, 6, 3): 1.040275,\n", "  datetime.date(2010, 6, 4): 1.062675,\n", "  datetime.date(2010, 6, 7): 1.061675,\n", "  datetime.date(2010, 6, 8): 1.047475,\n", "  datetime.date(2010, 6, 9): 1.04455,\n", "  datetime.date(2010, 6, 10): 1.029725,\n", "  datetime.date(2010, 6, 11): 1.03225,\n", "  datetime.date(2010, 6, 14): 1.0339,\n", "  datetime.date(2010, 6, 15): 1.0254,\n", "  datetime.date(2010, 6, 16): 1.0243,\n", "  datetime.date(2010, 6, 17): 1.0283,\n", "  datetime.date(2010, 6, 18): 1.021775,\n", "  datetime.date(2010, 6, 21): 1.02425,\n", "  datetime.date(2010, 6, 22): 1.02955,\n", "  datetime.date(2010, 6, 23): 1.03975,\n", "  datetime.date(2010, 6, 24): 1.043,\n", "  datetime.date(2010, 6, 25): 1.0355,\n", "  datetime.date(2010, 6, 28): 1.03575,\n", "  datetime.date(2010, 6, 29): 1.05595,\n", "  datetime.date(2010, 6, 30): 1.063875,\n", "  datetime.date(2010, 7, 1): 1.059625,\n", "  datetime.date(2010, 7, 2): 1.062375,\n", "  datetime.date(2010, 7, 5): 1.063475,\n", "  datetime.date(2010, 7, 6): 1.054425,\n", "  datetime.date(2010, 7, 7): 1.0467,\n", "  datetime.date(2010, 7, 8): 1.042075,\n", "  datetime.date(2010, 7, 9): 1.0339,\n", "  datetime.date(2010, 7, 12): 1.0372,\n", "  datetime.date(2010, 7, 13): 1.03005,\n", "  datetime.date(2010, 7, 14): 1.0326,\n", "  datetime.date(2010, 7, 15): 1.03825,\n", "  datetime.date(2010, 7, 16): 1.058125,\n", "  datetime.date(2010, 7, 19): 1.05495,\n", "  datetime.date(2010, 7, 20): 1.04355,\n", "  datetime.date(2010, 7, 21): 1.049075,\n", "  datetime.date(2010, 7, 22): 1.037225,\n", "  datetime.date(2010, 7, 23): 1.03575,\n", "  datetime.date(2010, 7, 26): 1.0323,\n", "  datetime.date(2010, 7, 27): 1.035425,\n", "  datetime.date(2010, 7, 28): 1.038825,\n", "  datetime.date(2010, 7, 29): 1.036675,\n", "  datetime.date(2010, 7, 30): 1.029375,\n", "  datetime.date(2010, 8, 2): 1.022875,\n", "  datetime.date(2010, 8, 3): 1.02345,\n", "  datetime.date(2010, 8, 4): 1.018275,\n", "  datetime.date(2010, 8, 5): 1.0168,\n", "  datetime.date(2010, 8, 6): 1.0274,\n", "  datetime.date(2010, 8, 9): 1.027025,\n", "  datetime.date(2010, 8, 10): 1.03135,\n", "  datetime.date(2010, 8, 11): 1.046375,\n", "  datetime.date(2010, 8, 12): 1.042425,\n", "  datetime.date(2010, 8, 13): 1.04185,\n", "  datetime.date(2010, 8, 16): 1.043475,\n", "  datetime.date(2010, 8, 17): 1.03205,\n", "  datetime.date(2010, 8, 18): 1.029575,\n", "  datetime.date(2010, 8, 19): 1.0404,\n", "  datetime.date(2010, 8, 20): 1.04745,\n", "  datetime.date(2010, 8, 23): 1.052175,\n", "  datetime.date(2010, 8, 24): 1.0615,\n", "  datetime.date(2010, 8, 25): 1.059725,\n", "  datetime.date(2010, 8, 26): 1.057875,\n", "  datetime.date(2010, 8, 27): 1.050775,\n", "  datetime.date(2010, 8, 30): 1.059975,\n", "  datetime.date(2010, 8, 31): 1.0656,\n", "  datetime.date(2010, 9, 1): 1.04975,\n", "  datetime.date(2010, 9, 2): 1.05235,\n", "  datetime.date(2010, 9, 3): 1.0388,\n", "  datetime.date(2010, 9, 6): 1.03535,\n", "  datetime.date(2010, 9, 7): 1.047875,\n", "  datetime.date(2010, 9, 8): 1.037475,\n", "  datetime.date(2010, 9, 9): 1.03395,\n", "  datetime.date(2010, 9, 10): 1.0369,\n", "  datetime.date(2010, 9, 13): 1.027125,\n", "  datetime.date(2010, 9, 14): 1.027525,\n", "  datetime.date(2010, 9, 15): 1.026225,\n", "  datetime.date(2010, 9, 16): 1.026575,\n", "  datetime.date(2010, 9, 17): 1.03285,\n", "  datetime.date(2010, 9, 20): 1.02825,\n", "  datetime.date(2010, 9, 21): 1.026875,\n", "  datetime.date(2010, 9, 22): 1.0307,\n", "  datetime.date(2010, 9, 23): 1.034225,\n", "  datetime.date(2010, 9, 24): 1.0242,\n", "  datetime.date(2010, 9, 27): 1.029275,\n", "  datetime.date(2010, 9, 28): 1.029775,\n", "  datetime.date(2010, 9, 29): 1.0326,\n", "  datetime.date(2010, 9, 30): 1.02925,\n", "  datetime.date(2010, 10, 1): 1.0197,\n", "  datetime.date(2010, 10, 4): 1.023975,\n", "  datetime.date(2010, 10, 5): 1.015775,\n", "  datetime.date(2010, 10, 6): 1.01105,\n", "  datetime.date(2010, 10, 7): 1.01735,\n", "  datetime.date(2010, 10, 8): 1.010675,\n", "  datetime.date(2010, 10, 11): 1.0138,\n", "  datetime.date(2010, 10, 12): 1.010125,\n", "  datetime.date(2010, 10, 13): 1.00335,\n", "  datetime.date(2010, 10, 14): 1.00395,\n", "  datetime.date(2010, 10, 15): 1.01035,\n", "  datetime.date(2010, 10, 18): 1.017225,\n", "  datetime.date(2010, 10, 19): 1.033525,\n", "  datetime.date(2010, 10, 20): 1.021075,\n", "  datetime.date(2010, 10, 21): 1.02675,\n", "  datetime.date(2010, 10, 22): 1.02585,\n", "  datetime.date(2010, 10, 25): 1.02,\n", "  datetime.date(2010, 10, 26): 1.0239,\n", "  datetime.date(2010, 10, 27): 1.0281,\n", "  datetime.date(2010, 10, 28): 1.02105,\n", "  datetime.date(2010, 10, 29): 1.0194,\n", "  datetime.date(2010, 11, 1): 1.015275,\n", "  datetime.date(2010, 11, 2): 1.008225,\n", "  datetime.date(2010, 11, 3): 1.00525,\n", "  datetime.date(2010, 11, 4): 1.002275,\n", "  datetime.date(2010, 11, 5): 1.000225,\n", "  datetime.date(2010, 11, 8): 1.0036,\n", "  datetime.date(2010, 11, 9): 1.00805,\n", "  datetime.date(2010, 11, 10): 1.0009,\n", "  datetime.date(2010, 11, 11): 1.003175,\n", "  datetime.date(2010, 11, 12): 1.01225,\n", "  datetime.date(2010, 11, 15): 1.009825,\n", "  datetime.date(2010, 11, 16): 1.022325,\n", "  datetime.date(2010, 11, 17): 1.0246,\n", "  datetime.date(2010, 11, 18): 1.019,\n", "  datetime.date(2010, 11, 19): 1.016875,\n", "  datetime.date(2010, 11, 22): 1.018675,\n", "  datetime.date(2010, 11, 23): 1.02465,\n", "  datetime.date(2010, 11, 24): 1.00995,\n", "  datetime.date(2010, 11, 25): 1.0092,\n", "  datetime.date(2010, 11, 26): 1.02125,\n", "  datetime.date(2010, 11, 29): 1.017925,\n", "  datetime.date(2010, 11, 30): 1.026425,\n", "  datetime.date(2010, 12, 1): 1.01705,\n", "  datetime.date(2010, 12, 2): 1.002675,\n", "  datetime.date(2010, 12, 3): 1.003975,\n", "  datetime.date(2010, 12, 6): 1.005625,\n", "  datetime.date(2010, 12, 7): 1.0124,\n", "  datetime.date(2010, 12, 8): 1.0112,\n", "  datetime.date(2010, 12, 9): 1.0106,\n", "  datetime.date(2010, 12, 10): 1.009275,\n", "  datetime.date(2010, 12, 13): 1.007425,\n", "  datetime.date(2010, 12, 14): 1.006175,\n", "  datetime.date(2010, 12, 15): 1.00495,\n", "  datetime.date(2010, 12, 16): 1.006125,\n", "  datetime.date(2010, 12, 17): 1.014025,\n", "  datetime.date(2010, 12, 20): 1.01685,\n", "  datetime.date(2010, 12, 21): 1.017125,\n", "  datetime.date(2010, 12, 22): 1.0132,\n", "  datetime.date(2010, 12, 23): 1.009225,\n", "  datetime.date(2010, 12, 24): 1.0083,\n", "  datetime.date(2010, 12, 27): 1.006875,\n", "  datetime.date(2010, 12, 28): 1.001075,\n", "  datetime.date(2010, 12, 29): 1.000775,\n", "  datetime.date(2010, 12, 30): 0.999975,\n", "  datetime.date(2010, 12, 31): 0.997675,\n", "  datetime.date(2011, 1, 3): 0.99375,\n", "  datetime.date(2011, 1, 4): 0.9984,\n", "  datetime.date(2011, 1, 5): 0.995675,\n", "  datetime.date(2011, 1, 6): 0.9966,\n", "  datetime.date(2011, 1, 7): 0.993375,\n", "  datetime.date(2011, 1, 10): 0.993375,\n", "  datetime.date(2011, 1, 11): 0.9904,\n", "  datetime.date(2011, 1, 12): 0.985725,\n", "  datetime.date(2011, 1, 13): 0.989175,\n", "  datetime.date(2011, 1, 14): 0.99085,\n", "  datetime.date(2011, 1, 17): 0.987075,\n", "  datetime.date(2011, 1, 18): 0.991275,\n", "  datetime.date(2011, 1, 19): 0.995775,\n", "  datetime.date(2011, 1, 20): 0.9973,\n", "  datetime.date(2011, 1, 21): 0.993275,\n", "  datetime.date(2011, 1, 24): 0.993575,\n", "  datetime.date(2011, 1, 25): 0.99685,\n", "  datetime.date(2011, 1, 26): 0.994075,\n", "  datetime.date(2011, 1, 27): 0.99345,\n", "  datetime.date(2011, 1, 28): 1.00125,\n", "  datetime.date(2011, 1, 31): 1.000975,\n", "  datetime.date(2011, 2, 1): 0.99055,\n", "  datetime.date(2011, 2, 2): 0.98795,\n", "  datetime.date(2011, 2, 3): 0.99115,\n", "  datetime.date(2011, 2, 4): 0.9873,\n", "  datetime.date(2011, 2, 7): 0.9908,\n", "  datetime.date(2011, 2, 8): 0.995225,\n", "  datetime.date(2011, 2, 9): 0.993825,\n", "  datetime.date(2011, 2, 10): 0.99545,\n", "  datetime.date(2011, 2, 11): 0.98755,\n", "  datetime.date(2011, 2, 14): 0.98905,\n", "  datetime.date(2011, 2, 15): 0.989775,\n", "  datetime.date(2011, 2, 16): 0.98485,\n", "  datetime.date(2011, 2, 17): 0.984275,\n", "  datetime.date(2011, 2, 18): 0.9867,\n", "  datetime.date(2011, 2, 21): 0.982825,\n", "  datetime.date(2011, 2, 22): 0.990725,\n", "  datetime.date(2011, 2, 23): 0.989525,\n", "  datetime.date(2011, 2, 24): 0.9827,\n", "  datetime.date(2011, 2, 25): 0.9775,\n", "  datetime.date(2011, 2, 28): 0.971625,\n", "  datetime.date(2011, 3, 1): 0.974725,\n", "  datetime.date(2011, 3, 2): 0.97305,\n", "  datetime.date(2011, 3, 3): 0.972025,\n", "  datetime.date(2011, 3, 4): 0.97325,\n", "  datetime.date(2011, 3, 7): 0.97325,\n", "  datetime.date(2011, 3, 8): 0.9714,\n", "  datetime.date(2011, 3, 9): 0.968475,\n", "  datetime.date(2011, 3, 10): 0.975925,\n", "  datetime.date(2011, 3, 11): 0.973125,\n", "  datetime.date(2011, 3, 14): 0.973825,\n", "  datetime.date(2011, 3, 15): 0.98595,\n", "  datetime.date(2011, 3, 16): 0.991475,\n", "  datetime.date(2011, 3, 17): 0.984875,\n", "  datetime.date(2011, 3, 18): 0.984425,\n", "  datetime.date(2011, 3, 21): 0.977825,\n", "  datetime.date(2011, 3, 22): 0.980725,\n", "  datetime.date(2011, 3, 23): 0.981525,\n", "  datetime.date(2011, 3, 24): 0.975175,\n", "  datetime.date(2011, 3, 25): 0.9814,\n", "  datetime.date(2011, 3, 28): 0.977875,\n", "  datetime.date(2011, 3, 29): 0.974425,\n", "  datetime.date(2011, 3, 30): 0.971025,\n", "  datetime.date(2011, 3, 31): 0.9706,\n", "  datetime.date(2011, 4, 1): 0.9633,\n", "  datetime.date(2011, 4, 4): 0.966975,\n", "  datetime.date(2011, 4, 5): 0.96385,\n", "  datetime.date(2011, 4, 6): 0.960725,\n", "  datetime.date(2011, 4, 7): 0.958425,\n", "  datetime.date(2011, 4, 8): 0.955525,\n", "  datetime.date(2011, 4, 11): 0.956425,\n", "  datetime.date(2011, 4, 12): 0.9634,\n", "  datetime.date(2011, 4, 13): 0.962325,\n", "  datetime.date(2011, 4, 14): 0.96115,\n", "  datetime.date(2011, 4, 15): 0.959325,\n", "  datetime.date(2011, 4, 18): 0.964225,\n", "  datetime.date(2011, 4, 19): 0.956125,\n", "  datetime.date(2011, 4, 20): 0.95255,\n", "  datetime.date(2011, 4, 21): 0.953175,\n", "  datetime.date(2011, 4, 22): 0.9545,\n", "  datetime.date(2011, 4, 25): 0.95445,\n", "  datetime.date(2011, 4, 26): 0.951575,\n", "  datetime.date(2011, 4, 27): 0.94975,\n", "  datetime.date(2011, 4, 28): 0.950625,\n", "  datetime.date(2011, 4, 29): 0.94505,\n", "  datetime.date(2011, 5, 2): 0.950625,\n", "  datetime.date(2011, 5, 3): 0.9526,\n", "  datetime.date(2011, 5, 4): 0.95935,\n", "  datetime.date(2011, 5, 5): 0.966925,\n", "  datetime.date(2011, 5, 6): 0.9665,\n", "  datetime.date(2011, 5, 9): 0.96185,\n", "  datetime.date(2011, 5, 10): 0.95675,\n", "  datetime.date(2011, 5, 11): 0.9619,\n", "  datetime.date(2011, 5, 12): 0.962575,\n", "  datetime.date(2011, 5, 13): 0.96875,\n", "  datetime.date(2011, 5, 16): 0.97585,\n", "  datetime.date(2011, 5, 17): 0.9722,\n", "  datetime.date(2011, 5, 18): 0.9701,\n", "  datetime.date(2011, 5, 19): 0.9676,\n", "  datetime.date(2011, 5, 20): 0.97395,\n", "  datetime.date(2011, 5, 23): 0.977875,\n", "  datetime.date(2011, 5, 24): 0.97675,\n", "  datetime.date(2011, 5, 25): 0.9775,\n", "  datetime.date(2011, 5, 26): 0.9778,\n", "  datetime.date(2011, 5, 27): 0.97635,\n", "  datetime.date(2011, 5, 30): 0.97705,\n", "  datetime.date(2011, 5, 31): 0.968375,\n", "  datetime.date(2011, 6, 1): 0.977175,\n", "  datetime.date(2011, 6, 2): 0.975875,\n", "  datetime.date(2011, 6, 3): 0.977925,\n", "  datetime.date(2011, 6, 6): 0.9809,\n", "  datetime.date(2011, 6, 7): 0.9745,\n", "  datetime.date(2011, 6, 8): 0.9793,\n", "  datetime.date(2011, 6, 9): 0.972975,\n", "  datetime.date(2011, 6, 10): 0.9798,\n", "  datetime.date(2011, 6, 13): 0.976125,\n", "  datetime.date(2011, 6, 14): 0.9682,\n", "  datetime.date(2011, 6, 15): 0.979125,\n", "  datetime.date(2011, 6, 16): 0.981325,\n", "  datetime.date(2011, 6, 17): 0.979425,\n", "  datetime.date(2011, 6, 20): 0.9798,\n", "  datetime.date(2011, 6, 21): 0.971175,\n", "  datetime.date(2011, 6, 22): 0.97385,\n", "  datetime.date(2011, 6, 23): 0.978975,\n", "  datetime.date(2011, 6, 24): 0.988375,\n", "  datetime.date(2011, 6, 27): 0.986125,\n", "  datetime.date(2011, 6, 28): 0.98125,\n", "  datetime.date(2011, 6, 29): 0.969525,\n", "  datetime.date(2011, 6, 30): 0.963425,\n", "  datetime.date(2011, 7, 1): 0.958475,\n", "  datetime.date(2011, 7, 4): 0.96115,\n", "  datetime.date(2011, 7, 5): 0.96335,\n", "  datetime.date(2011, 7, 6): 0.96535,\n", "  datetime.date(2011, 7, 7): 0.95865,\n", "  datetime.date(2011, 7, 8): 0.961975,\n", "  datetime.date(2011, 7, 11): 0.968775,\n", "  datetime.date(2011, 7, 12): 0.966475,\n", "  datetime.date(2011, 7, 13): 0.958275,\n", "  datetime.date(2011, 7, 14): 0.960625,\n", "  datetime.date(2011, 7, 15): 0.953325,\n", "  datetime.date(2011, 7, 18): 0.9597,\n", "  datetime.date(2011, 7, 19): 0.950125,\n", "  datetime.date(2011, 7, 20): 0.947475,\n", "  datetime.date(2011, 7, 21): 0.9433,\n", "  datetime.date(2011, 7, 22): 0.94805,\n", "  datetime.date(2011, 7, 25): 0.9471,\n", "  datetime.date(2011, 7, 26): 0.944275,\n", "  datetime.date(2011, 7, 27): 0.949825,\n", "  datetime.date(2011, 7, 28): 0.949325,\n", "  datetime.date(2011, 7, 29): 0.955525,\n", "  datetime.date(2011, 8, 1): 0.957025,\n", "  datetime.date(2011, 8, 2): 0.961275,\n", "  datetime.date(2011, 8, 3): 0.962075,\n", "  datetime.date(2011, 8, 4): 0.981525,\n", "  datetime.date(2011, 8, 5): 0.98185,\n", "  datetime.date(2011, 8, 8): 0.994475,\n", "  datetime.date(2011, 8, 9): 0.97725,\n", "  datetime.date(2011, 8, 10): 0.994825,\n", "  datetime.date(2011, 8, 11): 0.98425,\n", "  datetime.date(2011, 8, 12): 0.9873,\n", "  datetime.date(2011, 8, 15): 0.97925,\n", "  datetime.date(2011, 8, 16): 0.982425,\n", "  datetime.date(2011, 8, 17): 0.980675,\n", "  datetime.date(2011, 8, 18): 0.990425,\n", "  datetime.date(2011, 8, 19): 0.99015,\n", "  datetime.date(2011, 8, 22): 0.990475,\n", "  datetime.date(2011, 8, 23): 0.987375,\n", "  datetime.date(2011, 8, 24): 0.987125,\n", "  datetime.date(2011, 8, 25): 0.9883,\n", "  datetime.date(2011, 8, 26): 0.981375,\n", "  datetime.date(2011, 8, 29): 0.97695,\n", "  datetime.date(2011, 8, 30): 0.977875,\n", "  datetime.date(2011, 8, 31): 0.977725,\n", "  datetime.date(2011, 9, 1): 0.976825,\n", "  datetime.date(2011, 9, 2): 0.98535,\n", "  datetime.date(2011, 9, 5): 0.990125,\n", "  datetime.date(2011, 9, 6): 0.990525,\n", "  datetime.date(2011, 9, 7): 0.98325,\n", "  datetime.date(2011, 9, 8): 0.989425,\n", "  datetime.date(2011, 9, 9): 0.9967,\n", "  datetime.date(2011, 9, 12): 0.992825,\n", "  datetime.date(2011, 9, 13): 0.985725,\n", "  datetime.date(2011, 9, 14): 0.989225,\n", "  datetime.date(2011, 9, 15): 0.983575,\n", "  datetime.date(2011, 9, 16): 0.978075,\n", "  datetime.date(2011, 9, 19): 0.9907,\n", "  datetime.date(2011, 9, 20): 0.992575,\n", "  datetime.date(2011, 9, 21): 1.008125,\n", "  datetime.date(2011, 9, 22): 1.0284,\n", "  datetime.date(2011, 9, 23): 1.028175,\n", "  datetime.date(2011, 9, 26): 1.025125,\n", "  datetime.date(2011, 9, 27): 1.019325,\n", "  datetime.date(2011, 9, 28): 1.0335,\n", "  datetime.date(2011, 9, 29): 1.03585,\n", "  datetime.date(2011, 9, 30): 1.050375,\n", "  datetime.date(2011, 10, 3): 1.054675,\n", "  datetime.date(2011, 10, 4): 1.0514,\n", "  ...},\n", " 'USDCHF': {datetime.date(2007, 11, 2): 1.1541,\n", "  datetime.date(2007, 11, 9): 1.12255,\n", "  datetime.date(2007, 11, 29): 1.11795,\n", "  datetime.date(2007, 11, 30): 1.13205,\n", "  datetime.date(2007, 12, 3): 1.12735,\n", "  datetime.date(2007, 12, 4): 1.1167,\n", "  datetime.date(2007, 12, 5): 1.127475,\n", "  datetime.date(2007, 12, 6): 1.12975,\n", "  datetime.date(2007, 12, 7): 1.12795,\n", "  datetime.date(2007, 12, 10): 1.12815,\n", "  datetime.date(2007, 12, 11): 1.1309,\n", "  datetime.date(2007, 12, 12): 1.13475,\n", "  datetime.date(2007, 12, 13): 1.14115,\n", "  datetime.date(2007, 12, 14): 1.15295,\n", "  datetime.date(2007, 12, 17): 1.1491,\n", "  datetime.date(2007, 12, 18): 1.1518,\n", "  datetime.date(2007, 12, 19): 1.155425,\n", "  datetime.date(2007, 12, 20): 1.1582,\n", "  datetime.date(2007, 12, 21): 1.1549,\n", "  datetime.date(2007, 12, 24): 1.1568,\n", "  datetime.date(2007, 12, 26): 1.1515,\n", "  datetime.date(2007, 12, 27): 1.139125,\n", "  datetime.date(2007, 12, 28): 1.1262,\n", "  datetime.date(2007, 12, 31): 1.13355,\n", "  datetime.date(2008, 1, 2): 1.11955,\n", "  datetime.date(2008, 1, 3): 1.11095,\n", "  datetime.date(2008, 1, 4): 1.1084,\n", "  datetime.date(2008, 1, 7): 1.1164,\n", "  datetime.date(2008, 1, 8): 1.1126,\n", "  datetime.date(2008, 1, 9): 1.11665,\n", "  datetime.date(2008, 1, 10): 1.10355,\n", "  datetime.date(2008, 1, 11): 1.101525,\n", "  datetime.date(2008, 1, 14): 1.093125,\n", "  datetime.date(2008, 1, 15): 1.092925,\n", "  datetime.date(2008, 1, 16): 1.10055,\n", "  datetime.date(2008, 1, 17): 1.101025,\n", "  datetime.date(2008, 1, 18): 1.0985,\n", "  datetime.date(2008, 1, 21): 1.108175,\n", "  datetime.date(2008, 1, 22): 1.094925,\n", "  datetime.date(2008, 1, 23): 1.091525,\n", "  datetime.date(2008, 1, 24): 1.087925,\n", "  datetime.date(2008, 1, 25): 1.096775,\n", "  datetime.date(2008, 1, 28): 1.0893,\n", "  datetime.date(2008, 1, 29): 1.0938,\n", "  datetime.date(2008, 1, 30): 1.083375,\n", "  datetime.date(2008, 1, 31): 1.08145,\n", "  datetime.date(2008, 2, 1): 1.08955,\n", "  datetime.date(2008, 2, 4): 1.088225,\n", "  datetime.date(2008, 2, 5): 1.09965,\n", "  datetime.date(2008, 2, 6): 1.098325,\n", "  datetime.date(2008, 2, 7): 1.1041,\n", "  datetime.date(2008, 2, 8): 1.1032,\n", "  datetime.date(2008, 2, 11): 1.102425,\n", "  datetime.date(2008, 2, 12): 1.102125,\n", "  datetime.date(2008, 2, 13): 1.10835,\n", "  datetime.date(2008, 2, 14): 1.09765,\n", "  datetime.date(2008, 2, 15): 1.093275,\n", "  datetime.date(2008, 2, 18): 1.102125,\n", "  datetime.date(2008, 2, 19): 1.09415,\n", "  datetime.date(2008, 2, 20): 1.099625,\n", "  datetime.date(2008, 2, 21): 1.08975,\n", "  datetime.date(2008, 2, 22): 1.0853,\n", "  datetime.date(2008, 2, 25): 1.089425,\n", "  datetime.date(2008, 2, 26): 1.0756,\n", "  datetime.date(2008, 2, 27): 1.063175,\n", "  datetime.date(2008, 2, 28): 1.052025,\n", "  datetime.date(2008, 2, 29): 1.04125,\n", "  datetime.date(2008, 3, 3): 1.042325,\n", "  datetime.date(2008, 3, 4): 1.0377,\n", "  datetime.date(2008, 3, 5): 1.0367,\n", "  datetime.date(2008, 3, 6): 1.02275,\n", "  datetime.date(2008, 3, 7): 1.0253,\n", "  datetime.date(2008, 3, 10): 1.019325,\n", "  datetime.date(2008, 3, 11): 1.03345,\n", "  datetime.date(2008, 3, 12): 1.01465,\n", "  datetime.date(2008, 3, 13): 1.00995,\n", "  datetime.date(2008, 3, 14): 0.9984,\n", "  datetime.date(2008, 3, 17): 0.98445,\n", "  datetime.date(2008, 3, 18): 1.0024,\n", "  datetime.date(2008, 3, 19): 0.9981,\n", "  datetime.date(2008, 3, 20): 1.009575,\n", "  datetime.date(2008, 3, 21): 1.00985,\n", "  datetime.date(2008, 3, 24): 1.02045,\n", "  datetime.date(2008, 3, 25): 1.0052,\n", "  datetime.date(2008, 3, 26): 0.988875,\n", "  datetime.date(2008, 3, 27): 0.9941,\n", "  datetime.date(2008, 3, 28): 0.9952,\n", "  datetime.date(2008, 3, 31): 0.9931,\n", "  datetime.date(2008, 4, 1): 1.0125,\n", "  datetime.date(2008, 4, 2): 1.00865,\n", "  datetime.date(2008, 4, 3): 1.010025,\n", "  datetime.date(2008, 4, 4): 1.005875,\n", "  datetime.date(2008, 4, 7): 1.01305,\n", "  datetime.date(2008, 4, 8): 1.01405,\n", "  datetime.date(2008, 4, 9): 1.0019,\n", "  datetime.date(2008, 4, 10): 1.00825,\n", "  datetime.date(2008, 4, 11): 1.00095,\n", "  datetime.date(2008, 4, 14): 0.9988,\n", "  datetime.date(2008, 4, 15): 1.0064,\n", "  datetime.date(2008, 4, 16): 1.000425,\n", "  datetime.date(2008, 4, 17): 1.0063,\n", "  datetime.date(2008, 4, 18): 1.01825,\n", "  datetime.date(2008, 4, 21): 1.00865,\n", "  datetime.date(2008, 4, 22): 1.003275,\n", "  datetime.date(2008, 4, 23): 1.0152,\n", "  datetime.date(2008, 4, 24): 1.0356,\n", "  datetime.date(2008, 4, 25): 1.0344,\n", "  datetime.date(2008, 4, 28): 1.033825,\n", "  datetime.date(2008, 4, 29): 1.03775,\n", "  datetime.date(2008, 4, 30): 1.034525,\n", "  datetime.date(2008, 5, 1): 1.048525,\n", "  datetime.date(2008, 5, 2): 1.05725,\n", "  datetime.date(2008, 5, 5): 1.053625,\n", "  datetime.date(2008, 5, 6): 1.052075,\n", "  datetime.date(2008, 5, 7): 1.05525,\n", "  datetime.date(2008, 5, 8): 1.05115,\n", "  datetime.date(2008, 5, 9): 1.041025,\n", "  datetime.date(2008, 5, 12): 1.043675,\n", "  datetime.date(2008, 5, 13): 1.0528,\n", "  datetime.date(2008, 5, 14): 1.054725,\n", "  datetime.date(2008, 5, 15): 1.0571,\n", "  datetime.date(2008, 5, 16): 1.04765,\n", "  datetime.date(2008, 5, 19): 1.053525,\n", "  datetime.date(2008, 5, 20): 1.037375,\n", "  datetime.date(2008, 5, 21): 1.025075,\n", "  datetime.date(2008, 5, 22): 1.0306,\n", "  datetime.date(2008, 5, 23): 1.0242,\n", "  datetime.date(2008, 5, 26): 1.024475,\n", "  datetime.date(2008, 5, 27): 1.03375,\n", "  datetime.date(2008, 5, 28): 1.03805,\n", "  datetime.date(2008, 5, 29): 1.049425,\n", "  datetime.date(2008, 5, 30): 1.0424,\n", "  datetime.date(2008, 6, 2): 1.0375,\n", "  datetime.date(2008, 6, 3): 1.042275,\n", "  datetime.date(2008, 6, 4): 1.04235,\n", "  datetime.date(2008, 6, 5): 1.038,\n", "  datetime.date(2008, 6, 6): 1.018625,\n", "  datetime.date(2008, 6, 9): 1.0275,\n", "  datetime.date(2008, 6, 10): 1.0419,\n", "  datetime.date(2008, 6, 11): 1.0317,\n", "  datetime.date(2008, 6, 12): 1.0416,\n", "  datetime.date(2008, 6, 13): 1.0469,\n", "  datetime.date(2008, 6, 16): 1.04485,\n", "  datetime.date(2008, 6, 17): 1.0417,\n", "  datetime.date(2008, 6, 18): 1.0362,\n", "  datetime.date(2008, 6, 19): 1.045475,\n", "  datetime.date(2008, 6, 20): 1.03585,\n", "  datetime.date(2008, 6, 23): 1.045375,\n", "  datetime.date(2008, 6, 24): 1.041725,\n", "  datetime.date(2008, 6, 25): 1.035,\n", "  datetime.date(2008, 6, 26): 1.0238,\n", "  datetime.date(2008, 6, 27): 1.01815,\n", "  datetime.date(2008, 6, 30): 1.0211,\n", "  datetime.date(2008, 7, 1): 1.01975,\n", "  datetime.date(2008, 7, 2): 1.014075,\n", "  datetime.date(2008, 7, 3): 1.02635,\n", "  datetime.date(2008, 7, 4): 1.02555,\n", "  datetime.date(2008, 7, 7): 1.02705,\n", "  datetime.date(2008, 7, 8): 1.033725,\n", "  datetime.date(2008, 7, 9): 1.02815,\n", "  datetime.date(2008, 7, 10): 1.027875,\n", "  datetime.date(2008, 7, 11): 1.01615,\n", "  datetime.date(2008, 7, 14): 1.016125,\n", "  datetime.date(2008, 7, 15): 1.00925,\n", "  datetime.date(2008, 7, 16): 1.01735,\n", "  datetime.date(2008, 7, 17): 1.019325,\n", "  datetime.date(2008, 7, 18): 1.022775,\n", "  datetime.date(2008, 7, 21): 1.01765,\n", "  datetime.date(2008, 7, 22): 1.03095,\n", "  datetime.date(2008, 7, 23): 1.0377,\n", "  datetime.date(2008, 7, 24): 1.0372,\n", "  datetime.date(2008, 7, 25): 1.03625,\n", "  datetime.date(2008, 7, 28): 1.0345,\n", "  datetime.date(2008, 7, 29): 1.04685,\n", "  datetime.date(2008, 7, 30): 1.048375,\n", "  datetime.date(2008, 7, 31): 1.04695,\n", "  datetime.date(2008, 8, 1): 1.0497,\n", "  datetime.date(2008, 8, 4): 1.048275,\n", "  datetime.date(2008, 8, 5): 1.054475,\n", "  datetime.date(2008, 8, 6): 1.060025,\n", "  datetime.date(2008, 8, 7): 1.0625,\n", "  datetime.date(2008, 8, 8): 1.08235,\n", "  datetime.date(2008, 8, 11): 1.086,\n", "  datetime.date(2008, 8, 12): 1.0868,\n", "  datetime.date(2008, 8, 13): 1.0854,\n", "  datetime.date(2008, 8, 14): 1.09285,\n", "  datetime.date(2008, 8, 15): 1.09605,\n", "  datetime.date(2008, 8, 18): 1.097475,\n", "  datetime.date(2008, 8, 19): 1.092125,\n", "  datetime.date(2008, 8, 20): 1.099075,\n", "  datetime.date(2008, 8, 21): 1.085875,\n", "  datetime.date(2008, 8, 22): 1.0986,\n", "  datetime.date(2008, 8, 25): 1.0959,\n", "  datetime.date(2008, 8, 26): 1.099625,\n", "  datetime.date(2008, 8, 27): 1.0976,\n", "  datetime.date(2008, 8, 28): 1.098625,\n", "  datetime.date(2008, 8, 29): 1.1014,\n", "  datetime.date(2008, 9, 1): 1.1013,\n", "  datetime.date(2008, 9, 2): 1.1066,\n", "  datetime.date(2008, 9, 3): 1.106125,\n", "  datetime.date(2008, 9, 4): 1.109025,\n", "  datetime.date(2008, 9, 5): 1.1186,\n", "  datetime.date(2008, 9, 8): 1.131675,\n", "  datetime.date(2008, 9, 9): 1.126975,\n", "  datetime.date(2008, 9, 10): 1.1366,\n", "  datetime.date(2008, 9, 11): 1.13795,\n", "  datetime.date(2008, 9, 12): 1.1301,\n", "  datetime.date(2008, 9, 15): 1.1159,\n", "  datetime.date(2008, 9, 16): 1.122675,\n", "  datetime.date(2008, 9, 17): 1.1029,\n", "  datetime.date(2008, 9, 18): 1.10465,\n", "  datetime.date(2008, 9, 19): 1.10325,\n", "  datetime.date(2008, 9, 22): 1.074225,\n", "  datetime.date(2008, 9, 23): 1.08635,\n", "  datetime.date(2008, 9, 24): 1.091675,\n", "  datetime.date(2008, 9, 25): 1.090775,\n", "  datetime.date(2008, 9, 26): 1.09035,\n", "  datetime.date(2008, 9, 29): 1.0892,\n", "  datetime.date(2008, 9, 30): 1.12215,\n", "  datetime.date(2008, 10, 1): 1.12555,\n", "  datetime.date(2008, 10, 2): 1.135625,\n", "  datetime.date(2008, 10, 3): 1.1252,\n", "  datetime.date(2008, 10, 6): 1.147925,\n", "  datetime.date(2008, 10, 7): 1.139325,\n", "  datetime.date(2008, 10, 8): 1.1268,\n", "  datetime.date(2008, 10, 9): 1.12945,\n", "  datetime.date(2008, 10, 10): 1.13845,\n", "  datetime.date(2008, 10, 13): 1.137375,\n", "  datetime.date(2008, 10, 14): 1.1372,\n", "  datetime.date(2008, 10, 15): 1.131925,\n", "  datetime.date(2008, 10, 16): 1.13805,\n", "  datetime.date(2008, 10, 17): 1.137,\n", "  datetime.date(2008, 10, 20): 1.149425,\n", "  datetime.date(2008, 10, 21): 1.151,\n", "  datetime.date(2008, 10, 22): 1.16315,\n", "  datetime.date(2008, 10, 23): 1.15875,\n", "  datetime.date(2008, 10, 24): 1.16705,\n", "  datetime.date(2008, 10, 27): 1.155425,\n", "  datetime.date(2008, 10, 28): 1.1594,\n", "  datetime.date(2008, 10, 29): 1.13055,\n", "  datetime.date(2008, 10, 30): 1.13935,\n", "  datetime.date(2008, 10, 31): 1.157825,\n", "  datetime.date(2008, 11, 3): 1.174725,\n", "  datetime.date(2008, 11, 4): 1.162625,\n", "  datetime.date(2008, 11, 5): 1.158025,\n", "  datetime.date(2008, 11, 6): 1.1782,\n", "  datetime.date(2008, 11, 7): 1.1782,\n", "  datetime.date(2008, 11, 10): 1.17885,\n", "  datetime.date(2008, 11, 11): 1.1875,\n", "  datetime.date(2008, 11, 12): 1.186775,\n", "  datetime.date(2008, 11, 13): 1.187025,\n", "  datetime.date(2008, 11, 14): 1.19505,\n", "  datetime.date(2008, 11, 17): 1.19865,\n", "  datetime.date(2008, 11, 18): 1.2032,\n", "  datetime.date(2008, 11, 19): 1.214025,\n", "  datetime.date(2008, 11, 20): 1.224775,\n", "  datetime.date(2008, 11, 21): 1.222475,\n", "  datetime.date(2008, 11, 24): 1.193675,\n", "  datetime.date(2008, 11, 25): 1.1836,\n", "  datetime.date(2008, 11, 26): 1.2036,\n", "  datetime.date(2008, 11, 27): 1.200425,\n", "  datetime.date(2008, 11, 28): 1.21555,\n", "  datetime.date(2008, 12, 1): 1.20585,\n", "  datetime.date(2008, 12, 2): 1.20635,\n", "  datetime.date(2008, 12, 3): 1.2094,\n", "  datetime.date(2008, 12, 4): 1.195375,\n", "  datetime.date(2008, 12, 5): 1.220075,\n", "  datetime.date(2008, 12, 8): 1.20325,\n", "  datetime.date(2008, 12, 9): 1.205375,\n", "  datetime.date(2008, 12, 10): 1.19885,\n", "  datetime.date(2008, 12, 11): 1.184,\n", "  datetime.date(2008, 12, 12): 1.177425,\n", "  datetime.date(2008, 12, 15): 1.15925,\n", "  datetime.date(2008, 12, 16): 1.1244,\n", "  datetime.date(2008, 12, 17): 1.072075,\n", "  datetime.date(2008, 12, 18): 1.084225,\n", "  datetime.date(2008, 12, 19): 1.1034,\n", "  datetime.date(2008, 12, 22): 1.093525,\n", "  datetime.date(2008, 12, 23): 1.088625,\n", "  datetime.date(2008, 12, 24): 1.0735,\n", "  datetime.date(2008, 12, 26): 1.0704,\n", "  datetime.date(2008, 12, 29): 1.06205,\n", "  datetime.date(2008, 12, 30): 1.06015,\n", "  datetime.date(2008, 12, 31): 1.068725,\n", "  datetime.date(2009, 1, 2): 1.0806,\n", "  datetime.date(2009, 1, 5): 1.108975,\n", "  datetime.date(2009, 1, 6): 1.114075,\n", "  datetime.date(2009, 1, 7): 1.101475,\n", "  datetime.date(2009, 1, 8): 1.092875,\n", "  datetime.date(2009, 1, 9): 1.11345,\n", "  datetime.date(2009, 1, 12): 1.11475,\n", "  datetime.date(2009, 1, 13): 1.11885,\n", "  datetime.date(2009, 1, 14): 1.11595,\n", "  datetime.date(2009, 1, 15): 1.124825,\n", "  datetime.date(2009, 1, 16): 1.117475,\n", "  datetime.date(2009, 1, 19): 1.134525,\n", "  datetime.date(2009, 1, 20): 1.146425,\n", "  datetime.date(2009, 1, 21): 1.1533,\n", "  datetime.date(2009, 1, 22): 1.152775,\n", "  datetime.date(2009, 1, 23): 1.155125,\n", "  datetime.date(2009, 1, 26): 1.1374,\n", "  datetime.date(2009, 1, 27): 1.14265,\n", "  datetime.date(2009, 1, 28): 1.1514,\n", "  datetime.date(2009, 1, 29): 1.153175,\n", "  datetime.date(2009, 1, 30): 1.160275,\n", "  datetime.date(2009, 2, 2): 1.161325,\n", "  datetime.date(2009, 2, 3): 1.142375,\n", "  datetime.date(2009, 2, 4): 1.158375,\n", "  datetime.date(2009, 2, 5): 1.170525,\n", "  datetime.date(2009, 2, 6): 1.1625,\n", "  datetime.date(2009, 2, 9): 1.164125,\n", "  datetime.date(2009, 2, 10): 1.1566,\n", "  datetime.date(2009, 2, 11): 1.15845,\n", "  datetime.date(2009, 2, 12): 1.163275,\n", "  datetime.date(2009, 2, 13): 1.160875,\n", "  datetime.date(2009, 2, 16): 1.159675,\n", "  datetime.date(2009, 2, 17): 1.169375,\n", "  datetime.date(2009, 2, 18): 1.17825,\n", "  datetime.date(2009, 2, 19): 1.173575,\n", "  datetime.date(2009, 2, 20): 1.15655,\n", "  datetime.date(2009, 2, 23): 1.16865,\n", "  datetime.date(2009, 2, 24): 1.160175,\n", "  datetime.date(2009, 2, 25): 1.17,\n", "  datetime.date(2009, 2, 26): 1.1644,\n", "  datetime.date(2009, 2, 27): 1.1699,\n", "  datetime.date(2009, 3, 2): 1.175675,\n", "  datetime.date(2009, 3, 3): 1.1758,\n", "  datetime.date(2009, 3, 4): 1.16765,\n", "  datetime.date(2009, 3, 5): 1.1707,\n", "  datetime.date(2009, 3, 6): 1.1582,\n", "  datetime.date(2009, 3, 9): 1.1584,\n", "  datetime.date(2009, 3, 10): 1.160775,\n", "  datetime.date(2009, 3, 11): 1.1531,\n", "  datetime.date(2009, 3, 12): 1.1849,\n", "  datetime.date(2009, 3, 13): 1.1852,\n", "  datetime.date(2009, 3, 16): 1.184875,\n", "  datetime.date(2009, 3, 17): 1.1822,\n", "  datetime.date(2009, 3, 18): 1.14085,\n", "  datetime.date(2009, 3, 19): 1.123675,\n", "  datetime.date(2009, 3, 20): 1.127425,\n", "  datetime.date(2009, 3, 23): 1.124775,\n", "  datetime.date(2009, 3, 24): 1.13175,\n", "  datetime.date(2009, 3, 25): 1.121425,\n", "  datetime.date(2009, 3, 26): 1.127125,\n", "  datetime.date(2009, 3, 27): 1.1442,\n", "  datetime.date(2009, 3, 30): 1.1486,\n", "  datetime.date(2009, 3, 31): 1.13945,\n", "  datetime.date(2009, 4, 1): 1.145375,\n", "  datetime.date(2009, 4, 2): 1.13395,\n", "  datetime.date(2009, 4, 3): 1.13105,\n", "  datetime.date(2009, 4, 6): 1.136325,\n", "  datetime.date(2009, 4, 7): 1.14255,\n", "  datetime.date(2009, 4, 8): 1.14685,\n", "  datetime.date(2009, 4, 9): 1.156125,\n", "  datetime.date(2009, 4, 10): 1.15645,\n", "  datetime.date(2009, 4, 13): 1.133225,\n", "  datetime.date(2009, 4, 14): 1.13865,\n", "  datetime.date(2009, 4, 15): 1.1429,\n", "  datetime.date(2009, 4, 16): 1.14645,\n", "  datetime.date(2009, 4, 17): 1.165225,\n", "  datetime.date(2009, 4, 20): 1.16875,\n", "  datetime.date(2009, 4, 21): 1.167575,\n", "  datetime.date(2009, 4, 22): 1.1636,\n", "  datetime.date(2009, 4, 23): 1.15045,\n", "  datetime.date(2009, 4, 24): 1.139175,\n", "  datetime.date(2009, 4, 27): 1.1557,\n", "  datetime.date(2009, 4, 28): 1.142725,\n", "  datetime.date(2009, 4, 29): 1.1363,\n", "  datetime.date(2009, 4, 30): 1.14055,\n", "  datetime.date(2009, 5, 1): 1.135675,\n", "  datetime.date(2009, 5, 4): 1.126575,\n", "  datetime.date(2009, 5, 5): 1.1322,\n", "  datetime.date(2009, 5, 6): 1.131275,\n", "  datetime.date(2009, 5, 7): 1.13045,\n", "  datetime.date(2009, 5, 8): 1.1054,\n", "  datetime.date(2009, 5, 11): 1.109525,\n", "  datetime.date(2009, 5, 12): 1.1056,\n", "  datetime.date(2009, 5, 13): 1.106625,\n", "  datetime.date(2009, 5, 14): 1.104225,\n", "  datetime.date(2009, 5, 15): 1.121475,\n", "  datetime.date(2009, 5, 18): 1.114175,\n", "  datetime.date(2009, 5, 19): 1.109325,\n", "  datetime.date(2009, 5, 20): 1.100075,\n", "  datetime.date(2009, 5, 21): 1.09365,\n", "  datetime.date(2009, 5, 22): 1.085675,\n", "  datetime.date(2009, 5, 25): 1.0825,\n", "  datetime.date(2009, 5, 26): 1.08385,\n", "  datetime.date(2009, 5, 27): 1.093225,\n", "  datetime.date(2009, 5, 28): 1.084225,\n", "  datetime.date(2009, 5, 29): 1.066925,\n", "  datetime.date(2009, 6, 1): 1.070275,\n", "  datetime.date(2009, 6, 2): 1.061675,\n", "  datetime.date(2009, 6, 3): 1.070125,\n", "  datetime.date(2009, 6, 4): 1.069125,\n", "  datetime.date(2009, 6, 5): 1.08565,\n", "  datetime.date(2009, 6, 8): 1.091475,\n", "  datetime.date(2009, 6, 9): 1.0786,\n", "  datetime.date(2009, 6, 10): 1.08045,\n", "  datetime.date(2009, 6, 11): 1.07035,\n", "  datetime.date(2009, 6, 12): 1.079075,\n", "  datetime.date(2009, 6, 15): 1.091375,\n", "  datetime.date(2009, 6, 16): 1.08855,\n", "  datetime.date(2009, 6, 17): 1.08005,\n", "  datetime.date(2009, 6, 18): 1.086325,\n", "  datetime.date(2009, 6, 19): 1.081425,\n", "  datetime.date(2009, 6, 22): 1.086325,\n", "  datetime.date(2009, 6, 23): 1.066775,\n", "  datetime.date(2009, 6, 24): 1.0979,\n", "  datetime.date(2009, 6, 25): 1.093975,\n", "  datetime.date(2009, 6, 26): 1.083675,\n", "  datetime.date(2009, 6, 29): 1.082375,\n", "  datetime.date(2009, 6, 30): 1.08635,\n", "  datetime.date(2009, 7, 1): 1.075225,\n", "  datetime.date(2009, 7, 2): 1.0844,\n", "  datetime.date(2009, 7, 3): 1.086575,\n", "  datetime.date(2009, 7, 6): 1.0845,\n", "  datetime.date(2009, 7, 7): 1.0887,\n", "  datetime.date(2009, 7, 8): 1.0902,\n", "  datetime.date(2009, 7, 9): 1.07855,\n", "  datetime.date(2009, 7, 10): 1.086525,\n", "  datetime.date(2009, 7, 13): 1.082975,\n", "  datetime.date(2009, 7, 14): 1.088875,\n", "  datetime.date(2009, 7, 15): 1.0744,\n", "  datetime.date(2009, 7, 16): 1.073025,\n", "  datetime.date(2009, 7, 17): 1.07675,\n", "  datetime.date(2009, 7, 20): 1.067975,\n", "  datetime.date(2009, 7, 21): 1.0659,\n", "  datetime.date(2009, 7, 22): 1.065725,\n", "  datetime.date(2009, 7, 23): 1.0752,\n", "  datetime.date(2009, 7, 24): 1.07215,\n", "  datetime.date(2009, 7, 27): 1.0709,\n", "  datetime.date(2009, 7, 28): 1.075375,\n", "  datetime.date(2009, 7, 29): 1.087075,\n", "  datetime.date(2009, 7, 30): 1.08755,\n", "  datetime.date(2009, 7, 31): 1.068425,\n", "  datetime.date(2009, 8, 3): 1.05945,\n", "  datetime.date(2009, 8, 4): 1.059975,\n", "  datetime.date(2009, 8, 5): 1.0619,\n", "  datetime.date(2009, 8, 6): 1.0655,\n", "  datetime.date(2009, 8, 7): 1.081075,\n", "  datetime.date(2009, 8, 10): 1.085425,\n", "  datetime.date(2009, 8, 11): 1.081775,\n", "  datetime.date(2009, 8, 12): 1.0783,\n", "  datetime.date(2009, 8, 13): 1.0699,\n", "  datetime.date(2009, 8, 14): 1.071625,\n", "  datetime.date(2009, 8, 17): 1.077925,\n", "  datetime.date(2009, 8, 18): 1.07565,\n", "  datetime.date(2009, 8, 19): 1.06605,\n", "  datetime.date(2009, 8, 20): 1.062975,\n", "  datetime.date(2009, 8, 21): 1.058375,\n", "  datetime.date(2009, 8, 24): 1.06135,\n", "  datetime.date(2009, 8, 25): 1.061575,\n", "  datetime.date(2009, 8, 26): 1.067725,\n", "  datetime.date(2009, 8, 27): 1.059575,\n", "  datetime.date(2009, 8, 28): 1.05975,\n", "  datetime.date(2009, 8, 31): 1.059,\n", "  datetime.date(2009, 9, 1): 1.065425,\n", "  datetime.date(2009, 9, 2): 1.060975,\n", "  datetime.date(2009, 9, 3): 1.06265,\n", "  datetime.date(2009, 9, 4): 1.0605,\n", "  datetime.date(2009, 9, 7): 1.05995,\n", "  datetime.date(2009, 9, 8): 1.047325,\n", "  datetime.date(2009, 9, 9): 1.04125,\n", "  datetime.date(2009, 9, 10): 1.0385,\n", "  datetime.date(2009, 9, 11): 1.03845,\n", "  datetime.date(2009, 9, 14): 1.034625,\n", "  datetime.date(2009, 9, 15): 1.0349,\n", "  datetime.date(2009, 9, 16): 1.031975,\n", "  datetime.date(2009, 9, 17): 1.0284,\n", "  datetime.date(2009, 9, 18): 1.0296,\n", "  datetime.date(2009, 9, 21): 1.032325,\n", "  datetime.date(2009, 9, 22): 1.02345,\n", "  datetime.date(2009, 9, 23): 1.02685,\n", "  datetime.date(2009, 9, 24): 1.02975,\n", "  datetime.date(2009, 9, 25): 1.027375,\n", "  datetime.date(2009, 9, 28): 1.032425,\n", "  datetime.date(2009, 9, 29): 1.036125,\n", "  datetime.date(2009, 9, 30): 1.03635,\n", "  datetime.date(2009, 10, 1): 1.0408,\n", "  datetime.date(2009, 10, 2): 1.035225,\n", "  datetime.date(2009, 10, 5): 1.032275,\n", "  datetime.date(2009, 10, 6): 1.0269,\n", "  datetime.date(2009, 10, 7): 1.032825,\n", "  datetime.date(2009, 10, 8): 1.0258,\n", "  datetime.date(2009, 10, 9): 1.0314,\n", "  datetime.date(2009, 10, 12): 1.026925,\n", "  datetime.date(2009, 10, 13): 1.021625,\n", "  datetime.date(2009, 10, 14): 1.015,\n", "  datetime.date(2009, 10, 15): 1.01465,\n", "  datetime.date(2009, 10, 16): 1.018275,\n", "  datetime.date(2009, 10, 19): 1.011425,\n", "  datetime.date(2009, 10, 20): 1.011675,\n", "  datetime.date(2009, 10, 21): 1.006,\n", "  datetime.date(2009, 10, 22): 1.00475,\n", "  datetime.date(2009, 10, 23): 1.0088,\n", "  datetime.date(2009, 10, 26): 1.0181,\n", "  datetime.date(2009, 10, 27): 1.02185,\n", "  datetime.date(2009, 10, 28): 1.027075,\n", "  datetime.date(2009, 10, 29): 1.019025,\n", "  datetime.date(2009, 10, 30): 1.0264,\n", "  datetime.date(2009, 11, 2): 1.02165,\n", "  datetime.date(2009, 11, 3): 1.02585,\n", "  datetime.date(2009, 11, 4): 1.016175,\n", "  datetime.date(2009, 11, 5): 1.01625,\n", "  datetime.date(2009, 11, 6): 1.017375,\n", "  datetime.date(2009, 11, 9): 1.00785,\n", "  datetime.date(2009, 11, 10): 1.007575,\n", "  datetime.date(2009, 11, 11): 1.0079,\n", "  datetime.date(2009, 11, 12): 1.017375,\n", "  datetime.date(2009, 11, 13): 1.012675,\n", "  datetime.date(2009, 11, 16): 1.007575,\n", "  datetime.date(2009, 11, 17): 1.016075,\n", "  datetime.date(2009, 11, 18): 1.0098,\n", "  datetime.date(2009, 11, 19): 1.012975,\n", "  datetime.date(2009, 11, 20): 1.01805,\n", "  datetime.date(2009, 11, 23): 1.010075,\n", "  datetime.date(2009, 11, 24): 1.0088,\n", "  datetime.date(2009, 11, 25): 0.996525,\n", "  datetime.date(2009, 11, 26): 1.0028,\n", "  datetime.date(2009, 11, 27): 1.006,\n", "  datetime.date(2009, 11, 30): 1.005325,\n", "  datetime.date(2009, 12, 1): 0.9995,\n", "  datetime.date(2009, 12, 2): 1.002075,\n", "  datetime.date(2009, 12, 3): 1.0012,\n", "  datetime.date(2009, 12, 4): 1.0167,\n", "  datetime.date(2009, 12, 7): 1.01955,\n", "  datetime.date(2009, 12, 8): 1.027075,\n", "  datetime.date(2009, 12, 9): 1.026825,\n", "  datetime.date(2009, 12, 10): 1.026,\n", "  datetime.date(2009, 12, 11): 1.03455,\n", "  datetime.date(2009, 12, 14): 1.0317,\n", "  datetime.date(2009, 12, 15): 1.040325,\n", "  datetime.date(2009, 12, 16): 1.038475,\n", "  datetime.date(2009, 12, 17): 1.047575,\n", "  datetime.date(2009, 12, 18): 1.0422,\n", "  datetime.date(2009, 12, 21): 1.046125,\n", "  datetime.date(2009, 12, 22): 1.049025,\n", "  datetime.date(2009, 12, 23): 1.038925,\n", "  datetime.date(2009, 12, 24): 1.03755,\n", "  datetime.date(2009, 12, 28): 1.034775,\n", "  datetime.date(2009, 12, 29): 1.036725,\n", "  datetime.date(2009, 12, 30): 1.03665,\n", "  datetime.date(2009, 12, 31): 1.035725,\n", "  datetime.date(2010, 1, 4): 1.029775,\n", "  datetime.date(2010, 1, 5): 1.03335,\n", "  datetime.date(2010, 1, 6): 1.02755,\n", "  datetime.date(2010, 1, 7): 1.034125,\n", "  datetime.date(2010, 1, 8): 1.0239,\n", "  datetime.date(2010, 1, 11): 1.0165,\n", "  datetime.date(2010, 1, 12): 1.0186,\n", "  datetime.date(2010, 1, 13): 1.0182,\n", "  datetime.date(2010, 1, 14): 1.0188,\n", "  datetime.date(2010, 1, 15): 1.02535,\n", "  datetime.date(2010, 1, 18): 1.0248,\n", "  datetime.date(2010, 1, 19): 1.032725,\n", "  datetime.date(2010, 1, 20): 1.043925,\n", "  datetime.date(2010, 1, 21): 1.042775,\n", "  datetime.date(2010, 1, 22): 1.041175,\n", "  datetime.date(2010, 1, 25): 1.0397,\n", "  datetime.date(2010, 1, 26): 1.046375,\n", "  datetime.date(2010, 1, 27): 1.05025,\n", "  datetime.date(2010, 1, 28): 1.052175,\n", "  datetime.date(2010, 1, 29): 1.060775,\n", "  datetime.date(2010, 2, 1): 1.0564,\n", "  datetime.date(2010, 2, 2): 1.054925,\n", "  datetime.date(2010, 2, 3): 1.0595,\n", "  datetime.date(2010, 2, 4): 1.0669,\n", "  datetime.date(2010, 2, 5): 1.072425,\n", "  datetime.date(2010, 2, 8): 1.073425,\n", "  datetime.date(2010, 2, 9): 1.06375,\n", "  datetime.date(2010, 2, 10): 1.067675,\n", "  datetime.date(2010, 2, 11): 1.07045,\n", "  datetime.date(2010, 2, 12): 1.07545,\n", "  datetime.date(2010, 2, 15): 1.07765,\n", "  datetime.date(2010, 2, 16): 1.06605,\n", "  datetime.date(2010, 2, 17): 1.078325,\n", "  datetime.date(2010, 2, 18): 1.083725,\n", "  datetime.date(2010, 2, 19): 1.075625,\n", "  datetime.date(2010, 2, 22): 1.0762,\n", "  datetime.date(2010, 2, 23): 1.083925,\n", "  datetime.date(2010, 2, 24): 1.081,\n", "  datetime.date(2010, 2, 25): 1.0801,\n", "  datetime.date(2010, 2, 26): 1.073225,\n", "  datetime.date(2010, 3, 1): 1.0793,\n", "  datetime.date(2010, 3, 2): 1.074825,\n", "  datetime.date(2010, 3, 3): 1.06785,\n", "  datetime.date(2010, 3, 4): 1.07715,\n", "  datetime.date(2010, 3, 5): 1.07405,\n", "  datetime.date(2010, 3, 8): 1.073225,\n", "  datetime.date(2010, 3, 9): 1.07515,\n", "  datetime.date(2010, 3, 10): 1.069875,\n", "  datetime.date(2010, 3, 11): 1.0683,\n", "  datetime.date(2010, 3, 12): 1.057825,\n", "  datetime.date(2010, 3, 15): 1.061925,\n", "  datetime.date(2010, 3, 16): 1.05465,\n", "  datetime.date(2010, 3, 17): 1.054125,\n", "  datetime.date(2010, 3, 18): 1.058025,\n", "  datetime.date(2010, 3, 19): 1.061375,\n", "  datetime.date(2010, 3, 22): 1.058125,\n", "  datetime.date(2010, 3, 23): 1.0574,\n", "  datetime.date(2010, 3, 24): 1.072875,\n", "  datetime.date(2010, 3, 25): 1.07435,\n", "  datetime.date(2010, 3, 26): 1.065175,\n", "  datetime.date(2010, 3, 29): 1.062325,\n", "  datetime.date(2010, 3, 30): 1.06655,\n", "  datetime.date(2010, 3, 31): 1.054,\n", "  datetime.date(2010, 4, 1): 1.05405,\n", "  datetime.date(2010, 4, 2): 1.060975,\n", "  datetime.date(2010, 4, 5): 1.06215,\n", "  datetime.date(2010, 4, 6): 1.06895,\n", "  datetime.date(2010, 4, 7): 1.0739,\n", "  datetime.date(2010, 4, 8): 1.0729,\n", "  datetime.date(2010, 4, 9): 1.0658,\n", "  datetime.date(2010, 4, 12): 1.05925,\n", "  datetime.date(2010, 4, 13): 1.0537,\n", "  datetime.date(2010, 4, 14): 1.051875,\n", "  datetime.date(2010, 4, 15): 1.0564,\n", "  datetime.date(2010, 4, 16): 1.0614,\n", "  datetime.date(2010, 4, 19): 1.063075,\n", "  datetime.date(2010, 4, 20): 1.0686,\n", "  datetime.date(2010, 4, 21): 1.0705,\n", "  datetime.date(2010, 4, 22): 1.07805,\n", "  datetime.date(2010, 4, 23): 1.072475,\n", "  datetime.date(2010, 4, 26): 1.07295,\n", "  datetime.date(2010, 4, 27): 1.087825,\n", "  datetime.date(2010, 4, 28): 1.08445,\n", "  datetime.date(2010, 4, 29): 1.083825,\n", "  datetime.date(2010, 4, 30): 1.0776,\n", "  datetime.date(2010, 5, 3): 1.0858,\n", "  datetime.date(2010, 5, 4): 1.102925,\n", "  datetime.date(2010, 5, 5): 1.118075,\n", "  datetime.date(2010, 5, 6): 1.114175,\n", "  datetime.date(2010, 5, 7): 1.1082,\n", "  datetime.date(2010, 5, 10): 1.10975,\n", "  datetime.date(2010, 5, 11): 1.111725,\n", "  datetime.date(2010, 5, 12): 1.111525,\n", "  datetime.date(2010, 5, 13): 1.117625,\n", "  datetime.date(2010, 5, 14): 1.13345,\n", "  datetime.date(2010, 5, 17): 1.1312,\n", "  datetime.date(2010, 5, 18): 1.147725,\n", "  datetime.date(2010, 5, 19): 1.150475,\n", "  datetime.date(2010, 5, 20): 1.150775,\n", "  datetime.date(2010, 5, 21): 1.1497,\n", "  datetime.date(2010, 5, 24): 1.1591,\n", "  datetime.date(2010, 5, 25): 1.1571,\n", "  datetime.date(2010, 5, 26): 1.160325,\n", "  datetime.date(2010, 5, 27): 1.15105,\n", "  datetime.date(2010, 5, 28): 1.159125,\n", "  datetime.date(2010, 5, 31): 1.154875,\n", "  datetime.date(2010, 6, 1): 1.157025,\n", "  datetime.date(2010, 6, 2): 1.15485,\n", "  datetime.date(2010, 6, 3): 1.1565,\n", "  datetime.date(2010, 6, 4): 1.162125,\n", "  datetime.date(2010, 6, 7): 1.163125,\n", "  datetime.date(2010, 6, 8): 1.152625,\n", "  datetime.date(2010, 6, 9): 1.148375,\n", "  datetime.date(2010, 6, 10): 1.1419,\n", "  datetime.date(2010, 6, 11): 1.14955,\n", "  datetime.date(2010, 6, 14): 1.14205,\n", "  datetime.date(2010, 6, 15): 1.1331,\n", "  datetime.date(2010, 6, 16): 1.131075,\n", "  datetime.date(2010, 6, 17): 1.111975,\n", "  datetime.date(2010, 6, 18): 1.108525,\n", "  datetime.date(2010, 6, 21): 1.112275,\n", "  datetime.date(2010, 6, 22): 1.107375,\n", "  datetime.date(2010, 6, 23): 1.105225,\n", "  datetime.date(2010, 6, 24): 1.102975,\n", "  datetime.date(2010, 6, 25): 1.0928,\n", "  datetime.date(2010, 6, 28): 1.086875,\n", "  datetime.date(2010, 6, 29): 1.08205,\n", "  datetime.date(2010, 6, 30): 1.0774,\n", "  datetime.date(2010, 7, 1): 1.05915,\n", "  datetime.date(2010, 7, 2): 1.062725,\n", "  datetime.date(2010, 7, 5): 1.06455,\n", "  datetime.date(2010, 7, 6): 1.0592,\n", "  datetime.date(2010, 7, 7): 1.05175,\n", "  datetime.date(2010, 7, 8): 1.04915,\n", "  datetime.date(2010, 7, 9): 1.0577,\n", "  datetime.date(2010, 7, 12): 1.060425,\n", "  datetime.date(2010, 7, 13): 1.0547,\n", "  datetime.date(2010, 7, 14): 1.05255,\n", "  datetime.date(2010, 7, 15): 1.040875,\n", "  datetime.date(2010, 7, 16): 1.05055,\n", "  datetime.date(2010, 7, 19): 1.0551,\n", "  datetime.date(2010, 7, 20): 1.052825,\n", "  datetime.date(2010, 7, 21): 1.051575,\n", "  datetime.date(2010, 7, 22): 1.0429,\n", "  datetime.date(2010, 7, 23): 1.0547,\n", "  datetime.date(2010, 7, 26): 1.04855,\n", "  datetime.date(2010, 7, 27): 1.06065,\n", "  datetime.date(2010, 7, 28): 1.0567,\n", "  datetime.date(2010, 7, 29): 1.04105,\n", "  datetime.date(2010, 7, 30): 1.041225,\n", "  datetime.date(2010, 8, 2): 1.0389,\n", "  datetime.date(2010, 8, 3): 1.0392,\n", "  datetime.date(2010, 8, 4): 1.053075,\n", "  datetime.date(2010, 8, 5): 1.046225,\n", "  datetime.date(2010, 8, 6): 1.039,\n", "  datetime.date(2010, 8, 9): 1.049325,\n", "  datetime.date(2010, 8, 10): 1.048525,\n", "  datetime.date(2010, 8, 11): 1.06015,\n", "  datetime.date(2010, 8, 12): 1.050025,\n", "  datetime.date(2010, 8, 13): 1.05095,\n", "  datetime.date(2010, 8, 16): 1.039375,\n", "  datetime.date(2010, 8, 17): 1.04345,\n", "  datetime.date(2010, 8, 18): 1.042575,\n", "  datetime.date(2010, 8, 19): 1.0321,\n", "  datetime.date(2010, 8, 20): 1.033975,\n", "  datetime.date(2010, 8, 23): 1.040825,\n", "  datetime.date(2010, 8, 24): 1.03285,\n", "  datetime.date(2010, 8, 25): 1.030525,\n", "  datetime.date(2010, 8, 26): 1.02395,\n", "  datetime.date(2010, 8, 27): 1.02795,\n", "  datetime.date(2010, 8, 30): 1.0261,\n", "  datetime.date(2010, 8, 31): 1.01515,\n", "  datetime.date(2010, 9, 1): 1.015875,\n", "  datetime.date(2010, 9, 2): 1.012525,\n", "  datetime.date(2010, 9, 3): 1.016475,\n", "  datetime.date(2010, 9, 6): 1.012425,\n", "  datetime.date(2010, 9, 7): 1.01135,\n", "  datetime.date(2010, 9, 8): 1.011425,\n", "  datetime.date(2010, 9, 9): 1.0151,\n", "  datetime.date(2010, 9, 10): 1.01975,\n", "  datetime.date(2010, 9, 13): 1.0074,\n", "  datetime.date(2010, 9, 14): 0.9962,\n", "  datetime.date(2010, 9, 15): 1.00325,\n", "  datetime.date(2010, 9, 16): 1.01575,\n", "  datetime.date(2010, 9, 17): 1.00975,\n", "  datetime.date(2010, 9, 20): 1.0049,\n", "  datetime.date(2010, 9, 21): 0.996375,\n", "  datetime.date(2010, 9, 22): 0.98615,\n", "  datetime.date(2010, 9, 23): 0.985475,\n", "  datetime.date(2010, 9, 24): 0.98185,\n", "  datetime.date(2010, 9, 27): 0.985325,\n", "  datetime.date(2010, 9, 28): 0.9757,\n", "  datetime.date(2010, 9, 29): 0.977025,\n", "  datetime.date(2010, 9, 30): 0.98255,\n", "  datetime.date(2010, 10, 1): 0.973725,\n", "  datetime.date(2010, 10, 4): 0.9715,\n", "  datetime.date(2010, 10, 5): 0.9662,\n", "  datetime.date(2010, 10, 6): 0.961625,\n", "  datetime.date(2010, 10, 7): 0.966225,\n", "  datetime.date(2010, 10, 8): 0.96265,\n", "  datetime.date(2010, 10, 11): 0.96485,\n", "  datetime.date(2010, 10, 12): 0.9568,\n", "  datetime.date(2010, 10, 13): 0.958375,\n", "  datetime.date(2010, 10, 14): 0.952975,\n", "  datetime.date(2010, 10, 15): 0.959,\n", "  datetime.date(2010, 10, 18): 0.95945,\n", "  datetime.date(2010, 10, 19): 0.9718,\n", "  datetime.date(2010, 10, 20): 0.96175,\n", "  datetime.date(2010, 10, 21): 0.967875,\n", "  datetime.date(2010, 10, 22): 0.977125,\n", "  datetime.date(2010, 10, 25): 0.97105,\n", "  datetime.date(2010, 10, 26): 0.984275,\n", "  datetime.date(2010, 10, 27): 0.99035,\n", "  datetime.date(2010, 10, 28): 0.98335,\n", "  datetime.date(2010, 10, 29): 0.982425,\n", "  datetime.date(2010, 11, 1): 0.991825,\n", "  datetime.date(2010, 11, 2): 0.97955,\n", "  datetime.date(2010, 11, 3): 0.970775,\n", "  datetime.date(2010, 11, 4): 0.95845,\n", "  datetime.date(2010, 11, 5): 0.9612,\n", "  datetime.date(2010, 11, 8): 0.96605,\n", "  datetime.date(2010, 11, 9): 0.9682,\n", "  datetime.date(2010, 11, 10): 0.970775,\n", "  datetime.date(2010, 11, 11): 0.975075,\n", "  datetime.date(2010, 11, 12): 0.980875,\n", "  datetime.date(2010, 11, 15): 0.9846,\n", "  datetime.date(2010, 11, 16): 0.9959,\n", "  datetime.date(2010, 11, 17): 0.99145,\n", "  datetime.date(2010, 11, 18): 0.9958,\n", "  datetime.date(2010, 11, 19): 0.992275,\n", "  datetime.date(2010, 11, 22): 0.989575,\n", "  datetime.date(2010, 11, 23): 0.997375,\n", "  datetime.date(2010, 11, 24): 0.996075,\n", "  datetime.date(2010, 11, 25): 1.0006,\n", "  datetime.date(2010, 11, 26): 1.0033,\n", "  datetime.date(2010, 11, 29): 1.00005,\n", "  datetime.date(2010, 11, 30): 1.0035,\n", "  datetime.date(2010, 12, 1): 1.002325,\n", "  datetime.date(2010, 12, 2): 0.9928,\n", "  datetime.date(2010, 12, 3): 0.97355,\n", "  datetime.date(2010, 12, 6): 0.981675,\n", "  datetime.date(2010, 12, 7): 0.98765,\n", "  datetime.date(2010, 12, 8): 0.9861,\n", "  datetime.date(2010, 12, 9): 0.983475,\n", "  datetime.date(2010, 12, 10): 0.980975,\n", "  datetime.date(2010, 12, 13): 0.96775,\n", "  datetime.date(2010, 12, 14): 0.9596,\n", "  datetime.date(2010, 12, 15): 0.968125,\n", "  datetime.date(2010, 12, 16): 0.964425,\n", "  datetime.date(2010, 12, 17): 0.96875,\n", "  datetime.date(2010, 12, 20): 0.96485,\n", "  datetime.date(2010, 12, 21): 0.958075,\n", "  datetime.date(2010, 12, 22): 0.95205,\n", "  datetime.date(2010, 12, 23): 0.958775,\n", "  datetime.date(2010, 12, 24): 0.962125,\n", "  datetime.date(2010, 12, 27): 0.960225,\n", "  datetime.date(2010, 12, 28): 0.951975,\n", "  datetime.date(2010, 12, 29): 0.945575,\n", "  datetime.date(2010, 12, 30): 0.9351,\n", "  datetime.date(2010, 12, 31): 0.9356,\n", "  datetime.date(2011, 1, 3): 0.933375,\n", "  datetime.date(2011, 1, 4): 0.9486,\n", "  datetime.date(2011, 1, 5): 0.9659,\n", "  datetime.date(2011, 1, 6): 0.96545,\n", "  datetime.date(2011, 1, 7): 0.967425,\n", "  datetime.date(2011, 1, 10): 0.967525,\n", "  datetime.date(2011, 1, 11): 0.9737,\n", "  datetime.date(2011, 1, 12): 0.966625,\n", "  datetime.date(2011, 1, 13): 0.9638,\n", "  datetime.date(2011, 1, 14): 0.963425,\n", "  datetime.date(2011, 1, 17): 0.96465,\n", "  datetime.date(2011, 1, 18): 0.963375,\n", "  datetime.date(2011, 1, 19): 0.95485,\n", "  datetime.date(2011, 1, 20): 0.9673,\n", "  datetime.date(2011, 1, 21): 0.9583,\n", "  datetime.date(2011, 1, 24): 0.94915,\n", "  datetime.date(2011, 1, 25): 0.9424,\n", "  datetime.date(2011, 1, 26): 0.941625,\n", "  datetime.date(2011, 1, 27): 0.94545,\n", "  datetime.date(2011, 1, 28): 0.94195,\n", "  datetime.date(2011, 1, 31): 0.944025,\n", "  datetime.date(2011, 2, 1): 0.935375,\n", "  datetime.date(2011, 2, 2): 0.940025,\n", "  datetime.date(2011, 2, 3): 0.9456,\n", "  datetime.date(2011, 2, 4): 0.9551,\n", "  datetime.date(2011, 2, 7): 0.9555,\n", "  datetime.date(2011, 2, 8): 0.963275,\n", "  datetime.date(2011, 2, 9): 0.957775,\n", "  datetime.date(2011, 2, 10): 0.969075,\n", "  datetime.date(2011, 2, 11): 0.973175,\n", "  datetime.date(2011, 2, 14): 0.96995,\n", "  datetime.date(2011, 2, 15): 0.9671,\n", "  datetime.date(2011, 2, 16): 0.959375,\n", "  datetime.date(2011, 2, 17): 0.94965,\n", "  datetime.date(2011, 2, 18): 0.944675,\n", "  datetime.date(2011, 2, 21): 0.9468,\n", "  datetime.date(2011, 2, 22): 0.93885,\n", "  datetime.date(2011, 2, 23): 0.93305,\n", "  datetime.date(2011, 2, 24): 0.92635,\n", "  datetime.date(2011, 2, 25): 0.9282,\n", "  datetime.date(2011, 2, 28): 0.928875,\n", "  datetime.date(2011, 3, 1): 0.928425,\n", "  datetime.date(2011, 3, 2): 0.923575,\n", "  datetime.date(2011, 3, 3): 0.9316,\n", "  datetime.date(2011, 3, 4): 0.924675,\n", "  datetime.date(2011, 3, 7): 0.926625,\n", "  datetime.date(2011, 3, 8): 0.935125,\n", "  datetime.date(2011, 3, 9): 0.929525,\n", "  datetime.date(2011, 3, 10): 0.93205,\n", "  datetime.date(2011, 3, 11): 0.929775,\n", "  datetime.date(2011, 3, 14): 0.92435,\n", "  datetime.date(2011, 3, 15): 0.91655,\n", "  datetime.date(2011, 3, 16): 0.9081,\n", "  datetime.date(2011, 3, 17): 0.898575,\n", "  datetime.date(2011, 3, 18): 0.9008,\n", "  datetime.date(2011, 3, 21): 0.904525,\n", "  datetime.date(2011, 3, 22): 0.903475,\n", "  datetime.date(2011, 3, 23): 0.908475,\n", "  datetime.date(2011, 3, 24): 0.908475,\n", "  datetime.date(2011, 3, 25): 0.9199,\n", "  datetime.date(2011, 3, 28): 0.9168,\n", "  datetime.date(2011, 3, 29): 0.920125,\n", "  datetime.date(2011, 3, 30): 0.918375,\n", "  datetime.date(2011, 3, 31): 0.919175,\n", "  datetime.date(2011, 4, 1): 0.923625,\n", "  datetime.date(2011, 4, 4): 0.923075,\n", "  datetime.date(2011, 4, 5): 0.925375,\n", "  datetime.date(2011, 4, 6): 0.918975,\n", "  datetime.date(2011, 4, 7): 0.916425,\n", "  datetime.date(2011, 4, 8): 0.906475,\n", "  datetime.date(2011, 4, 11): 0.906675,\n", "  datetime.date(2011, 4, 12): 0.89695,\n", "  datetime.date(2011, 4, 13): 0.89635,\n", "  datetime.date(2011, 4, 14): 0.892525,\n", "  datetime.date(2011, 4, 15): 0.892175,\n", "  datetime.date(2011, 4, 18): 0.89645,\n", "  datetime.date(2011, 4, 19): 0.899875,\n", "  datetime.date(2011, 4, 20): 0.887925,\n", "  datetime.date(2011, 4, 21): 0.886175,\n", "  datetime.date(2011, 4, 22): 0.886175,\n", "  datetime.date(2011, 4, 25): 0.881175,\n", "  datetime.date(2011, 4, 26): 0.875225,\n", "  datetime.date(2011, 4, 27): 0.87465,\n", "  datetime.date(2011, 4, 28): 0.873625,\n", "  datetime.date(2011, 4, 29): 0.865425,\n", "  datetime.date(2011, 5, 2): 0.8652,\n", "  datetime.date(2011, 5, 3): 0.861275,\n", "  datetime.date(2011, 5, 4): 0.86175,\n", "  datetime.date(2011, 5, 5): 0.87045,\n", "  datetime.date(2011, 5, 6): 0.87885,\n", "  datetime.date(2011, 5, 9): 0.871775,\n", "  datetime.date(2011, 5, 10): 0.87985,\n", "  datetime.date(2011, 5, 11): 0.887775,\n", "  datetime.date(2011, 5, 12): 0.884075,\n", "  datetime.date(2011, 5, 13): 0.8925,\n", "  datetime.date(2011, 5, 16): 0.88455,\n", "  datetime.date(2011, 5, 17): 0.880175,\n", "  datetime.date(2011, 5, 18): 0.880925,\n", "  datetime.date(2011, 5, 19): 0.8808,\n", "  datetime.date(2011, 5, 20): 0.877475,\n", "  datetime.date(2011, 5, 23): 0.88375,\n", "  datetime.date(2011, 5, 24): 0.880025,\n", "  datetime.date(2011, 5, 25): 0.87255,\n", "  datetime.date(2011, 5, 26): 0.865575,\n", "  datetime.date(2011, 5, 27): 0.8492,\n", "  datetime.date(2011, 5, 30): 0.85225,\n", "  datetime.date(2011, 5, 31): 0.854025,\n", "  datetime.date(2011, 6, 1): 0.841925,\n", "  datetime.date(2011, 6, 2): 0.842475,\n", "  datetime.date(2011, 6, 3): 0.83375,\n", "  datetime.date(2011, 6, 6): 0.83465,\n", "  datetime.date(2011, 6, 7): 0.83625,\n", "  datetime.date(2011, 6, 8): 0.836025,\n", "  datetime.date(2011, 6, 9): 0.841425,\n", "  datetime.date(2011, 6, 10): 0.843025,\n", "  datetime.date(2011, 6, 13): 0.8372,\n", "  datetime.date(2011, 6, 14): 0.845375,\n", "  datetime.date(2011, 6, 15): 0.852975,\n", "  datetime.date(2011, 6, 16): 0.847625,\n", "  datetime.date(2011, 6, 17): 0.848975,\n", "  datetime.date(2011, 6, 20): 0.846275,\n", "  datetime.date(2011, 6, 21): 0.8405,\n", "  datetime.date(2011, 6, 22): 0.8392,\n", "  datetime.date(2011, 6, 23): 0.838825,\n", "  datetime.date(2011, 6, 24): 0.833225,\n", "  datetime.date(2011, 6, 27): 0.835225,\n", "  datetime.date(2011, 6, 28): 0.83195,\n", "  datetime.date(2011, 6, 29): 0.834125,\n", "  datetime.date(2011, 6, 30): 0.84045,\n", "  datetime.date(2011, 7, 1): 0.847725,\n", "  datetime.date(2011, 7, 4): 0.848075,\n", "  datetime.date(2011, 7, 5): 0.840625,\n", "  datetime.date(2011, 7, 6): 0.839475,\n", "  datetime.date(2011, 7, 7): 0.844275,\n", "  datetime.date(2011, 7, 8): 0.8364,\n", "  datetime.date(2011, 7, 11): 0.83585,\n", "  datetime.date(2011, 7, 12): 0.830575,\n", "  datetime.date(2011, 7, 13): 0.81785,\n", "  datetime.date(2011, 7, 14): 0.816,\n", "  datetime.date(2011, 7, 15): 0.815025,\n", "  datetime.date(2011, 7, 18): 0.817625,\n", "  datetime.date(2011, 7, 19): 0.82415,\n", "  datetime.date(2011, 7, 20): 0.8197,\n", "  datetime.date(2011, 7, 21): 0.8151,\n", "  datetime.date(2011, 7, 22): 0.8198,\n", "  datetime.date(2011, 7, 25): 0.80605,\n", "  datetime.date(2011, 7, 26): 0.8013,\n", "  datetime.date(2011, 7, 27): 0.8017,\n", "  datetime.date(2011, 7, 28): 0.801175,\n", "  datetime.date(2011, 7, 29): 0.785525,\n", "  datetime.date(2011, 8, 1): 0.783575,\n", "  datetime.date(2011, 8, 2): 0.76225,\n", "  datetime.date(2011, 8, 3): 0.77035,\n", "  datetime.date(2011, 8, 4): 0.763725,\n", "  datetime.date(2011, 8, 5): 0.767375,\n", "  datetime.date(2011, 8, 8): 0.75505,\n", "  datetime.date(2011, 8, 9): 0.720825,\n", "  datetime.date(2011, 8, 10): 0.72655,\n", "  datetime.date(2011, 8, 11): 0.76145,\n", "  datetime.date(2011, 8, 12): 0.7777,\n", "  datetime.date(2011, 8, 15): 0.78425,\n", "  datetime.date(2011, 8, 16): 0.79585,\n", "  datetime.date(2011, 8, 17): 0.79005,\n", "  datetime.date(2011, 8, 18): 0.794125,\n", "  datetime.date(2011, 8, 19): 0.785075,\n", "  datetime.date(2011, 8, 22): 0.790275,\n", "  datetime.date(2011, 8, 23): 0.7923,\n", "  datetime.date(2011, 8, 24): 0.795925,\n", "  datetime.date(2011, 8, 25): 0.792975,\n", "  datetime.date(2011, 8, 26): 0.8063,\n", "  datetime.date(2011, 8, 29): 0.815775,\n", "  datetime.date(2011, 8, 30): 0.820075,\n", "  datetime.date(2011, 8, 31): 0.805925,\n", "  datetime.date(2011, 9, 1): 0.795525,\n", "  datetime.date(2011, 9, 2): 0.7883,\n", "  datetime.date(2011, 9, 5): 0.787175,\n", "  datetime.date(2011, 9, 6): 0.86205,\n", "  datetime.date(2011, 9, 7): 0.8574,\n", "  datetime.date(2011, 9, 8): 0.875575,\n", "  datetime.date(2011, 9, 9): 0.8838,\n", "  datetime.date(2011, 9, 12): 0.880375,\n", "  datetime.date(2011, 9, 13): 0.88035,\n", "  datetime.date(2011, 9, 14): 0.875975,\n", "  datetime.date(2011, 9, 15): 0.869425,\n", "  datetime.date(2011, 9, 16): 0.875875,\n", "  datetime.date(2011, 9, 19): 0.882075,\n", "  datetime.date(2011, 9, 20): 0.887475,\n", "  datetime.date(2011, 9, 21): 0.899975,\n", "  datetime.date(2011, 9, 22): 0.90865,\n", "  datetime.date(2011, 9, 23): 0.905675,\n", "  datetime.date(2011, 9, 26): 0.90145,\n", "  datetime.date(2011, 9, 27): 0.89605,\n", "  datetime.date(2011, 9, 28): 0.900175,\n", "  datetime.date(2011, 9, 29): 0.8971,\n", "  datetime.date(2011, 9, 30): 0.9081,\n", "  datetime.date(2011, 10, 3): 0.9216,\n", "  datetime.date(2011, 10, 4): 0.916375,\n", "  ...},\n", " 'EURUSD': {datetime.date(2007, 11, 2): 1.450475,\n", "  datetime.date(2007, 11, 9): 1.467825,\n", "  datetime.date(2007, 11, 29): 1.4744,\n", "  datetime.date(2007, 11, 30): 1.46335,\n", "  datetime.date(2007, 12, 3): 1.46675,\n", "  datetime.date(2007, 12, 4): 1.47585,\n", "  datetime.date(2007, 12, 5): 1.461075,\n", "  datetime.date(2007, 12, 6): 1.46375,\n", "  datetime.date(2007, 12, 7): 1.4658,\n", "  datetime.date(2007, 12, 10): 1.4712,\n", "  datetime.date(2007, 12, 11): 1.4655,\n", "  datetime.date(2007, 12, 12): 1.470575,\n", "  datetime.date(2007, 12, 13): 1.463325,\n", "  datetime.date(2007, 12, 14): 1.44295,\n", "  datetime.date(2007, 12, 17): 1.44005,\n", "  datetime.date(2007, 12, 18): 1.4414,\n", "  datetime.date(2007, 12, 19): 1.43835,\n", "  datetime.date(2007, 12, 20): 1.43295,\n", "  datetime.date(2007, 12, 21): 1.4382,\n", "  datetime.date(2007, 12, 24): 1.44015,\n", "  datetime.date(2007, 12, 26): 1.4489,\n", "  datetime.date(2007, 12, 27): 1.46265,\n", "  datetime.date(2007, 12, 28): 1.4724,\n", "  datetime.date(2007, 12, 31): 1.459,\n", "  datetime.date(2008, 1, 2): 1.47145,\n", "  datetime.date(2008, 1, 3): 1.475125,\n", "  datetime.date(2008, 1, 4): 1.47425,\n", "  datetime.date(2008, 1, 7): 1.46955,\n", "  datetime.date(2008, 1, 8): 1.47075,\n", "  datetime.date(2008, 1, 9): 1.46595,\n", "  datetime.date(2008, 1, 10): 1.480425,\n", "  datetime.date(2008, 1, 11): 1.47765,\n", "  datetime.date(2008, 1, 14): 1.4869,\n", "  datetime.date(2008, 1, 15): 1.480425,\n", "  datetime.date(2008, 1, 16): 1.465225,\n", "  datetime.date(2008, 1, 17): 1.4642,\n", "  datetime.date(2008, 1, 18): 1.46205,\n", "  datetime.date(2008, 1, 21): 1.445425,\n", "  datetime.date(2008, 1, 22): 1.46295,\n", "  datetime.date(2008, 1, 23): 1.463,\n", "  datetime.date(2008, 1, 24): 1.47555,\n", "  datetime.date(2008, 1, 25): 1.468175,\n", "  datetime.date(2008, 1, 28): 1.4781,\n", "  datetime.date(2008, 1, 29): 1.47765,\n", "  datetime.date(2008, 1, 30): 1.4862,\n", "  datetime.date(2008, 1, 31): 1.48605,\n", "  datetime.date(2008, 2, 1): 1.48025,\n", "  datetime.date(2008, 2, 4): 1.48295,\n", "  datetime.date(2008, 2, 5): 1.464775,\n", "  datetime.date(2008, 2, 6): 1.4632,\n", "  datetime.date(2008, 2, 7): 1.44855,\n", "  datetime.date(2008, 2, 8): 1.450525,\n", "  datetime.date(2008, 2, 11): 1.451875,\n", "  datetime.date(2008, 2, 12): 1.4584,\n", "  datetime.date(2008, 2, 13): 1.45735,\n", "  datetime.date(2008, 2, 14): 1.4643,\n", "  datetime.date(2008, 2, 15): 1.4682,\n", "  datetime.date(2008, 2, 18): 1.465775,\n", "  datetime.date(2008, 2, 19): 1.47255,\n", "  datetime.date(2008, 2, 20): 1.4715,\n", "  datetime.date(2008, 2, 21): 1.4814,\n", "  datetime.date(2008, 2, 22): 1.48265,\n", "  datetime.date(2008, 2, 25): 1.483,\n", "  datetime.date(2008, 2, 26): 1.49745,\n", "  datetime.date(2008, 2, 27): 1.512025,\n", "  datetime.date(2008, 2, 28): 1.519375,\n", "  datetime.date(2008, 2, 29): 1.51785,\n", "  datetime.date(2008, 3, 3): 1.5204,\n", "  datetime.date(2008, 3, 4): 1.52165,\n", "  datetime.date(2008, 3, 5): 1.526475,\n", "  datetime.date(2008, 3, 6): 1.53805,\n", "  datetime.date(2008, 3, 7): 1.53555,\n", "  datetime.date(2008, 3, 10): 1.53435,\n", "  datetime.date(2008, 3, 11): 1.5338,\n", "  datetime.date(2008, 3, 12): 1.5551,\n", "  datetime.date(2008, 3, 13): 1.56355,\n", "  datetime.date(2008, 3, 14): 1.567325,\n", "  datetime.date(2008, 3, 17): 1.57295,\n", "  datetime.date(2008, 3, 18): 1.5624,\n", "  datetime.date(2008, 3, 19): 1.5626,\n", "  datetime.date(2008, 3, 20): 1.542775,\n", "  datetime.date(2008, 3, 21): 1.5434,\n", "  datetime.date(2008, 3, 24): 1.542275,\n", "  datetime.date(2008, 3, 25): 1.564975,\n", "  datetime.date(2008, 3, 26): 1.58455,\n", "  datetime.date(2008, 3, 27): 1.57785,\n", "  datetime.date(2008, 3, 28): 1.579575,\n", "  datetime.date(2008, 3, 31): 1.578825,\n", "  datetime.date(2008, 4, 1): 1.56145,\n", "  datetime.date(2008, 4, 2): 1.5686,\n", "  datetime.date(2008, 4, 3): 1.56845,\n", "  datetime.date(2008, 4, 4): 1.57375,\n", "  datetime.date(2008, 4, 7): 1.571125,\n", "  datetime.date(2008, 4, 8): 1.571225,\n", "  datetime.date(2008, 4, 9): 1.58315,\n", "  datetime.date(2008, 4, 10): 1.57425,\n", "  datetime.date(2008, 4, 11): 1.5809,\n", "  datetime.date(2008, 4, 14): 1.5832,\n", "  datetime.date(2008, 4, 15): 1.5789,\n", "  datetime.date(2008, 4, 16): 1.59475,\n", "  datetime.date(2008, 4, 17): 1.5908,\n", "  datetime.date(2008, 4, 18): 1.58175,\n", "  datetime.date(2008, 4, 21): 1.591175,\n", "  datetime.date(2008, 4, 22): 1.59915,\n", "  datetime.date(2008, 4, 23): 1.58895,\n", "  datetime.date(2008, 4, 24): 1.5682,\n", "  datetime.date(2008, 4, 25): 1.563025,\n", "  datetime.date(2008, 4, 28): 1.5658,\n", "  datetime.date(2008, 4, 29): 1.55715,\n", "  datetime.date(2008, 4, 30): 1.56225,\n", "  datetime.date(2008, 5, 1): 1.54745,\n", "  datetime.date(2008, 5, 2): 1.5424,\n", "  datetime.date(2008, 5, 5): 1.5497,\n", "  datetime.date(2008, 5, 6): 1.55325,\n", "  datetime.date(2008, 5, 7): 1.539325,\n", "  datetime.date(2008, 5, 8): 1.53935,\n", "  datetime.date(2008, 5, 9): 1.5483,\n", "  datetime.date(2008, 5, 12): 1.5553,\n", "  datetime.date(2008, 5, 13): 1.5474,\n", "  datetime.date(2008, 5, 14): 1.5474,\n", "  datetime.date(2008, 5, 15): 1.54485,\n", "  datetime.date(2008, 5, 16): 1.557825,\n", "  datetime.date(2008, 5, 19): 1.55105,\n", "  datetime.date(2008, 5, 20): 1.5646,\n", "  datetime.date(2008, 5, 21): 1.57945,\n", "  datetime.date(2008, 5, 22): 1.57325,\n", "  datetime.date(2008, 5, 23): 1.5762,\n", "  datetime.date(2008, 5, 26): 1.577,\n", "  datetime.date(2008, 5, 27): 1.569225,\n", "  datetime.date(2008, 5, 28): 1.5639,\n", "  datetime.date(2008, 5, 29): 1.55185,\n", "  datetime.date(2008, 5, 30): 1.555475,\n", "  datetime.date(2008, 6, 2): 1.5537,\n", "  datetime.date(2008, 6, 3): 1.54445,\n", "  datetime.date(2008, 6, 4): 1.544,\n", "  datetime.date(2008, 6, 5): 1.55935,\n", "  datetime.date(2008, 6, 6): 1.577875,\n", "  datetime.date(2008, 6, 9): 1.56465,\n", "  datetime.date(2008, 6, 10): 1.5467,\n", "  datetime.date(2008, 6, 11): 1.5553,\n", "  datetime.date(2008, 6, 12): 1.5439,\n", "  datetime.date(2008, 6, 13): 1.537975,\n", "  datetime.date(2008, 6, 16): 1.547675,\n", "  datetime.date(2008, 6, 17): 1.5511,\n", "  datetime.date(2008, 6, 18): 1.5535,\n", "  datetime.date(2008, 6, 19): 1.5504,\n", "  datetime.date(2008, 6, 20): 1.56065,\n", "  datetime.date(2008, 6, 23): 1.551775,\n", "  datetime.date(2008, 6, 24): 1.5568,\n", "  datetime.date(2008, 6, 25): 1.56665,\n", "  datetime.date(2008, 6, 26): 1.57565,\n", "  datetime.date(2008, 6, 27): 1.57945,\n", "  datetime.date(2008, 6, 30): 1.5756,\n", "  datetime.date(2008, 7, 1): 1.57925,\n", "  datetime.date(2008, 7, 2): 1.58825,\n", "  datetime.date(2008, 7, 3): 1.57035,\n", "  datetime.date(2008, 7, 4): 1.5703,\n", "  datetime.date(2008, 7, 7): 1.5726,\n", "  datetime.date(2008, 7, 8): 1.56705,\n", "  datetime.date(2008, 7, 9): 1.5743,\n", "  datetime.date(2008, 7, 10): 1.57885,\n", "  datetime.date(2008, 7, 11): 1.593875,\n", "  datetime.date(2008, 7, 14): 1.590775,\n", "  datetime.date(2008, 7, 15): 1.5911,\n", "  datetime.date(2008, 7, 16): 1.5827,\n", "  datetime.date(2008, 7, 17): 1.5862,\n", "  datetime.date(2008, 7, 18): 1.58485,\n", "  datetime.date(2008, 7, 21): 1.59225,\n", "  datetime.date(2008, 7, 22): 1.578475,\n", "  datetime.date(2008, 7, 23): 1.569875,\n", "  datetime.date(2008, 7, 24): 1.56785,\n", "  datetime.date(2008, 7, 25): 1.57095,\n", "  datetime.date(2008, 7, 28): 1.574125,\n", "  datetime.date(2008, 7, 29): 1.558825,\n", "  datetime.date(2008, 7, 30): 1.55755,\n", "  datetime.date(2008, 7, 31): 1.560325,\n", "  datetime.date(2008, 8, 1): 1.55635,\n", "  datetime.date(2008, 8, 4): 1.557675,\n", "  datetime.date(2008, 8, 5): 1.5454,\n", "  datetime.date(2008, 8, 6): 1.540725,\n", "  datetime.date(2008, 8, 7): 1.53245,\n", "  datetime.date(2008, 8, 8): 1.50055,\n", "  datetime.date(2008, 8, 11): 1.490975,\n", "  datetime.date(2008, 8, 12): 1.4926,\n", "  datetime.date(2008, 8, 13): 1.4919,\n", "  datetime.date(2008, 8, 14): 1.482575,\n", "  datetime.date(2008, 8, 15): 1.46875,\n", "  datetime.date(2008, 8, 18): 1.46945,\n", "  datetime.date(2008, 8, 19): 1.4776,\n", "  datetime.date(2008, 8, 20): 1.47475,\n", "  datetime.date(2008, 8, 21): 1.49,\n", "  datetime.date(2008, 8, 22): 1.47925,\n", "  datetime.date(2008, 8, 25): 1.4754,\n", "  datetime.date(2008, 8, 26): 1.46535,\n", "  datetime.date(2008, 8, 27): 1.47275,\n", "  datetime.date(2008, 8, 28): 1.470575,\n", "  datetime.date(2008, 8, 29): 1.46735,\n", "  datetime.date(2008, 9, 1): 1.4617,\n", "  datetime.date(2008, 9, 2): 1.45195,\n", "  datetime.date(2008, 9, 3): 1.44985,\n", "  datetime.date(2008, 9, 4): 1.432575,\n", "  datetime.date(2008, 9, 5): 1.42655,\n", "  datetime.date(2008, 9, 8): 1.41295,\n", "  datetime.date(2008, 9, 9): 1.4133,\n", "  datetime.date(2008, 9, 10): 1.399775,\n", "  datetime.date(2008, 9, 11): 1.399625,\n", "  datetime.date(2008, 9, 12): 1.4228,\n", "  datetime.date(2008, 9, 15): 1.42435,\n", "  datetime.date(2008, 9, 16): 1.413,\n", "  datetime.date(2008, 9, 17): 1.4326,\n", "  datetime.date(2008, 9, 18): 1.43485,\n", "  datetime.date(2008, 9, 19): 1.4468,\n", "  datetime.date(2008, 9, 22): 1.477475,\n", "  datetime.date(2008, 9, 23): 1.4647,\n", "  datetime.date(2008, 9, 24): 1.46215,\n", "  datetime.date(2008, 9, 25): 1.460875,\n", "  datetime.date(2008, 9, 26): 1.4613,\n", "  datetime.date(2008, 9, 29): 1.443475,\n", "  datetime.date(2008, 9, 30): 1.40925,\n", "  datetime.date(2008, 10, 1): 1.40085,\n", "  datetime.date(2008, 10, 2): 1.38195,\n", "  datetime.date(2008, 10, 3): 1.3775,\n", "  datetime.date(2008, 10, 6): 1.35005,\n", "  datetime.date(2008, 10, 7): 1.35885,\n", "  datetime.date(2008, 10, 8): 1.3654,\n", "  datetime.date(2008, 10, 9): 1.36055,\n", "  datetime.date(2008, 10, 10): 1.34075,\n", "  datetime.date(2008, 10, 13): 1.358025,\n", "  datetime.date(2008, 10, 14): 1.3619,\n", "  datetime.date(2008, 10, 15): 1.3499,\n", "  datetime.date(2008, 10, 16): 1.345525,\n", "  datetime.date(2008, 10, 17): 1.34105,\n", "  datetime.date(2008, 10, 20): 1.33435,\n", "  datetime.date(2008, 10, 21): 1.30625,\n", "  datetime.date(2008, 10, 22): 1.28535,\n", "  datetime.date(2008, 10, 23): 1.29345,\n", "  datetime.date(2008, 10, 24): 1.26245,\n", "  datetime.date(2008, 10, 27): 1.249425,\n", "  datetime.date(2008, 10, 28): 1.2682,\n", "  datetime.date(2008, 10, 29): 1.296075,\n", "  datetime.date(2008, 10, 30): 1.2915,\n", "  datetime.date(2008, 10, 31): 1.27235,\n", "  datetime.date(2008, 11, 3): 1.26435,\n", "  datetime.date(2008, 11, 4): 1.29815,\n", "  datetime.date(2008, 11, 5): 1.295525,\n", "  datetime.date(2008, 11, 6): 1.2714,\n", "  datetime.date(2008, 11, 7): 1.27185,\n", "  datetime.date(2008, 11, 10): 1.274875,\n", "  datetime.date(2008, 11, 11): 1.252225,\n", "  datetime.date(2008, 11, 12): 1.250575,\n", "  datetime.date(2008, 11, 13): 1.276975,\n", "  datetime.date(2008, 11, 14): 1.2603,\n", "  datetime.date(2008, 11, 17): 1.264925,\n", "  datetime.date(2008, 11, 18): 1.26185,\n", "  datetime.date(2008, 11, 19): 1.248875,\n", "  datetime.date(2008, 11, 20): 1.2453,\n", "  datetime.date(2008, 11, 21): 1.2588,\n", "  datetime.date(2008, 11, 24): 1.295375,\n", "  datetime.date(2008, 11, 25): 1.306475,\n", "  datetime.date(2008, 11, 26): 1.2879,\n", "  datetime.date(2008, 11, 27): 1.29045,\n", "  datetime.date(2008, 11, 28): 1.26915,\n", "  datetime.date(2008, 12, 1): 1.261125,\n", "  datetime.date(2008, 12, 2): 1.27145,\n", "  datetime.date(2008, 12, 3): 1.271675,\n", "  datetime.date(2008, 12, 4): 1.2777,\n", "  datetime.date(2008, 12, 5): 1.271525,\n", "  datetime.date(2008, 12, 8): 1.296325,\n", "  datetime.date(2008, 12, 9): 1.292675,\n", "  datetime.date(2008, 12, 10): 1.30225,\n", "  datetime.date(2008, 12, 11): 1.3352,\n", "  datetime.date(2008, 12, 12): 1.33675,\n", "  datetime.date(2008, 12, 15): 1.36885,\n", "  datetime.date(2008, 12, 16): 1.4,\n", "  datetime.date(2008, 12, 17): 1.44205,\n", "  datetime.date(2008, 12, 18): 1.42495,\n", "  datetime.date(2008, 12, 19): 1.39125,\n", "  datetime.date(2008, 12, 22): 1.39445,\n", "  datetime.date(2008, 12, 23): 1.392775,\n", "  datetime.date(2008, 12, 24): 1.40095,\n", "  datetime.date(2008, 12, 26): 1.402875,\n", "  datetime.date(2008, 12, 29): 1.392675,\n", "  datetime.date(2008, 12, 30): 1.40575,\n", "  datetime.date(2008, 12, 31): 1.39665,\n", "  datetime.date(2009, 1, 2): 1.39225,\n", "  datetime.date(2009, 1, 5): 1.36355,\n", "  datetime.date(2009, 1, 6): 1.35365,\n", "  datetime.date(2009, 1, 7): 1.36465,\n", "  datetime.date(2009, 1, 8): 1.37025,\n", "  datetime.date(2009, 1, 9): 1.347675,\n", "  datetime.date(2009, 1, 12): 1.336075,\n", "  datetime.date(2009, 1, 13): 1.3183,\n", "  datetime.date(2009, 1, 14): 1.319025,\n", "  datetime.date(2009, 1, 15): 1.311575,\n", "  datetime.date(2009, 1, 16): 1.32725,\n", "  datetime.date(2009, 1, 19): 1.3069,\n", "  datetime.date(2009, 1, 20): 1.290525,\n", "  datetime.date(2009, 1, 21): 1.3023,\n", "  datetime.date(2009, 1, 22): 1.3001,\n", "  datetime.date(2009, 1, 23): 1.29745,\n", "  datetime.date(2009, 1, 26): 1.318875,\n", "  datetime.date(2009, 1, 27): 1.31615,\n", "  datetime.date(2009, 1, 28): 1.316575,\n", "  datetime.date(2009, 1, 29): 1.29535,\n", "  datetime.date(2009, 1, 30): 1.281275,\n", "  datetime.date(2009, 2, 2): 1.284375,\n", "  datetime.date(2009, 2, 3): 1.304075,\n", "  datetime.date(2009, 2, 4): 1.2849,\n", "  datetime.date(2009, 2, 5): 1.2791,\n", "  datetime.date(2009, 2, 6): 1.2943,\n", "  datetime.date(2009, 2, 9): 1.3003,\n", "  datetime.date(2009, 2, 10): 1.29135,\n", "  datetime.date(2009, 2, 11): 1.2906,\n", "  datetime.date(2009, 2, 12): 1.28605,\n", "  datetime.date(2009, 2, 13): 1.286075,\n", "  datetime.date(2009, 2, 16): 1.2803,\n", "  datetime.date(2009, 2, 17): 1.258225,\n", "  datetime.date(2009, 2, 18): 1.252975,\n", "  datetime.date(2009, 2, 19): 1.267375,\n", "  datetime.date(2009, 2, 20): 1.28285,\n", "  datetime.date(2009, 2, 23): 1.269425,\n", "  datetime.date(2009, 2, 24): 1.28465,\n", "  datetime.date(2009, 2, 25): 1.272275,\n", "  datetime.date(2009, 2, 26): 1.27445,\n", "  datetime.date(2009, 2, 27): 1.26715,\n", "  datetime.date(2009, 3, 2): 1.257775,\n", "  datetime.date(2009, 3, 3): 1.256125,\n", "  datetime.date(2009, 3, 4): 1.26605,\n", "  datetime.date(2009, 3, 5): 1.254,\n", "  datetime.date(2009, 3, 6): 1.265425,\n", "  datetime.date(2009, 3, 9): 1.26115,\n", "  datetime.date(2009, 3, 10): 1.26825,\n", "  datetime.date(2009, 3, 11): 1.283625,\n", "  datetime.date(2009, 3, 12): 1.2914,\n", "  datetime.date(2009, 3, 13): 1.29295,\n", "  datetime.date(2009, 3, 16): 1.296725,\n", "  datetime.date(2009, 3, 17): 1.301775,\n", "  datetime.date(2009, 3, 18): 1.34735,\n", "  datetime.date(2009, 3, 19): 1.3665,\n", "  datetime.date(2009, 3, 20): 1.35815,\n", "  datetime.date(2009, 3, 23): 1.3633,\n", "  datetime.date(2009, 3, 24): 1.34685,\n", "  datetime.date(2009, 3, 25): 1.3582,\n", "  datetime.date(2009, 3, 26): 1.35265,\n", "  datetime.date(2009, 3, 27): 1.3288,\n", "  datetime.date(2009, 3, 30): 1.3199,\n", "  datetime.date(2009, 3, 31): 1.325,\n", "  datetime.date(2009, 4, 1): 1.3249,\n", "  datetime.date(2009, 4, 2): 1.346125,\n", "  datetime.date(2009, 4, 3): 1.34845,\n", "  datetime.date(2009, 4, 6): 1.34165,\n", "  datetime.date(2009, 4, 7): 1.327175,\n", "  datetime.date(2009, 4, 8): 1.32815,\n", "  datetime.date(2009, 4, 9): 1.316875,\n", "  datetime.date(2009, 4, 10): 1.31575,\n", "  datetime.date(2009, 4, 13): 1.33675,\n", "  datetime.date(2009, 4, 14): 1.325925,\n", "  datetime.date(2009, 4, 15): 1.3227,\n", "  datetime.date(2009, 4, 16): 1.3187,\n", "  datetime.date(2009, 4, 17): 1.30445,\n", "  datetime.date(2009, 4, 20): 1.29205,\n", "  datetime.date(2009, 4, 21): 1.29495,\n", "  datetime.date(2009, 4, 22): 1.300525,\n", "  datetime.date(2009, 4, 23): 1.314325,\n", "  datetime.date(2009, 4, 24): 1.324075,\n", "  datetime.date(2009, 4, 27): 1.30375,\n", "  datetime.date(2009, 4, 28): 1.3149,\n", "  datetime.date(2009, 4, 29): 1.3274,\n", "  datetime.date(2009, 4, 30): 1.323,\n", "  datetime.date(2009, 5, 1): 1.3273,\n", "  datetime.date(2009, 5, 4): 1.3406,\n", "  datetime.date(2009, 5, 5): 1.33295,\n", "  datetime.date(2009, 5, 6): 1.33345,\n", "  datetime.date(2009, 5, 7): 1.3391,\n", "  datetime.date(2009, 5, 8): 1.36345,\n", "  datetime.date(2009, 5, 11): 1.35825,\n", "  datetime.date(2009, 5, 12): 1.3648,\n", "  datetime.date(2009, 5, 13): 1.36,\n", "  datetime.date(2009, 5, 14): 1.36395,\n", "  datetime.date(2009, 5, 15): 1.3495,\n", "  datetime.date(2009, 5, 18): 1.3562,\n", "  datetime.date(2009, 5, 19): 1.36295,\n", "  datetime.date(2009, 5, 20): 1.37805,\n", "  datetime.date(2009, 5, 21): 1.38905,\n", "  datetime.date(2009, 5, 22): 1.3998,\n", "  datetime.date(2009, 5, 25): 1.40175,\n", "  datetime.date(2009, 5, 26): 1.3985,\n", "  datetime.date(2009, 5, 27): 1.3826,\n", "  datetime.date(2009, 5, 28): 1.3941,\n", "  datetime.date(2009, 5, 29): 1.41575,\n", "  datetime.date(2009, 6, 1): 1.41595,\n", "  datetime.date(2009, 6, 2): 1.43025,\n", "  datetime.date(2009, 6, 3): 1.4163,\n", "  datetime.date(2009, 6, 4): 1.41845,\n", "  datetime.date(2009, 6, 5): 1.39685,\n", "  datetime.date(2009, 6, 8): 1.39,\n", "  datetime.date(2009, 6, 9): 1.4064,\n", "  datetime.date(2009, 6, 10): 1.39845,\n", "  datetime.date(2009, 6, 11): 1.4108,\n", "  datetime.date(2009, 6, 12): 1.40165,\n", "  datetime.date(2009, 6, 15): 1.38035,\n", "  datetime.date(2009, 6, 16): 1.3837,\n", "  datetime.date(2009, 6, 17): 1.3942,\n", "  datetime.date(2009, 6, 18): 1.39005,\n", "  datetime.date(2009, 6, 19): 1.39365,\n", "  datetime.date(2009, 6, 22): 1.38655,\n", "  datetime.date(2009, 6, 23): 1.4077,\n", "  datetime.date(2009, 6, 24): 1.392925,\n", "  datetime.date(2009, 6, 25): 1.39875,\n", "  datetime.date(2009, 6, 26): 1.40555,\n", "  datetime.date(2009, 6, 29): 1.408225,\n", "  datetime.date(2009, 6, 30): 1.4033,\n", "  datetime.date(2009, 7, 1): 1.41415,\n", "  datetime.date(2009, 7, 2): 1.40035,\n", "  datetime.date(2009, 7, 3): 1.39805,\n", "  datetime.date(2009, 7, 6): 1.3984,\n", "  datetime.date(2009, 7, 7): 1.39245,\n", "  datetime.date(2009, 7, 8): 1.38835,\n", "  datetime.date(2009, 7, 9): 1.40195,\n", "  datetime.date(2009, 7, 10): 1.39355,\n", "  datetime.date(2009, 7, 13): 1.397725,\n", "  datetime.date(2009, 7, 14): 1.39665,\n", "  datetime.date(2009, 7, 15): 1.41065,\n", "  datetime.date(2009, 7, 16): 1.4148,\n", "  datetime.date(2009, 7, 17): 1.41015,\n", "  datetime.date(2009, 7, 20): 1.423175,\n", "  datetime.date(2009, 7, 21): 1.422625,\n", "  datetime.date(2009, 7, 22): 1.422,\n", "  datetime.date(2009, 7, 23): 1.4143,\n", "  datetime.date(2009, 7, 24): 1.42025,\n", "  datetime.date(2009, 7, 27): 1.423075,\n", "  datetime.date(2009, 7, 28): 1.41675,\n", "  datetime.date(2009, 7, 29): 1.404925,\n", "  datetime.date(2009, 7, 30): 1.40755,\n", "  datetime.date(2009, 7, 31): 1.42575,\n", "  datetime.date(2009, 8, 3): 1.441325,\n", "  datetime.date(2009, 8, 4): 1.4408,\n", "  datetime.date(2009, 8, 5): 1.440425,\n", "  datetime.date(2009, 8, 6): 1.43455,\n", "  datetime.date(2009, 8, 7): 1.4183,\n", "  datetime.date(2009, 8, 10): 1.414075,\n", "  datetime.date(2009, 8, 11): 1.41495,\n", "  datetime.date(2009, 8, 12): 1.4187,\n", "  datetime.date(2009, 8, 13): 1.42925,\n", "  datetime.date(2009, 8, 14): 1.420275,\n", "  datetime.date(2009, 8, 17): 1.408225,\n", "  datetime.date(2009, 8, 18): 1.413625,\n", "  datetime.date(2009, 8, 19): 1.42245,\n", "  datetime.date(2009, 8, 20): 1.42535,\n", "  datetime.date(2009, 8, 21): 1.4326,\n", "  datetime.date(2009, 8, 24): 1.43035,\n", "  datetime.date(2009, 8, 25): 1.42975,\n", "  datetime.date(2009, 8, 26): 1.42555,\n", "  datetime.date(2009, 8, 27): 1.434125,\n", "  datetime.date(2009, 8, 28): 1.4303,\n", "  datetime.date(2009, 8, 31): 1.433325,\n", "  datetime.date(2009, 9, 1): 1.42245,\n", "  datetime.date(2009, 9, 2): 1.42635,\n", "  datetime.date(2009, 9, 3): 1.42515,\n", "  datetime.date(2009, 9, 4): 1.429525,\n", "  datetime.date(2009, 9, 7): 1.43325,\n", "  datetime.date(2009, 9, 8): 1.44775,\n", "  datetime.date(2009, 9, 9): 1.4557,\n", "  datetime.date(2009, 9, 10): 1.45815,\n", "  datetime.date(2009, 9, 11): 1.457075,\n", "  datetime.date(2009, 9, 14): 1.4618,\n", "  datetime.date(2009, 9, 15): 1.46575,\n", "  datetime.date(2009, 9, 16): 1.471025,\n", "  datetime.date(2009, 9, 17): 1.47415,\n", "  datetime.date(2009, 9, 18): 1.4711,\n", "  datetime.date(2009, 9, 21): 1.46795,\n", "  datetime.date(2009, 9, 22): 1.47915,\n", "  datetime.date(2009, 9, 23): 1.473475,\n", "  datetime.date(2009, 9, 24): 1.466575,\n", "  datetime.date(2009, 9, 25): 1.468925,\n", "  datetime.date(2009, 9, 28): 1.46225,\n", "  datetime.date(2009, 9, 29): 1.4588,\n", "  datetime.date(2009, 9, 30): 1.4642,\n", "  datetime.date(2009, 10, 1): 1.4545,\n", "  datetime.date(2009, 10, 2): 1.457625,\n", "  datetime.date(2009, 10, 5): 1.4648,\n", "  datetime.date(2009, 10, 6): 1.47225,\n", "  datetime.date(2009, 10, 7): 1.469125,\n", "  datetime.date(2009, 10, 8): 1.47955,\n", "  datetime.date(2009, 10, 9): 1.4732,\n", "  datetime.date(2009, 10, 12): 1.477325,\n", "  datetime.date(2009, 10, 13): 1.485375,\n", "  datetime.date(2009, 10, 14): 1.4926,\n", "  datetime.date(2009, 10, 15): 1.49485,\n", "  datetime.date(2009, 10, 16): 1.490525,\n", "  datetime.date(2009, 10, 19): 1.4964,\n", "  datetime.date(2009, 10, 20): 1.494525,\n", "  datetime.date(2009, 10, 21): 1.501675,\n", "  datetime.date(2009, 10, 22): 1.503275,\n", "  datetime.date(2009, 10, 23): 1.500925,\n", "  datetime.date(2009, 10, 26): 1.48765,\n", "  datetime.date(2009, 10, 27): 1.480425,\n", "  datetime.date(2009, 10, 28): 1.470575,\n", "  datetime.date(2009, 10, 29): 1.482225,\n", "  datetime.date(2009, 10, 30): 1.471875,\n", "  datetime.date(2009, 11, 2): 1.4776,\n", "  datetime.date(2009, 11, 3): 1.472425,\n", "  datetime.date(2009, 11, 4): 1.486125,\n", "  datetime.date(2009, 11, 5): 1.48715,\n", "  datetime.date(2009, 11, 6): 1.484775,\n", "  datetime.date(2009, 11, 9): 1.499875,\n", "  datetime.date(2009, 11, 10): 1.499325,\n", "  datetime.date(2009, 11, 11): 1.498725,\n", "  datetime.date(2009, 11, 12): 1.4851,\n", "  datetime.date(2009, 11, 13): 1.490375,\n", "  datetime.date(2009, 11, 16): 1.49705,\n", "  datetime.date(2009, 11, 17): 1.48765,\n", "  datetime.date(2009, 11, 18): 1.49635,\n", "  datetime.date(2009, 11, 19): 1.492525,\n", "  datetime.date(2009, 11, 20): 1.48615,\n", "  datetime.date(2009, 11, 23): 1.496075,\n", "  datetime.date(2009, 11, 24): 1.49685,\n", "  datetime.date(2009, 11, 25): 1.513475,\n", "  datetime.date(2009, 11, 26): 1.502,\n", "  datetime.date(2009, 11, 27): 1.498625,\n", "  datetime.date(2009, 11, 30): 1.500475,\n", "  datetime.date(2009, 12, 1): 1.50805,\n", "  datetime.date(2009, 12, 2): 1.50445,\n", "  datetime.date(2009, 12, 3): 1.505275,\n", "  datetime.date(2009, 12, 4): 1.48575,\n", "  datetime.date(2009, 12, 7): 1.482625,\n", "  datetime.date(2009, 12, 8): 1.47035,\n", "  datetime.date(2009, 12, 9): 1.472475,\n", "  datetime.date(2009, 12, 10): 1.473075,\n", "  datetime.date(2009, 12, 11): 1.4615,\n", "  datetime.date(2009, 12, 14): 1.465675,\n", "  datetime.date(2009, 12, 15): 1.4538,\n", "  datetime.date(2009, 12, 16): 1.4531,\n", "  datetime.date(2009, 12, 17): 1.433825,\n", "  datetime.date(2009, 12, 18): 1.434375,\n", "  datetime.date(2009, 12, 21): 1.427725,\n", "  datetime.date(2009, 12, 22): 1.42495,\n", "  datetime.date(2009, 12, 23): 1.433675,\n", "  datetime.date(2009, 12, 24): 1.43795,\n", "  datetime.date(2009, 12, 28): 1.4379,\n", "  datetime.date(2009, 12, 29): 1.435325,\n", "  datetime.date(2009, 12, 30): 1.433925,\n", "  datetime.date(2009, 12, 31): 1.432625,\n", "  datetime.date(2010, 1, 4): 1.441275,\n", "  datetime.date(2010, 1, 5): 1.43655,\n", "  datetime.date(2010, 1, 6): 1.44075,\n", "  datetime.date(2010, 1, 7): 1.430775,\n", "  datetime.date(2010, 1, 8): 1.44105,\n", "  datetime.date(2010, 1, 11): 1.4513,\n", "  datetime.date(2010, 1, 12): 1.44855,\n", "  datetime.date(2010, 1, 13): 1.45105,\n", "  datetime.date(2010, 1, 14): 1.449975,\n", "  datetime.date(2010, 1, 15): 1.438575,\n", "  datetime.date(2010, 1, 18): 1.438325,\n", "  datetime.date(2010, 1, 19): 1.4288,\n", "  datetime.date(2010, 1, 20): 1.410575,\n", "  datetime.date(2010, 1, 21): 1.4084,\n", "  datetime.date(2010, 1, 22): 1.413825,\n", "  datetime.date(2010, 1, 25): 1.41505,\n", "  datetime.date(2010, 1, 26): 1.407175,\n", "  datetime.date(2010, 1, 27): 1.402325,\n", "  datetime.date(2010, 1, 28): 1.397075,\n", "  datetime.date(2010, 1, 29): 1.386325,\n", "  datetime.date(2010, 2, 1): 1.39305,\n", "  datetime.date(2010, 2, 2): 1.39645,\n", "  datetime.date(2010, 2, 3): 1.389325,\n", "  datetime.date(2010, 2, 4): 1.372275,\n", "  datetime.date(2010, 2, 5): 1.36785,\n", "  datetime.date(2010, 2, 8): 1.3648,\n", "  datetime.date(2010, 2, 9): 1.3797,\n", "  datetime.date(2010, 2, 10): 1.3737,\n", "  datetime.date(2010, 2, 11): 1.369325,\n", "  datetime.date(2010, 2, 12): 1.36315,\n", "  datetime.date(2010, 2, 15): 1.35985,\n", "  datetime.date(2010, 2, 16): 1.377125,\n", "  datetime.date(2010, 2, 17): 1.3607,\n", "  datetime.date(2010, 2, 18): 1.3527,\n", "  datetime.date(2010, 2, 19): 1.3613,\n", "  datetime.date(2010, 2, 22): 1.359575,\n", "  datetime.date(2010, 2, 23): 1.35075,\n", "  datetime.date(2010, 2, 24): 1.354075,\n", "  datetime.date(2010, 2, 25): 1.35485,\n", "  datetime.date(2010, 2, 26): 1.363275,\n", "  datetime.date(2010, 3, 1): 1.356,\n", "  datetime.date(2010, 3, 2): 1.3615,\n", "  datetime.date(2010, 3, 3): 1.36965,\n", "  datetime.date(2010, 3, 4): 1.358125,\n", "  datetime.date(2010, 3, 5): 1.3624,\n", "  datetime.date(2010, 3, 8): 1.3634,\n", "  datetime.date(2010, 3, 9): 1.360175,\n", "  datetime.date(2010, 3, 10): 1.3657,\n", "  datetime.date(2010, 3, 11): 1.3681,\n", "  datetime.date(2010, 3, 12): 1.3769,\n", "  datetime.date(2010, 3, 15): 1.3677,\n", "  datetime.date(2010, 3, 16): 1.376575,\n", "  datetime.date(2010, 3, 17): 1.37375,\n", "  datetime.date(2010, 3, 18): 1.3608,\n", "  datetime.date(2010, 3, 19): 1.353,\n", "  datetime.date(2010, 3, 22): 1.35585,\n", "  datetime.date(2010, 3, 23): 1.34985,\n", "  datetime.date(2010, 3, 24): 1.331425,\n", "  datetime.date(2010, 3, 25): 1.3272,\n", "  datetime.date(2010, 3, 26): 1.340925,\n", "  datetime.date(2010, 3, 29): 1.34825,\n", "  datetime.date(2010, 3, 30): 1.341375,\n", "  datetime.date(2010, 3, 31): 1.35095,\n", "  datetime.date(2010, 4, 1): 1.358875,\n", "  datetime.date(2010, 4, 2): 1.3503,\n", "  datetime.date(2010, 4, 5): 1.3484,\n", "  datetime.date(2010, 4, 6): 1.339875,\n", "  datetime.date(2010, 4, 7): 1.334375,\n", "  datetime.date(2010, 4, 8): 1.3361,\n", "  datetime.date(2010, 4, 9): 1.3499,\n", "  datetime.date(2010, 4, 12): 1.35915,\n", "  datetime.date(2010, 4, 13): 1.36135,\n", "  datetime.date(2010, 4, 14): 1.365325,\n", "  datetime.date(2010, 4, 15): 1.35725,\n", "  datetime.date(2010, 4, 16): 1.350275,\n", "  datetime.date(2010, 4, 19): 1.348925,\n", "  datetime.date(2010, 4, 20): 1.343425,\n", "  datetime.date(2010, 4, 21): 1.3389,\n", "  datetime.date(2010, 4, 22): 1.32945,\n", "  datetime.date(2010, 4, 23): 1.3384,\n", "  datetime.date(2010, 4, 26): 1.338175,\n", "  datetime.date(2010, 4, 27): 1.3175,\n", "  datetime.date(2010, 4, 28): 1.322,\n", "  datetime.date(2010, 4, 29): 1.32335,\n", "  datetime.date(2010, 4, 30): 1.32945,\n", "  datetime.date(2010, 5, 3): 1.31945,\n", "  datetime.date(2010, 5, 4): 1.2987,\n", "  datetime.date(2010, 5, 5): 1.281425,\n", "  datetime.date(2010, 5, 6): 1.2618,\n", "  datetime.date(2010, 5, 7): 1.275125,\n", "  datetime.date(2010, 5, 10): 1.2787,\n", "  datetime.date(2010, 5, 11): 1.2662,\n", "  datetime.date(2010, 5, 12): 1.26135,\n", "  datetime.date(2010, 5, 13): 1.253475,\n", "  datetime.date(2010, 5, 14): 1.2359,\n", "  datetime.date(2010, 5, 17): 1.239525,\n", "  datetime.date(2010, 5, 18): 1.220225,\n", "  datetime.date(2010, 5, 19): 1.24155,\n", "  datetime.date(2010, 5, 20): 1.248625,\n", "  datetime.date(2010, 5, 21): 1.257075,\n", "  datetime.date(2010, 5, 24): 1.2372,\n", "  datetime.date(2010, 5, 25): 1.2345,\n", "  datetime.date(2010, 5, 26): 1.21785,\n", "  datetime.date(2010, 5, 27): 1.23615,\n", "  datetime.date(2010, 5, 28): 1.2273,\n", "  datetime.date(2010, 5, 31): 1.230575,\n", "  datetime.date(2010, 6, 1): 1.22285,\n", "  datetime.date(2010, 6, 2): 1.2249,\n", "  datetime.date(2010, 6, 3): 1.216175,\n", "  datetime.date(2010, 6, 4): 1.19665,\n", "  datetime.date(2010, 6, 7): 1.19235,\n", "  datetime.date(2010, 6, 8): 1.197325,\n", "  datetime.date(2010, 6, 9): 1.197825,\n", "  datetime.date(2010, 6, 10): 1.212425,\n", "  datetime.date(2010, 6, 11): 1.2112,\n", "  datetime.date(2010, 6, 14): 1.222,\n", "  datetime.date(2010, 6, 15): 1.2332,\n", "  datetime.date(2010, 6, 16): 1.231075,\n", "  datetime.date(2010, 6, 17): 1.2388,\n", "  datetime.date(2010, 6, 18): 1.238775,\n", "  datetime.date(2010, 6, 21): 1.2312,\n", "  datetime.date(2010, 6, 22): 1.227075,\n", "  datetime.date(2010, 6, 23): 1.231,\n", "  datetime.date(2010, 6, 24): 1.23335,\n", "  datetime.date(2010, 6, 25): 1.236825,\n", "  datetime.date(2010, 6, 28): 1.2277,\n", "  datetime.date(2010, 6, 29): 1.218775,\n", "  datetime.date(2010, 6, 30): 1.223725,\n", "  datetime.date(2010, 7, 1): 1.252725,\n", "  datetime.date(2010, 7, 2): 1.25685,\n", "  datetime.date(2010, 7, 5): 1.25385,\n", "  datetime.date(2010, 7, 6): 1.26265,\n", "  datetime.date(2010, 7, 7): 1.2638,\n", "  datetime.date(2010, 7, 8): 1.26975,\n", "  datetime.date(2010, 7, 9): 1.263975,\n", "  datetime.date(2010, 7, 12): 1.259575,\n", "  datetime.date(2010, 7, 13): 1.272425,\n", "  datetime.date(2010, 7, 14): 1.274375,\n", "  datetime.date(2010, 7, 15): 1.295,\n", "  datetime.date(2010, 7, 16): 1.29295,\n", "  datetime.date(2010, 7, 19): 1.294175,\n", "  datetime.date(2010, 7, 20): 1.288,\n", "  datetime.date(2010, 7, 21): 1.2754,\n", "  datetime.date(2010, 7, 22): 1.28925,\n", "  datetime.date(2010, 7, 23): 1.290975,\n", "  datetime.date(2010, 7, 26): 1.299425,\n", "  datetime.date(2010, 7, 27): 1.299575,\n", "  datetime.date(2010, 7, 28): 1.299475,\n", "  datetime.date(2010, 7, 29): 1.3079,\n", "  datetime.date(2010, 7, 30): 1.305075,\n", "  datetime.date(2010, 8, 2): 1.318,\n", "  datetime.date(2010, 8, 3): 1.323025,\n", "  datetime.date(2010, 8, 4): 1.316125,\n", "  datetime.date(2010, 8, 5): 1.31885,\n", "  datetime.date(2010, 8, 6): 1.328025,\n", "  datetime.date(2010, 8, 9): 1.32215,\n", "  datetime.date(2010, 8, 10): 1.3176,\n", "  datetime.date(2010, 8, 11): 1.286275,\n", "  datetime.date(2010, 8, 12): 1.282925,\n", "  datetime.date(2010, 8, 13): 1.27535,\n", "  datetime.date(2010, 8, 16): 1.2826,\n", "  datetime.date(2010, 8, 17): 1.288425,\n", "  datetime.date(2010, 8, 18): 1.2853,\n", "  datetime.date(2010, 8, 19): 1.282325,\n", "  datetime.date(2010, 8, 20): 1.27115,\n", "  datetime.date(2010, 8, 23): 1.2657,\n", "  datetime.date(2010, 8, 24): 1.262725,\n", "  datetime.date(2010, 8, 25): 1.26585,\n", "  datetime.date(2010, 8, 26): 1.2716,\n", "  datetime.date(2010, 8, 27): 1.276275,\n", "  datetime.date(2010, 8, 30): 1.266275,\n", "  datetime.date(2010, 8, 31): 1.267925,\n", "  datetime.date(2010, 9, 1): 1.28085,\n", "  datetime.date(2010, 9, 2): 1.28255,\n", "  datetime.date(2010, 9, 3): 1.28965,\n", "  datetime.date(2010, 9, 6): 1.287575,\n", "  datetime.date(2010, 9, 7): 1.268175,\n", "  datetime.date(2010, 9, 8): 1.27205,\n", "  datetime.date(2010, 9, 9): 1.2695,\n", "  datetime.date(2010, 9, 10): 1.2679,\n", "  datetime.date(2010, 9, 13): 1.2883,\n", "  datetime.date(2010, 9, 14): 1.29975,\n", "  datetime.date(2010, 9, 15): 1.3011,\n", "  datetime.date(2010, 9, 16): 1.3078,\n", "  datetime.date(2010, 9, 17): 1.304925,\n", "  datetime.date(2010, 9, 20): 1.306125,\n", "  datetime.date(2010, 9, 21): 1.3264,\n", "  datetime.date(2010, 9, 22): 1.34055,\n", "  datetime.date(2010, 9, 23): 1.331475,\n", "  datetime.date(2010, 9, 24): 1.3493,\n", "  datetime.date(2010, 9, 27): 1.34545,\n", "  datetime.date(2010, 9, 28): 1.35855,\n", "  datetime.date(2010, 9, 29): 1.36265,\n", "  datetime.date(2010, 9, 30): 1.363475,\n", "  datetime.date(2010, 10, 1): 1.37915,\n", "  datetime.date(2010, 10, 4): 1.368525,\n", "  datetime.date(2010, 10, 5): 1.383925,\n", "  datetime.date(2010, 10, 6): 1.393025,\n", "  datetime.date(2010, 10, 7): 1.392575,\n", "  datetime.date(2010, 10, 8): 1.393975,\n", "  datetime.date(2010, 10, 11): 1.387625,\n", "  datetime.date(2010, 10, 12): 1.392475,\n", "  datetime.date(2010, 10, 13): 1.39615,\n", "  datetime.date(2010, 10, 14): 1.408475,\n", "  datetime.date(2010, 10, 15): 1.397725,\n", "  datetime.date(2010, 10, 18): 1.3934,\n", "  datetime.date(2010, 10, 19): 1.3727,\n", "  datetime.date(2010, 10, 20): 1.396375,\n", "  datetime.date(2010, 10, 21): 1.392,\n", "  datetime.date(2010, 10, 22): 1.39535,\n", "  datetime.date(2010, 10, 25): 1.39655,\n", "  datetime.date(2010, 10, 26): 1.385925,\n", "  datetime.date(2010, 10, 27): 1.37695,\n", "  datetime.date(2010, 10, 28): 1.3931,\n", "  datetime.date(2010, 10, 29): 1.394675,\n", "  datetime.date(2010, 11, 1): 1.389275,\n", "  datetime.date(2010, 11, 2): 1.403325,\n", "  datetime.date(2010, 11, 3): 1.4138,\n", "  datetime.date(2010, 11, 4): 1.420775,\n", "  datetime.date(2010, 11, 5): 1.40325,\n", "  datetime.date(2010, 11, 8): 1.39195,\n", "  datetime.date(2010, 11, 9): 1.37725,\n", "  datetime.date(2010, 11, 10): 1.3783,\n", "  datetime.date(2010, 11, 11): 1.36675,\n", "  datetime.date(2010, 11, 12): 1.36915,\n", "  datetime.date(2010, 11, 15): 1.358675,\n", "  datetime.date(2010, 11, 16): 1.3489,\n", "  datetime.date(2010, 11, 17): 1.35295,\n", "  datetime.date(2010, 11, 18): 1.3643,\n", "  datetime.date(2010, 11, 19): 1.36725,\n", "  datetime.date(2010, 11, 22): 1.3627,\n", "  datetime.date(2010, 11, 23): 1.33665,\n", "  datetime.date(2010, 11, 24): 1.3335,\n", "  datetime.date(2010, 11, 25): 1.33595,\n", "  datetime.date(2010, 11, 26): 1.324125,\n", "  datetime.date(2010, 11, 29): 1.312525,\n", "  datetime.date(2010, 11, 30): 1.298275,\n", "  datetime.date(2010, 12, 1): 1.31385,\n", "  datetime.date(2010, 12, 2): 1.32095,\n", "  datetime.date(2010, 12, 3): 1.3415,\n", "  datetime.date(2010, 12, 6): 1.330775,\n", "  datetime.date(2010, 12, 7): 1.3261,\n", "  datetime.date(2010, 12, 8): 1.326175,\n", "  datetime.date(2010, 12, 9): 1.3239,\n", "  datetime.date(2010, 12, 10): 1.322625,\n", "  datetime.date(2010, 12, 13): 1.3391,\n", "  datetime.date(2010, 12, 14): 1.33785,\n", "  datetime.date(2010, 12, 15): 1.321375,\n", "  datetime.date(2010, 12, 16): 1.3244,\n", "  datetime.date(2010, 12, 17): 1.31885,\n", "  datetime.date(2010, 12, 20): 1.313075,\n", "  datetime.date(2010, 12, 21): 1.310075,\n", "  datetime.date(2010, 12, 22): 1.310025,\n", "  datetime.date(2010, 12, 23): 1.311425,\n", "  datetime.date(2010, 12, 24): 1.312175,\n", "  datetime.date(2010, 12, 27): 1.31645,\n", "  datetime.date(2010, 12, 28): 1.31145,\n", "  datetime.date(2010, 12, 29): 1.3225,\n", "  datetime.date(2010, 12, 30): 1.328975,\n", "  datetime.date(2010, 12, 31): 1.338325,\n", "  datetime.date(2011, 1, 3): 1.336075,\n", "  datetime.date(2011, 1, 4): 1.3307,\n", "  datetime.date(2011, 1, 5): 1.315,\n", "  datetime.date(2011, 1, 6): 1.3003,\n", "  datetime.date(2011, 1, 7): 1.2907,\n", "  datetime.date(2011, 1, 10): 1.2951,\n", "  datetime.date(2011, 1, 11): 1.297425,\n", "  datetime.date(2011, 1, 12): 1.313125,\n", "  datetime.date(2011, 1, 13): 1.33645,\n", "  datetime.date(2011, 1, 14): 1.33885,\n", "  datetime.date(2011, 1, 17): 1.32935,\n", "  datetime.date(2011, 1, 18): 1.3387,\n", "  datetime.date(2011, 1, 19): 1.34735,\n", "  datetime.date(2011, 1, 20): 1.34725,\n", "  datetime.date(2011, 1, 21): 1.36205,\n", "  datetime.date(2011, 1, 24): 1.363775,\n", "  datetime.date(2011, 1, 25): 1.368175,\n", "  datetime.date(2011, 1, 26): 1.371275,\n", "  datetime.date(2011, 1, 27): 1.3734,\n", "  datetime.date(2011, 1, 28): 1.361125,\n", "  datetime.date(2011, 1, 31): 1.36935,\n", "  datetime.date(2011, 2, 1): 1.3829,\n", "  datetime.date(2011, 2, 2): 1.381075,\n", "  datetime.date(2011, 2, 3): 1.36335,\n", "  datetime.date(2011, 2, 4): 1.358275,\n", "  datetime.date(2011, 2, 7): 1.3582,\n", "  datetime.date(2011, 2, 8): 1.36255,\n", "  datetime.date(2011, 2, 9): 1.37325,\n", "  datetime.date(2011, 2, 10): 1.3602,\n", "  datetime.date(2011, 2, 11): 1.35555,\n", "  datetime.date(2011, 2, 14): 1.34885,\n", "  datetime.date(2011, 2, 15): 1.348675,\n", "  datetime.date(2011, 2, 16): 1.35685,\n", "  datetime.date(2011, 2, 17): 1.360925,\n", "  datetime.date(2011, 2, 18): 1.36945,\n", "  datetime.date(2011, 2, 21): 1.367825,\n", "  datetime.date(2011, 2, 22): 1.365,\n", "  datetime.date(2011, 2, 23): 1.37485,\n", "  datetime.date(2011, 2, 24): 1.37995,\n", "  datetime.date(2011, 2, 25): 1.375325,\n", "  datetime.date(2011, 2, 28): 1.38065,\n", "  datetime.date(2011, 3, 1): 1.377675,\n", "  datetime.date(2011, 3, 2): 1.38655,\n", "  datetime.date(2011, 3, 3): 1.39695,\n", "  datetime.date(2011, 3, 4): 1.39865,\n", "  datetime.date(2011, 3, 7): 1.3969,\n", "  datetime.date(2011, 3, 8): 1.39045,\n", "  datetime.date(2011, 3, 9): 1.39085,\n", "  datetime.date(2011, 3, 10): 1.379775,\n", "  datetime.date(2011, 3, 11): 1.390325,\n", "  datetime.date(2011, 3, 14): 1.39925,\n", "  datetime.date(2011, 3, 15): 1.399775,\n", "  datetime.date(2011, 3, 16): 1.390025,\n", "  datetime.date(2011, 3, 17): 1.402125,\n", "  datetime.date(2011, 3, 18): 1.41825,\n", "  datetime.date(2011, 3, 21): 1.422625,\n", "  datetime.date(2011, 3, 22): 1.419625,\n", "  datetime.date(2011, 3, 23): 1.40875,\n", "  datetime.date(2011, 3, 24): 1.417725,\n", "  datetime.date(2011, 3, 25): 1.408825,\n", "  datetime.date(2011, 3, 28): 1.4087,\n", "  datetime.date(2011, 3, 29): 1.41135,\n", "  datetime.date(2011, 3, 30): 1.4128,\n", "  datetime.date(2011, 3, 31): 1.41575,\n", "  datetime.date(2011, 4, 1): 1.423675,\n", "  datetime.date(2011, 4, 4): 1.42205,\n", "  datetime.date(2011, 4, 5): 1.422275,\n", "  datetime.date(2011, 4, 6): 1.433175,\n", "  datetime.date(2011, 4, 7): 1.4308,\n", "  datetime.date(2011, 4, 8): 1.448325,\n", "  datetime.date(2011, 4, 11): 1.44355,\n", "  datetime.date(2011, 4, 12): 1.4477,\n", "  datetime.date(2011, 4, 13): 1.444275,\n", "  datetime.date(2011, 4, 14): 1.448775,\n", "  datetime.date(2011, 4, 15): 1.443025,\n", "  datetime.date(2011, 4, 18): 1.423375,\n", "  datetime.date(2011, 4, 19): 1.43345,\n", "  datetime.date(2011, 4, 20): 1.45225,\n", "  datetime.date(2011, 4, 21): 1.45525,\n", "  datetime.date(2011, 4, 22): 1.456175,\n", "  datetime.date(2011, 4, 25): 1.45805,\n", "  datetime.date(2011, 4, 26): 1.4644,\n", "  datetime.date(2011, 4, 27): 1.478775,\n", "  datetime.date(2011, 4, 28): 1.48225,\n", "  datetime.date(2011, 4, 29): 1.480775,\n", "  datetime.date(2011, 5, 2): 1.483025,\n", "  datetime.date(2011, 5, 3): 1.482575,\n", "  datetime.date(2011, 5, 4): 1.48275,\n", "  datetime.date(2011, 5, 5): 1.453875,\n", "  datetime.date(2011, 5, 6): 1.431575,\n", "  datetime.date(2011, 5, 9): 1.43655,\n", "  datetime.date(2011, 5, 10): 1.44095,\n", "  datetime.date(2011, 5, 11): 1.419225,\n", "  datetime.date(2011, 5, 12): 1.424625,\n", "  datetime.date(2011, 5, 13): 1.41185,\n", "  datetime.date(2011, 5, 16): 1.4156,\n", "  datetime.date(2011, 5, 17): 1.423725,\n", "  datetime.date(2011, 5, 18): 1.424925,\n", "  datetime.date(2011, 5, 19): 1.431025,\n", "  datetime.date(2011, 5, 20): 1.416125,\n", "  datetime.date(2011, 5, 23): 1.40475,\n", "  datetime.date(2011, 5, 24): 1.41,\n", "  datetime.date(2011, 5, 25): 1.408775,\n", "  datetime.date(2011, 5, 26): 1.41445,\n", "  datetime.date(2011, 5, 27): 1.4319,\n", "  datetime.date(2011, 5, 30): 1.42815,\n", "  datetime.date(2011, 5, 31): 1.4396,\n", "  datetime.date(2011, 6, 1): 1.432825,\n", "  datetime.date(2011, 6, 2): 1.449125,\n", "  datetime.date(2011, 6, 3): 1.463475,\n", "  datetime.date(2011, 6, 6): 1.457575,\n", "  datetime.date(2011, 6, 7): 1.469125,\n", "  datetime.date(2011, 6, 8): 1.458325,\n", "  datetime.date(2011, 6, 9): 1.451075,\n", "  datetime.date(2011, 6, 10): 1.434675,\n", "  datetime.date(2011, 6, 13): 1.441325,\n", "  datetime.date(2011, 6, 14): 1.444,\n", "  datetime.date(2011, 6, 15): 1.417925,\n", "  datetime.date(2011, 6, 16): 1.420525,\n", "  datetime.date(2011, 6, 17): 1.430525,\n", "  datetime.date(2011, 6, 20): 1.430425,\n", "  datetime.date(2011, 6, 21): 1.44115,\n", "  datetime.date(2011, 6, 22): 1.43555,\n", "  datetime.date(2011, 6, 23): 1.42565,\n", "  datetime.date(2011, 6, 24): 1.41885,\n", "  datetime.date(2011, 6, 27): 1.4287,\n", "  datetime.date(2011, 6, 28): 1.4371,\n", "  datetime.date(2011, 6, 29): 1.4435,\n", "  datetime.date(2011, 6, 30): 1.4502,\n", "  datetime.date(2011, 7, 1): 1.452675,\n", "  datetime.date(2011, 7, 4): 1.453925,\n", "  datetime.date(2011, 7, 5): 1.4429,\n", "  datetime.date(2011, 7, 6): 1.4319,\n", "  datetime.date(2011, 7, 7): 1.43635,\n", "  datetime.date(2011, 7, 8): 1.4265,\n", "  datetime.date(2011, 7, 11): 1.402825,\n", "  datetime.date(2011, 7, 12): 1.397625,\n", "  datetime.date(2011, 7, 13): 1.41685,\n", "  datetime.date(2011, 7, 14): 1.414275,\n", "  datetime.date(2011, 7, 15): 1.415775,\n", "  datetime.date(2011, 7, 18): 1.41125,\n", "  datetime.date(2011, 7, 19): 1.415525,\n", "  datetime.date(2011, 7, 20): 1.421575,\n", "  datetime.date(2011, 7, 21): 1.442475,\n", "  datetime.date(2011, 7, 22): 1.435875,\n", "  datetime.date(2011, 7, 25): 1.437675,\n", "  datetime.date(2011, 7, 26): 1.45115,\n", "  datetime.date(2011, 7, 27): 1.436875,\n", "  datetime.date(2011, 7, 28): 1.433375,\n", "  datetime.date(2011, 7, 29): 1.439875,\n", "  datetime.date(2011, 8, 1): 1.425,\n", "  datetime.date(2011, 8, 2): 1.420425,\n", "  datetime.date(2011, 8, 3): 1.43235,\n", "  datetime.date(2011, 8, 4): 1.4092,\n", "  datetime.date(2011, 8, 5): 1.4282,\n", "  datetime.date(2011, 8, 8): 1.4178,\n", "  datetime.date(2011, 8, 9): 1.4375,\n", "  datetime.date(2011, 8, 10): 1.417775,\n", "  datetime.date(2011, 8, 11): 1.42405,\n", "  datetime.date(2011, 8, 12): 1.4248,\n", "  datetime.date(2011, 8, 15): 1.444475,\n", "  datetime.date(2011, 8, 16): 1.44075,\n", "  datetime.date(2011, 8, 17): 1.4426,\n", "  datetime.date(2011, 8, 18): 1.43325,\n", "  datetime.date(2011, 8, 19): 1.439675,\n", "  datetime.date(2011, 8, 22): 1.435825,\n", "  datetime.date(2011, 8, 23): 1.444075,\n", "  datetime.date(2011, 8, 24): 1.441425,\n", "  datetime.date(2011, 8, 25): 1.437975,\n", "  datetime.date(2011, 8, 26): 1.4499,\n", "  datetime.date(2011, 8, 29): 1.4511,\n", "  datetime.date(2011, 8, 30): 1.444175,\n", "  datetime.date(2011, 8, 31): 1.43695,\n", "  datetime.date(2011, 9, 1): 1.42595,\n", "  datetime.date(2011, 9, 2): 1.420525,\n", "  datetime.date(2011, 9, 5): 1.409775,\n", "  datetime.date(2011, 9, 6): 1.3998,\n", "  datetime.date(2011, 9, 7): 1.4098,\n", "  datetime.date(2011, 9, 8): 1.388275,\n", "  datetime.date(2011, 9, 9): 1.365675,\n", "  datetime.date(2011, 9, 12): 1.367825,\n", "  datetime.date(2011, 9, 13): 1.367825,\n", "  datetime.date(2011, 9, 14): 1.3755,\n", "  datetime.date(2011, 9, 15): 1.387725,\n", "  datetime.date(2011, 9, 16): 1.37955,\n", "  datetime.date(2011, 9, 19): 1.3685,\n", "  datetime.date(2011, 9, 20): 1.370175,\n", "  datetime.date(2011, 9, 21): 1.35725,\n", "  datetime.date(2011, 9, 22): 1.346425,\n", "  datetime.date(2011, 9, 23): 1.35005,\n", "  datetime.date(2011, 9, 26): 1.353275,\n", "  datetime.date(2011, 9, 27): 1.358325,\n", "  datetime.date(2011, 9, 28): 1.3542,\n", "  datetime.date(2011, 9, 29): 1.3597,\n", "  datetime.date(2011, 9, 30): 1.3388,\n", "  datetime.date(2011, 10, 3): 1.317675,\n", "  datetime.date(2011, 10, 4): 1.334925,\n", "  ...},\n", " 'GBPUSD': {datetime.date(2007, 11, 2): 2.08955,\n", "  datetime.date(2007, 11, 9): 2.090275,\n", "  datetime.date(2007, 11, 29): 2.0613,\n", "  datetime.date(2007, 11, 30): 2.0563,\n", "  datetime.date(2007, 12, 3): 2.065875,\n", "  datetime.date(2007, 12, 4): 2.05915,\n", "  datetime.date(2007, 12, 5): 2.0262,\n", "  datetime.date(2007, 12, 6): 2.027975,\n", "  datetime.date(2007, 12, 7): 2.0304,\n", "  datetime.date(2007, 12, 10): 2.0462,\n", "  datetime.date(2007, 12, 11): 2.0343,\n", "  datetime.date(2007, 12, 12): 2.046875,\n", "  datetime.date(2007, 12, 13): 2.041375,\n", "  datetime.date(2007, 12, 14): 2.0178,\n", "  datetime.date(2007, 12, 17): 2.020575,\n", "  datetime.date(2007, 12, 18): 2.0144,\n", "  datetime.date(2007, 12, 19): 1.9979,\n", "  datetime.date(2007, 12, 20): 1.983875,\n", "  datetime.date(2007, 12, 21): 1.9836,\n", "  datetime.date(2007, 12, 24): 1.9764,\n", "  datetime.date(2007, 12, 26): 1.98385,\n", "  datetime.date(2007, 12, 27): 1.99605,\n", "  datetime.date(2007, 12, 28): 1.9969,\n", "  datetime.date(2007, 12, 31): 1.9849,\n", "  datetime.date(2008, 1, 2): 1.980875,\n", "  datetime.date(2008, 1, 3): 1.97105,\n", "  datetime.date(2008, 1, 4): 1.97405,\n", "  datetime.date(2008, 1, 7): 1.969975,\n", "  datetime.date(2008, 1, 8): 1.97315,\n", "  datetime.date(2008, 1, 9): 1.958575,\n", "  datetime.date(2008, 1, 10): 1.9615,\n", "  datetime.date(2008, 1, 11): 1.9566,\n", "  datetime.date(2008, 1, 14): 1.95595,\n", "  datetime.date(2008, 1, 15): 1.962725,\n", "  datetime.date(2008, 1, 16): 1.9635,\n", "  datetime.date(2008, 1, 17): 1.9715,\n", "  datetime.date(2008, 1, 18): 1.9555,\n", "  datetime.date(2008, 1, 21): 1.94335,\n", "  datetime.date(2008, 1, 22): 1.960725,\n", "  datetime.date(2008, 1, 23): 1.95535,\n", "  datetime.date(2008, 1, 24): 1.97655,\n", "  datetime.date(2008, 1, 25): 1.9832,\n", "  datetime.date(2008, 1, 28): 1.984225,\n", "  datetime.date(2008, 1, 29): 1.98945,\n", "  datetime.date(2008, 1, 30): 1.98645,\n", "  datetime.date(2008, 1, 31): 1.98725,\n", "  datetime.date(2008, 2, 1): 1.965125,\n", "  datetime.date(2008, 2, 4): 1.97345,\n", "  datetime.date(2008, 2, 5): 1.964875,\n", "  datetime.date(2008, 2, 6): 1.961575,\n", "  datetime.date(2008, 2, 7): 1.94235,\n", "  datetime.date(2008, 2, 8): 1.9459,\n", "  datetime.date(2008, 2, 11): 1.9509,\n", "  datetime.date(2008, 2, 12): 1.95985,\n", "  datetime.date(2008, 2, 13): 1.96295,\n", "  datetime.date(2008, 2, 14): 1.96905,\n", "  datetime.date(2008, 2, 15): 1.961275,\n", "  datetime.date(2008, 2, 18): 1.95255,\n", "  datetime.date(2008, 2, 19): 1.9485,\n", "  datetime.date(2008, 2, 20): 1.9419,\n", "  datetime.date(2008, 2, 21): 1.9628,\n", "  datetime.date(2008, 2, 22): 1.96685,\n", "  datetime.date(2008, 2, 25): 1.967225,\n", "  datetime.date(2008, 2, 26): 1.987225,\n", "  datetime.date(2008, 2, 27): 1.9818,\n", "  datetime.date(2008, 2, 28): 1.9888,\n", "  datetime.date(2008, 2, 29): 1.989075,\n", "  datetime.date(2008, 3, 3): 1.98375,\n", "  datetime.date(2008, 3, 4): 1.98635,\n", "  datetime.date(2008, 3, 5): 1.992,\n", "  datetime.date(2008, 3, 6): 2.00925,\n", "  datetime.date(2008, 3, 7): 2.0136,\n", "  datetime.date(2008, 3, 10): 2.009275,\n", "  datetime.date(2008, 3, 11): 2.0065,\n", "  datetime.date(2008, 3, 12): 2.026975,\n", "  datetime.date(2008, 3, 13): 2.033575,\n", "  datetime.date(2008, 3, 14): 2.01945,\n", "  datetime.date(2008, 3, 17): 1.998925,\n", "  datetime.date(2008, 3, 18): 2.006525,\n", "  datetime.date(2008, 3, 19): 1.98425,\n", "  datetime.date(2008, 3, 20): 1.98505,\n", "  datetime.date(2008, 3, 21): 1.98135,\n", "  datetime.date(2008, 3, 24): 1.9857,\n", "  datetime.date(2008, 3, 25): 2.006025,\n", "  datetime.date(2008, 3, 26): 2.0088,\n", "  datetime.date(2008, 3, 27): 2.0073,\n", "  datetime.date(2008, 3, 28): 1.9945,\n", "  datetime.date(2008, 3, 31): 1.98375,\n", "  datetime.date(2008, 4, 1): 1.97585,\n", "  datetime.date(2008, 4, 2): 1.9874,\n", "  datetime.date(2008, 4, 3): 1.99675,\n", "  datetime.date(2008, 4, 4): 1.993325,\n", "  datetime.date(2008, 4, 7): 1.988275,\n", "  datetime.date(2008, 4, 8): 1.9697,\n", "  datetime.date(2008, 4, 9): 1.976075,\n", "  datetime.date(2008, 4, 10): 1.97075,\n", "  datetime.date(2008, 4, 11): 1.969075,\n", "  datetime.date(2008, 4, 14): 1.978425,\n", "  datetime.date(2008, 4, 15): 1.9625,\n", "  datetime.date(2008, 4, 16): 1.972275,\n", "  datetime.date(2008, 4, 17): 1.991275,\n", "  datetime.date(2008, 4, 18): 1.9979,\n", "  datetime.date(2008, 4, 21): 1.980325,\n", "  datetime.date(2008, 4, 22): 1.9952,\n", "  datetime.date(2008, 4, 23): 1.980425,\n", "  datetime.date(2008, 4, 24): 1.973975,\n", "  datetime.date(2008, 4, 25): 1.985825,\n", "  datetime.date(2008, 4, 28): 1.99145,\n", "  datetime.date(2008, 4, 29): 1.969775,\n", "  datetime.date(2008, 4, 30): 1.9867,\n", "  datetime.date(2008, 5, 1): 1.9748,\n", "  datetime.date(2008, 5, 2): 1.97175,\n", "  datetime.date(2008, 5, 5): 1.97205,\n", "  datetime.date(2008, 5, 6): 1.9738,\n", "  datetime.date(2008, 5, 7): 1.953825,\n", "  datetime.date(2008, 5, 8): 1.95415,\n", "  datetime.date(2008, 5, 9): 1.9539,\n", "  datetime.date(2008, 5, 12): 1.9579,\n", "  datetime.date(2008, 5, 13): 1.94555,\n", "  datetime.date(2008, 5, 14): 1.9459,\n", "  datetime.date(2008, 5, 15): 1.947425,\n", "  datetime.date(2008, 5, 16): 1.95695,\n", "  datetime.date(2008, 5, 19): 1.948875,\n", "  datetime.date(2008, 5, 20): 1.96835,\n", "  datetime.date(2008, 5, 21): 1.973325,\n", "  datetime.date(2008, 5, 22): 1.980375,\n", "  datetime.date(2008, 5, 23): 1.9798,\n", "  datetime.date(2008, 5, 26): 1.9821,\n", "  datetime.date(2008, 5, 27): 1.9764,\n", "  datetime.date(2008, 5, 28): 1.981375,\n", "  datetime.date(2008, 5, 29): 1.97665,\n", "  datetime.date(2008, 5, 30): 1.982275,\n", "  datetime.date(2008, 6, 2): 1.967125,\n", "  datetime.date(2008, 6, 3): 1.963475,\n", "  datetime.date(2008, 6, 4): 1.955675,\n", "  datetime.date(2008, 6, 5): 1.958425,\n", "  datetime.date(2008, 6, 6): 1.9708,\n", "  datetime.date(2008, 6, 9): 1.97515,\n", "  datetime.date(2008, 6, 10): 1.95445,\n", "  datetime.date(2008, 6, 11): 1.9633,\n", "  datetime.date(2008, 6, 12): 1.94635,\n", "  datetime.date(2008, 6, 13): 1.94765,\n", "  datetime.date(2008, 6, 16): 1.96325,\n", "  datetime.date(2008, 6, 17): 1.95685,\n", "  datetime.date(2008, 6, 18): 1.9599,\n", "  datetime.date(2008, 6, 19): 1.97225,\n", "  datetime.date(2008, 6, 20): 1.976075,\n", "  datetime.date(2008, 6, 23): 1.96545,\n", "  datetime.date(2008, 6, 24): 1.97085,\n", "  datetime.date(2008, 6, 25): 1.975175,\n", "  datetime.date(2008, 6, 26): 1.989,\n", "  datetime.date(2008, 6, 27): 1.995,\n", "  datetime.date(2008, 6, 30): 1.99235,\n", "  datetime.date(2008, 7, 1): 1.99495,\n", "  datetime.date(2008, 7, 2): 1.993075,\n", "  datetime.date(2008, 7, 3): 1.98335,\n", "  datetime.date(2008, 7, 4): 1.98225,\n", "  datetime.date(2008, 7, 7): 1.976275,\n", "  datetime.date(2008, 7, 8): 1.96955,\n", "  datetime.date(2008, 7, 9): 1.983175,\n", "  datetime.date(2008, 7, 10): 1.977975,\n", "  datetime.date(2008, 7, 11): 1.9886,\n", "  datetime.date(2008, 7, 14): 1.995125,\n", "  datetime.date(2008, 7, 15): 2.00585,\n", "  datetime.date(2008, 7, 16): 1.999125,\n", "  datetime.date(2008, 7, 17): 2.003875,\n", "  datetime.date(2008, 7, 18): 1.99885,\n", "  datetime.date(2008, 7, 21): 2.003625,\n", "  datetime.date(2008, 7, 22): 1.991725,\n", "  datetime.date(2008, 7, 23): 1.9995,\n", "  datetime.date(2008, 7, 24): 1.9868,\n", "  datetime.date(2008, 7, 25): 1.99165,\n", "  datetime.date(2008, 7, 28): 1.9939,\n", "  datetime.date(2008, 7, 29): 1.97885,\n", "  datetime.date(2008, 7, 30): 1.98165,\n", "  datetime.date(2008, 7, 31): 1.98415,\n", "  datetime.date(2008, 8, 1): 1.975325,\n", "  datetime.date(2008, 8, 4): 1.96215,\n", "  datetime.date(2008, 8, 5): 1.954825,\n", "  datetime.date(2008, 8, 6): 1.9478,\n", "  datetime.date(2008, 8, 7): 1.943975,\n", "  datetime.date(2008, 8, 8): 1.92125,\n", "  datetime.date(2008, 8, 11): 1.911,\n", "  datetime.date(2008, 8, 12): 1.89685,\n", "  datetime.date(2008, 8, 13): 1.8704,\n", "  datetime.date(2008, 8, 14): 1.8699,\n", "  datetime.date(2008, 8, 15): 1.86615,\n", "  datetime.date(2008, 8, 18): 1.865175,\n", "  datetime.date(2008, 8, 19): 1.867075,\n", "  datetime.date(2008, 8, 20): 1.862075,\n", "  datetime.date(2008, 8, 21): 1.8783,\n", "  datetime.date(2008, 8, 22): 1.8526,\n", "  datetime.date(2008, 8, 25): 1.8532,\n", "  datetime.date(2008, 8, 26): 1.83995,\n", "  datetime.date(2008, 8, 27): 1.8356,\n", "  datetime.date(2008, 8, 28): 1.829475,\n", "  datetime.date(2008, 8, 29): 1.82125,\n", "  datetime.date(2008, 9, 1): 1.801475,\n", "  datetime.date(2008, 9, 2): 1.78385,\n", "  datetime.date(2008, 9, 3): 1.776825,\n", "  datetime.date(2008, 9, 4): 1.7684,\n", "  datetime.date(2008, 9, 5): 1.76615,\n", "  datetime.date(2008, 9, 8): 1.7581,\n", "  datetime.date(2008, 9, 9): 1.7607,\n", "  datetime.date(2008, 9, 10): 1.753,\n", "  datetime.date(2008, 9, 11): 1.757975,\n", "  datetime.date(2008, 9, 12): 1.7936,\n", "  datetime.date(2008, 9, 15): 1.7997,\n", "  datetime.date(2008, 9, 16): 1.7835,\n", "  datetime.date(2008, 9, 17): 1.8173,\n", "  datetime.date(2008, 9, 18): 1.81805,\n", "  datetime.date(2008, 9, 19): 1.83175,\n", "  datetime.date(2008, 9, 22): 1.8543,\n", "  datetime.date(2008, 9, 23): 1.8522,\n", "  datetime.date(2008, 9, 24): 1.8466,\n", "  datetime.date(2008, 9, 25): 1.83735,\n", "  datetime.date(2008, 9, 26): 1.84495,\n", "  datetime.date(2008, 9, 29): 1.8088,\n", "  datetime.date(2008, 9, 30): 1.78055,\n", "  datetime.date(2008, 10, 1): 1.76985,\n", "  datetime.date(2008, 10, 2): 1.763975,\n", "  datetime.date(2008, 10, 3): 1.771675,\n", "  datetime.date(2008, 10, 6): 1.743875,\n", "  datetime.date(2008, 10, 7): 1.745425,\n", "  datetime.date(2008, 10, 8): 1.730525,\n", "  datetime.date(2008, 10, 9): 1.709625,\n", "  datetime.date(2008, 10, 10): 1.71015,\n", "  datetime.date(2008, 10, 13): 1.734175,\n", "  datetime.date(2008, 10, 14): 1.7395,\n", "  datetime.date(2008, 10, 15): 1.726875,\n", "  datetime.date(2008, 10, 16): 1.7304,\n", "  datetime.date(2008, 10, 17): 1.7281,\n", "  datetime.date(2008, 10, 20): 1.71535,\n", "  datetime.date(2008, 10, 21): 1.670575,\n", "  datetime.date(2008, 10, 22): 1.626275,\n", "  datetime.date(2008, 10, 23): 1.623,\n", "  datetime.date(2008, 10, 24): 1.58945,\n", "  datetime.date(2008, 10, 27): 1.55515,\n", "  datetime.date(2008, 10, 28): 1.5899,\n", "  datetime.date(2008, 10, 29): 1.6373,\n", "  datetime.date(2008, 10, 30): 1.645075,\n", "  datetime.date(2008, 10, 31): 1.608,\n", "  datetime.date(2008, 11, 3): 1.582275,\n", "  datetime.date(2008, 11, 4): 1.5957,\n", "  datetime.date(2008, 11, 5): 1.590525,\n", "  datetime.date(2008, 11, 6): 1.562675,\n", "  datetime.date(2008, 11, 7): 1.5647,\n", "  datetime.date(2008, 11, 10): 1.560975,\n", "  datetime.date(2008, 11, 11): 1.538425,\n", "  datetime.date(2008, 11, 12): 1.49675,\n", "  datetime.date(2008, 11, 13): 1.4841,\n", "  datetime.date(2008, 11, 14): 1.473925,\n", "  datetime.date(2008, 11, 17): 1.498975,\n", "  datetime.date(2008, 11, 18): 1.4958,\n", "  datetime.date(2008, 11, 19): 1.495125,\n", "  datetime.date(2008, 11, 20): 1.47275,\n", "  datetime.date(2008, 11, 21): 1.492375,\n", "  datetime.date(2008, 11, 24): 1.51835,\n", "  datetime.date(2008, 11, 25): 1.547375,\n", "  datetime.date(2008, 11, 26): 1.532575,\n", "  datetime.date(2008, 11, 27): 1.54035,\n", "  datetime.date(2008, 11, 28): 1.537425,\n", "  datetime.date(2008, 12, 1): 1.48855,\n", "  datetime.date(2008, 12, 2): 1.492,\n", "  datetime.date(2008, 12, 3): 1.478325,\n", "  datetime.date(2008, 12, 4): 1.4681,\n", "  datetime.date(2008, 12, 5): 1.4688,\n", "  datetime.date(2008, 12, 8): 1.491325,\n", "  datetime.date(2008, 12, 9): 1.47505,\n", "  datetime.date(2008, 12, 10): 1.4786,\n", "  datetime.date(2008, 12, 11): 1.503275,\n", "  datetime.date(2008, 12, 12): 1.494775,\n", "  datetime.date(2008, 12, 15): 1.53065,\n", "  datetime.date(2008, 12, 16): 1.558175,\n", "  datetime.date(2008, 12, 17): 1.553575,\n", "  datetime.date(2008, 12, 18): 1.501525,\n", "  datetime.date(2008, 12, 19): 1.49255,\n", "  datetime.date(2008, 12, 22): 1.48235,\n", "  datetime.date(2008, 12, 23): 1.472975,\n", "  datetime.date(2008, 12, 24): 1.4769,\n", "  datetime.date(2008, 12, 26): 1.4603,\n", "  datetime.date(2008, 12, 29): 1.439375,\n", "  datetime.date(2008, 12, 30): 1.4409,\n", "  datetime.date(2008, 12, 31): 1.4605,\n", "  datetime.date(2009, 1, 2): 1.454825,\n", "  datetime.date(2009, 1, 5): 1.470025,\n", "  datetime.date(2009, 1, 6): 1.491975,\n", "  datetime.date(2009, 1, 7): 1.5098,\n", "  datetime.date(2009, 1, 8): 1.52165,\n", "  datetime.date(2009, 1, 9): 1.5165,\n", "  datetime.date(2009, 1, 12): 1.4822,\n", "  datetime.date(2009, 1, 13): 1.450225,\n", "  datetime.date(2009, 1, 14): 1.46115,\n", "  datetime.date(2009, 1, 15): 1.46405,\n", "  datetime.date(2009, 1, 16): 1.47325,\n", "  datetime.date(2009, 1, 19): 1.4423,\n", "  datetime.date(2009, 1, 20): 1.39265,\n", "  datetime.date(2009, 1, 21): 1.395675,\n", "  datetime.date(2009, 1, 22): 1.38775,\n", "  datetime.date(2009, 1, 23): 1.38005,\n", "  datetime.date(2009, 1, 26): 1.3994,\n", "  datetime.date(2009, 1, 27): 1.413275,\n", "  datetime.date(2009, 1, 28): 1.42495,\n", "  datetime.date(2009, 1, 29): 1.4301,\n", "  datetime.date(2009, 1, 30): 1.454025,\n", "  datetime.date(2009, 2, 2): 1.4264,\n", "  datetime.date(2009, 2, 3): 1.44575,\n", "  datetime.date(2009, 2, 4): 1.44675,\n", "  datetime.date(2009, 2, 5): 1.461225,\n", "  datetime.date(2009, 2, 6): 1.478725,\n", "  datetime.date(2009, 2, 9): 1.489875,\n", "  datetime.date(2009, 2, 10): 1.45415,\n", "  datetime.date(2009, 2, 11): 1.4397,\n", "  datetime.date(2009, 2, 12): 1.426825,\n", "  datetime.date(2009, 2, 13): 1.435275,\n", "  datetime.date(2009, 2, 16): 1.42975,\n", "  datetime.date(2009, 2, 17): 1.423825,\n", "  datetime.date(2009, 2, 18): 1.42105,\n", "  datetime.date(2009, 2, 19): 1.42935,\n", "  datetime.date(2009, 2, 20): 1.4433,\n", "  datetime.date(2009, 2, 23): 1.4487,\n", "  datetime.date(2009, 2, 24): 1.44805,\n", "  datetime.date(2009, 2, 25): 1.419975,\n", "  datetime.date(2009, 2, 26): 1.43175,\n", "  datetime.date(2009, 2, 27): 1.431775,\n", "  datetime.date(2009, 3, 2): 1.40555,\n", "  datetime.date(2009, 3, 3): 1.405,\n", "  datetime.date(2009, 3, 4): 1.419325,\n", "  datetime.date(2009, 3, 5): 1.411725,\n", "  datetime.date(2009, 3, 6): 1.40935,\n", "  datetime.date(2009, 3, 9): 1.37765,\n", "  datetime.date(2009, 3, 10): 1.375225,\n", "  datetime.date(2009, 3, 11): 1.3874,\n", "  datetime.date(2009, 3, 12): 1.394025,\n", "  datetime.date(2009, 3, 13): 1.4002,\n", "  datetime.date(2009, 3, 16): 1.40635,\n", "  datetime.date(2009, 3, 17): 1.403725,\n", "  datetime.date(2009, 3, 18): 1.42725,\n", "  datetime.date(2009, 3, 19): 1.450625,\n", "  datetime.date(2009, 3, 20): 1.446425,\n", "  datetime.date(2009, 3, 23): 1.45735,\n", "  datetime.date(2009, 3, 24): 1.467975,\n", "  datetime.date(2009, 3, 25): 1.455225,\n", "  datetime.date(2009, 3, 26): 1.445275,\n", "  datetime.date(2009, 3, 27): 1.43195,\n", "  datetime.date(2009, 3, 30): 1.426575,\n", "  datetime.date(2009, 3, 31): 1.43235,\n", "  datetime.date(2009, 4, 1): 1.4468,\n", "  datetime.date(2009, 4, 2): 1.4724,\n", "  datetime.date(2009, 4, 3): 1.484125,\n", "  datetime.date(2009, 4, 6): 1.4769,\n", "  datetime.date(2009, 4, 7): 1.473125,\n", "  datetime.date(2009, 4, 8): 1.47165,\n", "  datetime.date(2009, 4, 9): 1.467925,\n", "  datetime.date(2009, 4, 10): 1.464575,\n", "  datetime.date(2009, 4, 13): 1.485575,\n", "  datetime.date(2009, 4, 14): 1.48955,\n", "  datetime.date(2009, 4, 15): 1.499825,\n", "  datetime.date(2009, 4, 16): 1.4925,\n", "  datetime.date(2009, 4, 17): 1.47975,\n", "  datetime.date(2009, 4, 20): 1.45385,\n", "  datetime.date(2009, 4, 21): 1.46745,\n", "  datetime.date(2009, 4, 22): 1.44915,\n", "  datetime.date(2009, 4, 23): 1.4722,\n", "  datetime.date(2009, 4, 24): 1.4679,\n", "  datetime.date(2009, 4, 27): 1.4643,\n", "  datetime.date(2009, 4, 28): 1.463375,\n", "  datetime.date(2009, 4, 29): 1.4772,\n", "  datetime.date(2009, 4, 30): 1.4791,\n", "  datetime.date(2009, 5, 1): 1.492225,\n", "  datetime.date(2009, 5, 4): 1.5018,\n", "  datetime.date(2009, 5, 5): 1.508725,\n", "  datetime.date(2009, 5, 6): 1.51365,\n", "  datetime.date(2009, 5, 7): 1.501875,\n", "  datetime.date(2009, 5, 8): 1.52335,\n", "  datetime.date(2009, 5, 11): 1.511775,\n", "  datetime.date(2009, 5, 12): 1.527075,\n", "  datetime.date(2009, 5, 13): 1.5157,\n", "  datetime.date(2009, 5, 14): 1.5237,\n", "  datetime.date(2009, 5, 15): 1.5178,\n", "  datetime.date(2009, 5, 18): 1.5348,\n", "  datetime.date(2009, 5, 19): 1.54765,\n", "  datetime.date(2009, 5, 20): 1.575275,\n", "  datetime.date(2009, 5, 21): 1.584025,\n", "  datetime.date(2009, 5, 22): 1.5933,\n", "  datetime.date(2009, 5, 25): 1.59085,\n", "  datetime.date(2009, 5, 26): 1.59245,\n", "  datetime.date(2009, 5, 27): 1.59535,\n", "  datetime.date(2009, 5, 28): 1.59435,\n", "  datetime.date(2009, 5, 29): 1.6188,\n", "  datetime.date(2009, 6, 1): 1.64445,\n", "  datetime.date(2009, 6, 2): 1.6578,\n", "  datetime.date(2009, 6, 3): 1.631725,\n", "  datetime.date(2009, 6, 4): 1.6175,\n", "  datetime.date(2009, 6, 5): 1.5981,\n", "  datetime.date(2009, 6, 8): 1.6051,\n", "  datetime.date(2009, 6, 9): 1.63045,\n", "  datetime.date(2009, 6, 10): 1.63585,\n", "  datetime.date(2009, 6, 11): 1.6591,\n", "  datetime.date(2009, 6, 12): 1.64445,\n", "  datetime.date(2009, 6, 15): 1.6319,\n", "  datetime.date(2009, 6, 16): 1.641225,\n", "  datetime.date(2009, 6, 17): 1.63995,\n", "  datetime.date(2009, 6, 18): 1.633175,\n", "  datetime.date(2009, 6, 19): 1.64945,\n", "  datetime.date(2009, 6, 22): 1.63475,\n", "  datetime.date(2009, 6, 23): 1.6454,\n", "  datetime.date(2009, 6, 24): 1.640775,\n", "  datetime.date(2009, 6, 25): 1.6368,\n", "  datetime.date(2009, 6, 26): 1.6525,\n", "  datetime.date(2009, 6, 29): 1.65665,\n", "  datetime.date(2009, 6, 30): 1.6459,\n", "  datetime.date(2009, 7, 1): 1.6479,\n", "  datetime.date(2009, 7, 2): 1.6394,\n", "  datetime.date(2009, 7, 3): 1.6333,\n", "  datetime.date(2009, 7, 6): 1.6287,\n", "  datetime.date(2009, 7, 7): 1.61385,\n", "  datetime.date(2009, 7, 8): 1.60725,\n", "  datetime.date(2009, 7, 9): 1.633525,\n", "  datetime.date(2009, 7, 10): 1.62115,\n", "  datetime.date(2009, 7, 13): 1.622725,\n", "  datetime.date(2009, 7, 14): 1.63085,\n", "  datetime.date(2009, 7, 15): 1.642525,\n", "  datetime.date(2009, 7, 16): 1.6439,\n", "  datetime.date(2009, 7, 17): 1.633525,\n", "  datetime.date(2009, 7, 20): 1.654675,\n", "  datetime.date(2009, 7, 21): 1.645975,\n", "  datetime.date(2009, 7, 22): 1.649325,\n", "  datetime.date(2009, 7, 23): 1.6478,\n", "  datetime.date(2009, 7, 24): 1.642625,\n", "  datetime.date(2009, 7, 27): 1.648625,\n", "  datetime.date(2009, 7, 28): 1.642825,\n", "  datetime.date(2009, 7, 29): 1.63795,\n", "  datetime.date(2009, 7, 30): 1.64945,\n", "  datetime.date(2009, 7, 31): 1.671425,\n", "  datetime.date(2009, 8, 3): 1.6928,\n", "  datetime.date(2009, 8, 4): 1.693875,\n", "  datetime.date(2009, 8, 5): 1.69895,\n", "  datetime.date(2009, 8, 6): 1.678325,\n", "  datetime.date(2009, 8, 7): 1.6684,\n", "  datetime.date(2009, 8, 10): 1.648175,\n", "  datetime.date(2009, 8, 11): 1.64795,\n", "  datetime.date(2009, 8, 12): 1.648,\n", "  datetime.date(2009, 8, 13): 1.65825,\n", "  datetime.date(2009, 8, 14): 1.6543,\n", "  datetime.date(2009, 8, 17): 1.6348,\n", "  datetime.date(2009, 8, 18): 1.6559,\n", "  datetime.date(2009, 8, 19): 1.6529,\n", "  datetime.date(2009, 8, 20): 1.650675,\n", "  datetime.date(2009, 8, 21): 1.6507,\n", "  datetime.date(2009, 8, 24): 1.641675,\n", "  datetime.date(2009, 8, 25): 1.6351,\n", "  datetime.date(2009, 8, 26): 1.625,\n", "  datetime.date(2009, 8, 27): 1.62825,\n", "  datetime.date(2009, 8, 28): 1.62695,\n", "  datetime.date(2009, 8, 31): 1.62875,\n", "  datetime.date(2009, 9, 1): 1.61585,\n", "  datetime.date(2009, 9, 2): 1.6273,\n", "  datetime.date(2009, 9, 3): 1.631925,\n", "  datetime.date(2009, 9, 4): 1.639225,\n", "  datetime.date(2009, 9, 7): 1.63495,\n", "  datetime.date(2009, 9, 8): 1.64885,\n", "  datetime.date(2009, 9, 9): 1.654675,\n", "  datetime.date(2009, 9, 10): 1.665225,\n", "  datetime.date(2009, 9, 11): 1.66565,\n", "  datetime.date(2009, 9, 14): 1.65835,\n", "  datetime.date(2009, 9, 15): 1.648875,\n", "  datetime.date(2009, 9, 16): 1.64945,\n", "  datetime.date(2009, 9, 17): 1.6454,\n", "  datetime.date(2009, 9, 18): 1.627125,\n", "  datetime.date(2009, 9, 21): 1.62175,\n", "  datetime.date(2009, 9, 22): 1.63595,\n", "  datetime.date(2009, 9, 23): 1.63415,\n", "  datetime.date(2009, 9, 24): 1.606375,\n", "  datetime.date(2009, 9, 25): 1.595125,\n", "  datetime.date(2009, 9, 28): 1.588175,\n", "  datetime.date(2009, 9, 29): 1.5962,\n", "  datetime.date(2009, 9, 30): 1.59815,\n", "  datetime.date(2009, 10, 1): 1.5956,\n", "  datetime.date(2009, 10, 2): 1.5945,\n", "  datetime.date(2009, 10, 5): 1.59345,\n", "  datetime.date(2009, 10, 6): 1.5922,\n", "  datetime.date(2009, 10, 7): 1.596975,\n", "  datetime.date(2009, 10, 8): 1.607125,\n", "  datetime.date(2009, 10, 9): 1.58455,\n", "  datetime.date(2009, 10, 12): 1.5799,\n", "  datetime.date(2009, 10, 13): 1.592425,\n", "  datetime.date(2009, 10, 14): 1.5981,\n", "  datetime.date(2009, 10, 15): 1.627075,\n", "  datetime.date(2009, 10, 16): 1.63535,\n", "  datetime.date(2009, 10, 19): 1.642225,\n", "  datetime.date(2009, 10, 20): 1.638075,\n", "  datetime.date(2009, 10, 21): 1.660775,\n", "  datetime.date(2009, 10, 22): 1.66205,\n", "  datetime.date(2009, 10, 23): 1.630575,\n", "  datetime.date(2009, 10, 26): 1.6336,\n", "  datetime.date(2009, 10, 27): 1.6368,\n", "  datetime.date(2009, 10, 28): 1.637375,\n", "  datetime.date(2009, 10, 29): 1.654475,\n", "  datetime.date(2009, 10, 30): 1.64535,\n", "  datetime.date(2009, 11, 2): 1.64085,\n", "  datetime.date(2009, 11, 3): 1.643825,\n", "  datetime.date(2009, 11, 4): 1.6553,\n", "  datetime.date(2009, 11, 5): 1.65825,\n", "  datetime.date(2009, 11, 6): 1.661275,\n", "  datetime.date(2009, 11, 9): 1.675825,\n", "  datetime.date(2009, 11, 10): 1.67445,\n", "  datetime.date(2009, 11, 11): 1.65745,\n", "  datetime.date(2009, 11, 12): 1.657675,\n", "  datetime.date(2009, 11, 13): 1.66735,\n", "  datetime.date(2009, 11, 16): 1.6821,\n", "  datetime.date(2009, 11, 17): 1.6811,\n", "  datetime.date(2009, 11, 18): 1.67485,\n", "  datetime.date(2009, 11, 19): 1.66675,\n", "  datetime.date(2009, 11, 20): 1.650475,\n", "  datetime.date(2009, 11, 23): 1.660525,\n", "  datetime.date(2009, 11, 24): 1.658775,\n", "  datetime.date(2009, 11, 25): 1.670575,\n", "  datetime.date(2009, 11, 26): 1.653,\n", "  datetime.date(2009, 11, 27): 1.650075,\n", "  datetime.date(2009, 11, 30): 1.644,\n", "  datetime.date(2009, 12, 1): 1.66095,\n", "  datetime.date(2009, 12, 2): 1.66285,\n", "  datetime.date(2009, 12, 3): 1.65395,\n", "  datetime.date(2009, 12, 4): 1.6475,\n", "  datetime.date(2009, 12, 7): 1.644675,\n", "  datetime.date(2009, 12, 8): 1.6287,\n", "  datetime.date(2009, 12, 9): 1.626225,\n", "  datetime.date(2009, 12, 10): 1.6279,\n", "  datetime.date(2009, 12, 11): 1.626175,\n", "  datetime.date(2009, 12, 14): 1.63115,\n", "  datetime.date(2009, 12, 15): 1.6267,\n", "  datetime.date(2009, 12, 16): 1.63345,\n", "  datetime.date(2009, 12, 17): 1.6154,\n", "  datetime.date(2009, 12, 18): 1.615425,\n", "  datetime.date(2009, 12, 21): 1.603925,\n", "  datetime.date(2009, 12, 22): 1.59655,\n", "  datetime.date(2009, 12, 23): 1.59565,\n", "  datetime.date(2009, 12, 24): 1.5966,\n", "  datetime.date(2009, 12, 28): 1.60015,\n", "  datetime.date(2009, 12, 29): 1.5904,\n", "  datetime.date(2009, 12, 30): 1.607675,\n", "  datetime.date(2009, 12, 31): 1.616825,\n", "  datetime.date(2010, 1, 4): 1.608575,\n", "  datetime.date(2010, 1, 5): 1.599275,\n", "  datetime.date(2010, 1, 6): 1.601825,\n", "  datetime.date(2010, 1, 7): 1.593225,\n", "  datetime.date(2010, 1, 8): 1.60235,\n", "  datetime.date(2010, 1, 11): 1.61155,\n", "  datetime.date(2010, 1, 12): 1.61645,\n", "  datetime.date(2010, 1, 13): 1.628075,\n", "  datetime.date(2010, 1, 14): 1.6335,\n", "  datetime.date(2010, 1, 15): 1.6264,\n", "  datetime.date(2010, 1, 18): 1.634175,\n", "  datetime.date(2010, 1, 19): 1.63605,\n", "  datetime.date(2010, 1, 20): 1.629225,\n", "  datetime.date(2010, 1, 21): 1.619625,\n", "  datetime.date(2010, 1, 22): 1.61135,\n", "  datetime.date(2010, 1, 25): 1.6245,\n", "  datetime.date(2010, 1, 26): 1.61435,\n", "  datetime.date(2010, 1, 27): 1.61685,\n", "  datetime.date(2010, 1, 28): 1.61375,\n", "  datetime.date(2010, 1, 29): 1.598475,\n", "  datetime.date(2010, 2, 1): 1.595725,\n", "  datetime.date(2010, 2, 2): 1.59735,\n", "  datetime.date(2010, 2, 3): 1.589325,\n", "  datetime.date(2010, 2, 4): 1.575425,\n", "  datetime.date(2010, 2, 5): 1.564175,\n", "  datetime.date(2010, 2, 8): 1.55835,\n", "  datetime.date(2010, 2, 9): 1.5719,\n", "  datetime.date(2010, 2, 10): 1.559425,\n", "  datetime.date(2010, 2, 11): 1.5705,\n", "  datetime.date(2010, 2, 12): 1.57015,\n", "  datetime.date(2010, 2, 15): 1.566,\n", "  datetime.date(2010, 2, 16): 1.579275,\n", "  datetime.date(2010, 2, 17): 1.567375,\n", "  datetime.date(2010, 2, 18): 1.552925,\n", "  datetime.date(2010, 2, 19): 1.546975,\n", "  datetime.date(2010, 2, 22): 1.548175,\n", "  datetime.date(2010, 2, 23): 1.542225,\n", "  datetime.date(2010, 2, 24): 1.54095,\n", "  datetime.date(2010, 2, 25): 1.52645,\n", "  datetime.date(2010, 2, 26): 1.523625,\n", "  datetime.date(2010, 3, 1): 1.4991,\n", "  datetime.date(2010, 3, 2): 1.497075,\n", "  datetime.date(2010, 3, 3): 1.509875,\n", "  datetime.date(2010, 3, 4): 1.503275,\n", "  datetime.date(2010, 3, 5): 1.513125,\n", "  datetime.date(2010, 3, 8): 1.50665,\n", "  datetime.date(2010, 3, 9): 1.499625,\n", "  datetime.date(2010, 3, 10): 1.497775,\n", "  datetime.date(2010, 3, 11): 1.5062,\n", "  datetime.date(2010, 3, 12): 1.52035,\n", "  datetime.date(2010, 3, 15): 1.505675,\n", "  datetime.date(2010, 3, 16): 1.52435,\n", "  datetime.date(2010, 3, 17): 1.5325,\n", "  datetime.date(2010, 3, 18): 1.5242,\n", "  datetime.date(2010, 3, 19): 1.5013,\n", "  datetime.date(2010, 3, 22): 1.509975,\n", "  datetime.date(2010, 3, 23): 1.504625,\n", "  datetime.date(2010, 3, 24): 1.486675,\n", "  datetime.date(2010, 3, 25): 1.4812,\n", "  datetime.date(2010, 3, 26): 1.48975,\n", "  datetime.date(2010, 3, 29): 1.498725,\n", "  datetime.date(2010, 3, 30): 1.506925,\n", "  datetime.date(2010, 3, 31): 1.518275,\n", "  datetime.date(2010, 4, 1): 1.52925,\n", "  datetime.date(2010, 4, 2): 1.520925,\n", "  datetime.date(2010, 4, 5): 1.529625,\n", "  datetime.date(2010, 4, 6): 1.52665,\n", "  datetime.date(2010, 4, 7): 1.524275,\n", "  datetime.date(2010, 4, 8): 1.528,\n", "  datetime.date(2010, 4, 9): 1.537,\n", "  datetime.date(2010, 4, 12): 1.5369,\n", "  datetime.date(2010, 4, 13): 1.538125,\n", "  datetime.date(2010, 4, 14): 1.546675,\n", "  datetime.date(2010, 4, 15): 1.549475,\n", "  datetime.date(2010, 4, 16): 1.5363,\n", "  datetime.date(2010, 4, 19): 1.533775,\n", "  datetime.date(2010, 4, 20): 1.53585,\n", "  datetime.date(2010, 4, 21): 1.54105,\n", "  datetime.date(2010, 4, 22): 1.53785,\n", "  datetime.date(2010, 4, 23): 1.5377,\n", "  datetime.date(2010, 4, 26): 1.545775,\n", "  datetime.date(2010, 4, 27): 1.526525,\n", "  datetime.date(2010, 4, 28): 1.52085,\n", "  datetime.date(2010, 4, 29): 1.53205,\n", "  datetime.date(2010, 4, 30): 1.527125,\n", "  datetime.date(2010, 5, 3): 1.524625,\n", "  datetime.date(2010, 5, 4): 1.51435,\n", "  datetime.date(2010, 5, 5): 1.51015,\n", "  datetime.date(2010, 5, 6): 1.4833,\n", "  datetime.date(2010, 5, 7): 1.480425,\n", "  datetime.date(2010, 5, 10): 1.484625,\n", "  datetime.date(2010, 5, 11): 1.4956,\n", "  datetime.date(2010, 5, 12): 1.48235,\n", "  datetime.date(2010, 5, 13): 1.4613,\n", "  datetime.date(2010, 5, 14): 1.45385,\n", "  datetime.date(2010, 5, 17): 1.447575,\n", "  datetime.date(2010, 5, 18): 1.43335,\n", "  datetime.date(2010, 5, 19): 1.44435,\n", "  datetime.date(2010, 5, 20): 1.436525,\n", "  datetime.date(2010, 5, 21): 1.44605,\n", "  datetime.date(2010, 5, 24): 1.442425,\n", "  datetime.date(2010, 5, 25): 1.4408,\n", "  datetime.date(2010, 5, 26): 1.438825,\n", "  datetime.date(2010, 5, 27): 1.458175,\n", "  datetime.date(2010, 5, 28): 1.445825,\n", "  datetime.date(2010, 5, 31): 1.453625,\n", "  datetime.date(2010, 6, 1): 1.46495,\n", "  datetime.date(2010, 6, 2): 1.4652,\n", "  datetime.date(2010, 6, 3): 1.4614,\n", "  datetime.date(2010, 6, 4): 1.445525,\n", "  datetime.date(2010, 6, 7): 1.446775,\n", "  datetime.date(2010, 6, 8): 1.4469,\n", "  datetime.date(2010, 6, 9): 1.452775,\n", "  datetime.date(2010, 6, 10): 1.47145,\n", "  datetime.date(2010, 6, 11): 1.455075,\n", "  datetime.date(2010, 6, 14): 1.474175,\n", "  datetime.date(2010, 6, 15): 1.480425,\n", "  datetime.date(2010, 6, 16): 1.4731,\n", "  datetime.date(2010, 6, 17): 1.482375,\n", "  datetime.date(2010, 6, 18): 1.48235,\n", "  datetime.date(2010, 6, 21): 1.47565,\n", "  datetime.date(2010, 6, 22): 1.481525,\n", "  datetime.date(2010, 6, 23): 1.49575,\n", "  datetime.date(2010, 6, 24): 1.49365,\n", "  datetime.date(2010, 6, 25): 1.50585,\n", "  datetime.date(2010, 6, 28): 1.5105,\n", "  datetime.date(2010, 6, 29): 1.50655,\n", "  datetime.date(2010, 6, 30): 1.494475,\n", "  datetime.date(2010, 7, 1): 1.51765,\n", "  datetime.date(2010, 7, 2): 1.519525,\n", "  datetime.date(2010, 7, 5): 1.513475,\n", "  datetime.date(2010, 7, 6): 1.515,\n", "  datetime.date(2010, 7, 7): 1.5189,\n", "  datetime.date(2010, 7, 8): 1.51665,\n", "  datetime.date(2010, 7, 9): 1.5063,\n", "  datetime.date(2010, 7, 12): 1.5032,\n", "  datetime.date(2010, 7, 13): 1.51755,\n", "  datetime.date(2010, 7, 14): 1.52665,\n", "  datetime.date(2010, 7, 15): 1.54615,\n", "  datetime.date(2010, 7, 16): 1.530025,\n", "  datetime.date(2010, 7, 19): 1.522875,\n", "  datetime.date(2010, 7, 20): 1.526375,\n", "  datetime.date(2010, 7, 21): 1.516375,\n", "  datetime.date(2010, 7, 22): 1.52595,\n", "  datetime.date(2010, 7, 23): 1.54235,\n", "  datetime.date(2010, 7, 26): 1.548975,\n", "  datetime.date(2010, 7, 27): 1.55935,\n", "  datetime.date(2010, 7, 28): 1.559775,\n", "  datetime.date(2010, 7, 29): 1.561225,\n", "  datetime.date(2010, 7, 30): 1.569075,\n", "  datetime.date(2010, 8, 2): 1.588675,\n", "  datetime.date(2010, 8, 3): 1.595275,\n", "  datetime.date(2010, 8, 4): 1.5883,\n", "  datetime.date(2010, 8, 5): 1.58965,\n", "  datetime.date(2010, 8, 6): 1.59425,\n", "  datetime.date(2010, 8, 9): 1.589175,\n", "  datetime.date(2010, 8, 10): 1.585275,\n", "  datetime.date(2010, 8, 11): 1.56585,\n", "  datetime.date(2010, 8, 12): 1.557875,\n", "  datetime.date(2010, 8, 13): 1.559025,\n", "  datetime.date(2010, 8, 16): 1.566375,\n", "  datetime.date(2010, 8, 17): 1.558625,\n", "  datetime.date(2010, 8, 18): 1.55945,\n", "  datetime.date(2010, 8, 19): 1.5602,\n", "  datetime.date(2010, 8, 20): 1.55345,\n", "  datetime.date(2010, 8, 23): 1.551275,\n", "  datetime.date(2010, 8, 24): 1.53955,\n", "  datetime.date(2010, 8, 25): 1.5467,\n", "  datetime.date(2010, 8, 26): 1.55285,\n", "  datetime.date(2010, 8, 27): 1.552875,\n", "  datetime.date(2010, 8, 30): 1.5461,\n", "  datetime.date(2010, 8, 31): 1.534775,\n", "  datetime.date(2010, 9, 1): 1.545425,\n", "  datetime.date(2010, 9, 2): 1.540075,\n", "  datetime.date(2010, 9, 3): 1.545325,\n", "  datetime.date(2010, 9, 6): 1.539325,\n", "  datetime.date(2010, 9, 7): 1.535775,\n", "  datetime.date(2010, 9, 8): 1.5471,\n", "  datetime.date(2010, 9, 9): 1.542875,\n", "  datetime.date(2010, 9, 10): 1.53575,\n", "  datetime.date(2010, 9, 13): 1.542875,\n", "  datetime.date(2010, 9, 14): 1.553975,\n", "  datetime.date(2010, 9, 15): 1.5624,\n", "  datetime.date(2010, 9, 16): 1.56245,\n", "  datetime.date(2010, 9, 17): 1.56315,\n", "  datetime.date(2010, 9, 20): 1.55455,\n", "  datetime.date(2010, 9, 21): 1.56225,\n", "  datetime.date(2010, 9, 22): 1.56645,\n", "  datetime.date(2010, 9, 23): 1.568425,\n", "  datetime.date(2010, 9, 24): 1.582275,\n", "  datetime.date(2010, 9, 27): 1.582775,\n", "  datetime.date(2010, 9, 28): 1.580175,\n", "  datetime.date(2010, 9, 29): 1.578625,\n", "  datetime.date(2010, 9, 30): 1.57165,\n", "  datetime.date(2010, 10, 1): 1.58195,\n", "  datetime.date(2010, 10, 4): 1.582975,\n", "  datetime.date(2010, 10, 5): 1.588975,\n", "  datetime.date(2010, 10, 6): 1.589025,\n", "  datetime.date(2010, 10, 7): 1.5877,\n", "  datetime.date(2010, 10, 8): 1.596175,\n", "  datetime.date(2010, 10, 11): 1.58825,\n", "  datetime.date(2010, 10, 12): 1.580925,\n", "  datetime.date(2010, 10, 13): 1.5897,\n", "  datetime.date(2010, 10, 14): 1.60105,\n", "  datetime.date(2010, 10, 15): 1.599175,\n", "  datetime.date(2010, 10, 18): 1.587525,\n", "  datetime.date(2010, 10, 19): 1.57065,\n", "  datetime.date(2010, 10, 20): 1.5847,\n", "  datetime.date(2010, 10, 21): 1.57035,\n", "  datetime.date(2010, 10, 22): 1.5684,\n", "  datetime.date(2010, 10, 25): 1.57225,\n", "  datetime.date(2010, 10, 26): 1.58455,\n", "  datetime.date(2010, 10, 27): 1.57685,\n", "  datetime.date(2010, 10, 28): 1.594575,\n", "  datetime.date(2010, 10, 29): 1.604125,\n", "  datetime.date(2010, 11, 1): 1.603525,\n", "  datetime.date(2010, 11, 2): 1.604075,\n", "  datetime.date(2010, 11, 3): 1.6081,\n", "  datetime.date(2010, 11, 4): 1.6269,\n", "  datetime.date(2010, 11, 5): 1.618175,\n", "  datetime.date(2010, 11, 8): 1.61385,\n", "  datetime.date(2010, 11, 9): 1.598475,\n", "  datetime.date(2010, 11, 10): 1.612075,\n", "  datetime.date(2010, 11, 11): 1.61225,\n", "  datetime.date(2010, 11, 12): 1.611375,\n", "  datetime.date(2010, 11, 15): 1.605225,\n", "  datetime.date(2010, 11, 16): 1.588525,\n", "  datetime.date(2010, 11, 17): 1.590725,\n", "  datetime.date(2010, 11, 18): 1.604125,\n", "  datetime.date(2010, 11, 19): 1.59795,\n", "  datetime.date(2010, 11, 22): 1.59575,\n", "  datetime.date(2010, 11, 23): 1.5775,\n", "  datetime.date(2010, 11, 24): 1.577175,\n", "  datetime.date(2010, 11, 25): 1.57605,\n", "  datetime.date(2010, 11, 26): 1.55915,\n", "  datetime.date(2010, 11, 29): 1.557275,\n", "  datetime.date(2010, 11, 30): 1.5562,\n", "  datetime.date(2010, 12, 1): 1.562275,\n", "  datetime.date(2010, 12, 2): 1.55995,\n", "  datetime.date(2010, 12, 3): 1.57775,\n", "  datetime.date(2010, 12, 6): 1.571575,\n", "  datetime.date(2010, 12, 7): 1.57585,\n", "  datetime.date(2010, 12, 8): 1.5804,\n", "  datetime.date(2010, 12, 9): 1.577025,\n", "  datetime.date(2010, 12, 10): 1.580125,\n", "  datetime.date(2010, 12, 13): 1.5862,\n", "  datetime.date(2010, 12, 14): 1.5775,\n", "  datetime.date(2010, 12, 15): 1.55445,\n", "  datetime.date(2010, 12, 16): 1.563225,\n", "  datetime.date(2010, 12, 17): 1.5533,\n", "  datetime.date(2010, 12, 20): 1.551325,\n", "  datetime.date(2010, 12, 21): 1.547025,\n", "  datetime.date(2010, 12, 22): 1.53865,\n", "  datetime.date(2010, 12, 23): 1.54275,\n", "  datetime.date(2010, 12, 24): 1.54465,\n", "  datetime.date(2010, 12, 27): 1.542175,\n", "  datetime.date(2010, 12, 28): 1.536625,\n", "  datetime.date(2010, 12, 29): 1.550025,\n", "  datetime.date(2010, 12, 30): 1.5426,\n", "  datetime.date(2010, 12, 31): 1.561225,\n", "  datetime.date(2011, 1, 3): 1.5489,\n", "  datetime.date(2011, 1, 4): 1.55865,\n", "  datetime.date(2011, 1, 5): 1.551425,\n", "  datetime.date(2011, 1, 6): 1.5473,\n", "  datetime.date(2011, 1, 7): 1.55475,\n", "  datetime.date(2011, 1, 10): 1.5573,\n", "  datetime.date(2011, 1, 11): 1.559875,\n", "  datetime.date(2011, 1, 12): 1.576125,\n", "  datetime.date(2011, 1, 13): 1.583725,\n", "  datetime.date(2011, 1, 14): 1.586975,\n", "  datetime.date(2011, 1, 17): 1.58875,\n", "  datetime.date(2011, 1, 18): 1.59625,\n", "  datetime.date(2011, 1, 19): 1.59955,\n", "  datetime.date(2011, 1, 20): 1.589725,\n", "  datetime.date(2011, 1, 21): 1.6,\n", "  datetime.date(2011, 1, 24): 1.5988,\n", "  datetime.date(2011, 1, 25): 1.5817,\n", "  datetime.date(2011, 1, 26): 1.59345,\n", "  datetime.date(2011, 1, 27): 1.59285,\n", "  datetime.date(2011, 1, 28): 1.5861,\n", "  datetime.date(2011, 1, 31): 1.601325,\n", "  datetime.date(2011, 2, 1): 1.614175,\n", "  datetime.date(2011, 2, 2): 1.619175,\n", "  datetime.date(2011, 2, 3): 1.613625,\n", "  datetime.date(2011, 2, 4): 1.6111,\n", "  datetime.date(2011, 2, 7): 1.610925,\n", "  datetime.date(2011, 2, 8): 1.60685,\n", "  datetime.date(2011, 2, 9): 1.61015,\n", "  datetime.date(2011, 2, 10): 1.6098,\n", "  datetime.date(2011, 2, 11): 1.60075,\n", "  datetime.date(2011, 2, 14): 1.603775,\n", "  datetime.date(2011, 2, 15): 1.6126,\n", "  datetime.date(2011, 2, 16): 1.60945,\n", "  datetime.date(2011, 2, 17): 1.617375,\n", "  datetime.date(2011, 2, 18): 1.625375,\n", "  datetime.date(2011, 2, 21): 1.62255,\n", "  datetime.date(2011, 2, 22): 1.613675,\n", "  datetime.date(2011, 2, 23): 1.6212,\n", "  datetime.date(2011, 2, 24): 1.6137,\n", "  datetime.date(2011, 2, 25): 1.611825,\n", "  datetime.date(2011, 2, 28): 1.62565,\n", "  datetime.date(2011, 3, 1): 1.626675,\n", "  datetime.date(2011, 3, 2): 1.632425,\n", "  datetime.date(2011, 3, 3): 1.6277,\n", "  datetime.date(2011, 3, 4): 1.6268,\n", "  datetime.date(2011, 3, 7): 1.620125,\n", "  datetime.date(2011, 3, 8): 1.616025,\n", "  datetime.date(2011, 3, 9): 1.6203,\n", "  datetime.date(2011, 3, 10): 1.606125,\n", "  datetime.date(2011, 3, 11): 1.608175,\n", "  datetime.date(2011, 3, 14): 1.617225,\n", "  datetime.date(2011, 3, 15): 1.60785,\n", "  datetime.date(2011, 3, 16): 1.602525,\n", "  datetime.date(2011, 3, 17): 1.613575,\n", "  datetime.date(2011, 3, 18): 1.623325,\n", "  datetime.date(2011, 3, 21): 1.631025,\n", "  datetime.date(2011, 3, 22): 1.63645,\n", "  datetime.date(2011, 3, 23): 1.623475,\n", "  datetime.date(2011, 3, 24): 1.612175,\n", "  datetime.date(2011, 3, 25): 1.6042,\n", "  datetime.date(2011, 3, 28): 1.599275,\n", "  datetime.date(2011, 3, 29): 1.60115,\n", "  datetime.date(2011, 3, 30): 1.607275,\n", "  datetime.date(2011, 3, 31): 1.602775,\n", "  datetime.date(2011, 4, 1): 1.611075,\n", "  datetime.date(2011, 4, 4): 1.613225,\n", "  datetime.date(2011, 4, 5): 1.629675,\n", "  datetime.date(2011, 4, 6): 1.633125,\n", "  datetime.date(2011, 4, 7): 1.632325,\n", "  datetime.date(2011, 4, 8): 1.638,\n", "  datetime.date(2011, 4, 11): 1.634575,\n", "  datetime.date(2011, 4, 12): 1.62575,\n", "  datetime.date(2011, 4, 13): 1.626925,\n", "  datetime.date(2011, 4, 14): 1.635225,\n", "  datetime.date(2011, 4, 15): 1.63255,\n", "  datetime.date(2011, 4, 18): 1.6266,\n", "  datetime.date(2011, 4, 19): 1.631475,\n", "  datetime.date(2011, 4, 20): 1.640925,\n", "  datetime.date(2011, 4, 21): 1.652175,\n", "  datetime.date(2011, 4, 22): 1.650925,\n", "  datetime.date(2011, 4, 25): 1.64985,\n", "  datetime.date(2011, 4, 26): 1.6482,\n", "  datetime.date(2011, 4, 27): 1.66275,\n", "  datetime.date(2011, 4, 28): 1.663125,\n", "  datetime.date(2011, 4, 29): 1.670875,\n", "  datetime.date(2011, 5, 2): 1.665525,\n", "  datetime.date(2011, 5, 3): 1.648725,\n", "  datetime.date(2011, 5, 4): 1.648875,\n", "  datetime.date(2011, 5, 5): 1.638875,\n", "  datetime.date(2011, 5, 6): 1.636675,\n", "  datetime.date(2011, 5, 9): 1.64035,\n", "  datetime.date(2011, 5, 10): 1.63635,\n", "  datetime.date(2011, 5, 11): 1.634525,\n", "  datetime.date(2011, 5, 12): 1.629125,\n", "  datetime.date(2011, 5, 13): 1.619725,\n", "  datetime.date(2011, 5, 16): 1.61915,\n", "  datetime.date(2011, 5, 17): 1.6252,\n", "  datetime.date(2011, 5, 18): 1.6168,\n", "  datetime.date(2011, 5, 19): 1.623625,\n", "  datetime.date(2011, 5, 20): 1.6229,\n", "  datetime.date(2011, 5, 23): 1.612275,\n", "  datetime.date(2011, 5, 24): 1.6181,\n", "  datetime.date(2011, 5, 25): 1.627375,\n", "  datetime.date(2011, 5, 26): 1.640275,\n", "  datetime.date(2011, 5, 27): 1.6509,\n", "  datetime.date(2011, 5, 30): 1.647425,\n", "  datetime.date(2011, 5, 31): 1.64475,\n", "  datetime.date(2011, 6, 1): 1.633375,\n", "  datetime.date(2011, 6, 2): 1.63715,\n", "  datetime.date(2011, 6, 3): 1.64255,\n", "  datetime.date(2011, 6, 6): 1.63565,\n", "  datetime.date(2011, 6, 7): 1.6446,\n", "  datetime.date(2011, 6, 8): 1.640325,\n", "  datetime.date(2011, 6, 9): 1.6367,\n", "  datetime.date(2011, 6, 10): 1.622725,\n", "  datetime.date(2011, 6, 13): 1.637675,\n", "  datetime.date(2011, 6, 14): 1.637,\n", "  datetime.date(2011, 6, 15): 1.619375,\n", "  datetime.date(2011, 6, 16): 1.615875,\n", "  datetime.date(2011, 6, 17): 1.619375,\n", "  datetime.date(2011, 6, 20): 1.62015,\n", "  datetime.date(2011, 6, 21): 1.624525,\n", "  datetime.date(2011, 6, 22): 1.607175,\n", "  datetime.date(2011, 6, 23): 1.600525,\n", "  datetime.date(2011, 6, 24): 1.595925,\n", "  datetime.date(2011, 6, 27): 1.598975,\n", "  datetime.date(2011, 6, 28): 1.600075,\n", "  datetime.date(2011, 6, 29): 1.6063,\n", "  datetime.date(2011, 6, 30): 1.6053,\n", "  datetime.date(2011, 7, 1): 1.607225,\n", "  datetime.date(2011, 7, 4): 1.6083,\n", "  datetime.date(2011, 7, 5): 1.60605,\n", "  datetime.date(2011, 7, 6): 1.600375,\n", "  datetime.date(2011, 7, 7): 1.59735,\n", "  datetime.date(2011, 7, 8): 1.605825,\n", "  datetime.date(2011, 7, 11): 1.590625,\n", "  datetime.date(2011, 7, 12): 1.591125,\n", "  datetime.date(2011, 7, 13): 1.610575,\n", "  datetime.date(2011, 7, 14): 1.61405,\n", "  datetime.date(2011, 7, 15): 1.613625,\n", "  datetime.date(2011, 7, 18): 1.605725,\n", "  datetime.date(2011, 7, 19): 1.61235,\n", "  datetime.date(2011, 7, 20): 1.615475,\n", "  datetime.date(2011, 7, 21): 1.633125,\n", "  datetime.date(2011, 7, 22): 1.630025,\n", "  datetime.date(2011, 7, 25): 1.62755,\n", "  datetime.date(2011, 7, 26): 1.640575,\n", "  datetime.date(2011, 7, 27): 1.633025,\n", "  datetime.date(2011, 7, 28): 1.6373,\n", "  datetime.date(2011, 7, 29): 1.642475,\n", "  datetime.date(2011, 8, 1): 1.6295,\n", "  datetime.date(2011, 8, 2): 1.62985,\n", "  datetime.date(2011, 8, 3): 1.642775,\n", "  datetime.date(2011, 8, 4): 1.6256,\n", "  datetime.date(2011, 8, 5): 1.639075,\n", "  datetime.date(2011, 8, 8): 1.63175,\n", "  datetime.date(2011, 8, 9): 1.6316,\n", "  datetime.date(2011, 8, 10): 1.61335,\n", "  datetime.date(2011, 8, 11): 1.6238,\n", "  datetime.date(2011, 8, 12): 1.627825,\n", "  datetime.date(2011, 8, 15): 1.638975,\n", "  datetime.date(2011, 8, 16): 1.645475,\n", "  datetime.date(2011, 8, 17): 1.654275,\n", "  datetime.date(2011, 8, 18): 1.651625,\n", "  datetime.date(2011, 8, 19): 1.646475,\n", "  datetime.date(2011, 8, 22): 1.645325,\n", "  datetime.date(2011, 8, 23): 1.6495,\n", "  datetime.date(2011, 8, 24): 1.637425,\n", "  datetime.date(2011, 8, 25): 1.62815,\n", "  datetime.date(2011, 8, 26): 1.63695,\n", "  datetime.date(2011, 8, 29): 1.6408,\n", "  datetime.date(2011, 8, 30): 1.63,\n", "  datetime.date(2011, 8, 31): 1.625225,\n", "  datetime.date(2011, 9, 1): 1.61795,\n", "  datetime.date(2011, 9, 2): 1.62175,\n", "  datetime.date(2011, 9, 5): 1.611775,\n", "  datetime.date(2011, 9, 6): 1.594225,\n", "  datetime.date(2011, 9, 7): 1.59905,\n", "  datetime.date(2011, 9, 8): 1.595875,\n", "  datetime.date(2011, 9, 9): 1.588125,\n", "  datetime.date(2011, 9, 12): 1.5861,\n", "  datetime.date(2011, 9, 13): 1.577925,\n", "  datetime.date(2011, 9, 14): 1.577,\n", "  datetime.date(2011, 9, 15): 1.57995,\n", "  datetime.date(2011, 9, 16): 1.579225,\n", "  datetime.date(2011, 9, 19): 1.570275,\n", "  datetime.date(2011, 9, 20): 1.5738,\n", "  datetime.date(2011, 9, 21): 1.5499,\n", "  datetime.date(2011, 9, 22): 1.534225,\n", "  datetime.date(2011, 9, 23): 1.54475,\n", "  datetime.date(2011, 9, 26): 1.5564,\n", "  datetime.date(2011, 9, 27): 1.56345,\n", "  datetime.date(2011, 9, 28): 1.557775,\n", "  datetime.date(2011, 9, 29): 1.56275,\n", "  datetime.date(2011, 9, 30): 1.55845,\n", "  datetime.date(2011, 10, 3): 1.54325,\n", "  datetime.date(2011, 10, 4): 1.548775,\n", "  ...},\n", " 'USDJPY': {datetime.date(2007, 11, 2): 114.86,\n", "  datetime.date(2007, 11, 9): 110.695,\n", "  datetime.date(2007, 11, 29): 109.935,\n", "  datetime.date(2007, 11, 30): 111.2375,\n", "  datetime.date(2007, 12, 3): 110.465,\n", "  datetime.date(2007, 12, 4): 109.87,\n", "  datetime.date(2007, 12, 5): 110.8875,\n", "  datetime.date(2007, 12, 6): 111.3225,\n", "  datetime.date(2007, 12, 7): 111.69,\n", "  datetime.date(2007, 12, 10): 111.705,\n", "  datetime.date(2007, 12, 11): 110.66,\n", "  datetime.date(2007, 12, 12): 112.24,\n", "  datetime.date(2007, 12, 13): 112.2125,\n", "  datetime.date(2007, 12, 14): 113.275,\n", "  datetime.date(2007, 12, 17): 112.9475,\n", "  datetime.date(2007, 12, 18): 113.4,\n", "  datetime.date(2007, 12, 19): 113.43,\n", "  datetime.date(2007, 12, 20): 113.14,\n", "  datetime.date(2007, 12, 21): 114.0625,\n", "  datetime.date(2007, 12, 24): 114.38,\n", "  datetime.date(2007, 12, 26): 114.34,\n", "  datetime.date(2007, 12, 27): 113.735,\n", "  datetime.date(2007, 12, 28): 112.285,\n", "  datetime.date(2007, 12, 31): 111.71,\n", "  datetime.date(2008, 1, 2): 109.66,\n", "  datetime.date(2008, 1, 3): 109.33,\n", "  datetime.date(2008, 1, 4): 108.605,\n", "  datetime.date(2008, 1, 7): 109.18,\n", "  datetime.date(2008, 1, 8): 108.905,\n", "  datetime.date(2008, 1, 9): 110.04,\n", "  datetime.date(2008, 1, 10): 109.335,\n", "  datetime.date(2008, 1, 11): 108.86,\n", "  datetime.date(2008, 1, 14): 108.1675,\n", "  datetime.date(2008, 1, 15): 106.785,\n", "  datetime.date(2008, 1, 16): 107.64,\n", "  datetime.date(2008, 1, 17): 106.5425,\n", "  datetime.date(2008, 1, 18): 106.8625,\n", "  datetime.date(2008, 1, 21): 105.99,\n", "  datetime.date(2008, 1, 22): 106.4425,\n", "  datetime.date(2008, 1, 23): 106.7175,\n", "  datetime.date(2008, 1, 24): 107.18,\n", "  datetime.date(2008, 1, 25): 106.7225,\n", "  datetime.date(2008, 1, 28): 106.8975,\n", "  datetime.date(2008, 1, 29): 107.1175,\n", "  datetime.date(2008, 1, 30): 106.225,\n", "  datetime.date(2008, 1, 31): 106.4375,\n", "  datetime.date(2008, 2, 1): 106.485,\n", "  datetime.date(2008, 2, 4): 106.7175,\n", "  datetime.date(2008, 2, 5): 106.815,\n", "  datetime.date(2008, 2, 6): 106.54,\n", "  datetime.date(2008, 2, 7): 107.485,\n", "  datetime.date(2008, 2, 8): 107.3275,\n", "  datetime.date(2008, 2, 11): 106.9675,\n", "  datetime.date(2008, 2, 12): 107.315,\n", "  datetime.date(2008, 2, 13): 108.3325,\n", "  datetime.date(2008, 2, 14): 107.87,\n", "  datetime.date(2008, 2, 15): 107.82,\n", "  datetime.date(2008, 2, 18): 108.23,\n", "  datetime.date(2008, 2, 19): 107.7775,\n", "  datetime.date(2008, 2, 20): 108.12,\n", "  datetime.date(2008, 2, 21): 107.4025,\n", "  datetime.date(2008, 2, 22): 107.175,\n", "  datetime.date(2008, 2, 25): 108.0675,\n", "  datetime.date(2008, 2, 26): 107.28,\n", "  datetime.date(2008, 2, 27): 106.4875,\n", "  datetime.date(2008, 2, 28): 105.37,\n", "  datetime.date(2008, 2, 29): 103.735,\n", "  datetime.date(2008, 3, 3): 103.49,\n", "  datetime.date(2008, 3, 4): 103.3675,\n", "  datetime.date(2008, 3, 5): 104.02,\n", "  datetime.date(2008, 3, 6): 102.675,\n", "  datetime.date(2008, 3, 7): 102.675,\n", "  datetime.date(2008, 3, 10): 101.755,\n", "  datetime.date(2008, 3, 11): 103.42,\n", "  datetime.date(2008, 3, 12): 101.7925,\n", "  datetime.date(2008, 3, 13): 100.65,\n", "  datetime.date(2008, 3, 14): 99.085,\n", "  datetime.date(2008, 3, 17): 97.325,\n", "  datetime.date(2008, 3, 18): 99.86,\n", "  datetime.date(2008, 3, 19): 99.035,\n", "  datetime.date(2008, 3, 20): 99.515,\n", "  datetime.date(2008, 3, 21): 99.53,\n", "  datetime.date(2008, 3, 24): 100.745,\n", "  datetime.date(2008, 3, 25): 99.98,\n", "  datetime.date(2008, 3, 26): 99.2,\n", "  datetime.date(2008, 3, 27): 99.655,\n", "  datetime.date(2008, 3, 28): 99.245,\n", "  datetime.date(2008, 3, 31): 99.695,\n", "  datetime.date(2008, 4, 1): 101.845,\n", "  datetime.date(2008, 4, 2): 102.36,\n", "  datetime.date(2008, 4, 3): 102.265,\n", "  datetime.date(2008, 4, 4): 101.47,\n", "  datetime.date(2008, 4, 7): 102.4075,\n", "  datetime.date(2008, 4, 8): 102.66,\n", "  datetime.date(2008, 4, 9): 101.795,\n", "  datetime.date(2008, 4, 10): 101.9475,\n", "  datetime.date(2008, 4, 11): 101.0025,\n", "  datetime.date(2008, 4, 14): 101.0975,\n", "  datetime.date(2008, 4, 15): 101.8275,\n", "  datetime.date(2008, 4, 16): 101.83,\n", "  datetime.date(2008, 4, 17): 102.4875,\n", "  datetime.date(2008, 4, 18): 103.665,\n", "  datetime.date(2008, 4, 21): 103.27,\n", "  datetime.date(2008, 4, 22): 103.025,\n", "  datetime.date(2008, 4, 23): 103.375,\n", "  datetime.date(2008, 4, 24): 104.275,\n", "  datetime.date(2008, 4, 25): 104.425,\n", "  datetime.date(2008, 4, 28): 104.19,\n", "  datetime.date(2008, 4, 29): 104.015,\n", "  datetime.date(2008, 4, 30): 103.895,\n", "  datetime.date(2008, 5, 1): 104.4275,\n", "  datetime.date(2008, 5, 2): 105.39,\n", "  datetime.date(2008, 5, 5): 104.855,\n", "  datetime.date(2008, 5, 6): 104.77,\n", "  datetime.date(2008, 5, 7): 104.735,\n", "  datetime.date(2008, 5, 8): 103.7375,\n", "  datetime.date(2008, 5, 9): 102.8975,\n", "  datetime.date(2008, 5, 12): 103.75,\n", "  datetime.date(2008, 5, 13): 104.75,\n", "  datetime.date(2008, 5, 14): 105.0475,\n", "  datetime.date(2008, 5, 15): 104.7425,\n", "  datetime.date(2008, 5, 16): 104.06,\n", "  datetime.date(2008, 5, 19): 104.33,\n", "  datetime.date(2008, 5, 20): 103.6775,\n", "  datetime.date(2008, 5, 21): 103.055,\n", "  datetime.date(2008, 5, 22): 104.0775,\n", "  datetime.date(2008, 5, 23): 103.38,\n", "  datetime.date(2008, 5, 26): 103.4275,\n", "  datetime.date(2008, 5, 27): 104.24,\n", "  datetime.date(2008, 5, 28): 104.69,\n", "  datetime.date(2008, 5, 29): 105.5,\n", "  datetime.date(2008, 5, 30): 105.515,\n", "  datetime.date(2008, 6, 2): 104.43,\n", "  datetime.date(2008, 6, 3): 105.0875,\n", "  datetime.date(2008, 6, 4): 105.2275,\n", "  datetime.date(2008, 6, 5): 105.945,\n", "  datetime.date(2008, 6, 6): 104.925,\n", "  datetime.date(2008, 6, 9): 106.305,\n", "  datetime.date(2008, 6, 10): 107.4425,\n", "  datetime.date(2008, 6, 11): 106.9675,\n", "  datetime.date(2008, 6, 12): 107.96,\n", "  datetime.date(2008, 6, 13): 108.195,\n", "  datetime.date(2008, 6, 16): 108.22,\n", "  datetime.date(2008, 6, 17): 107.935,\n", "  datetime.date(2008, 6, 18): 107.88,\n", "  datetime.date(2008, 6, 19): 108.005,\n", "  datetime.date(2008, 6, 20): 107.3325,\n", "  datetime.date(2008, 6, 23): 107.85,\n", "  datetime.date(2008, 6, 24): 107.815,\n", "  datetime.date(2008, 6, 25): 107.805,\n", "  datetime.date(2008, 6, 26): 106.815,\n", "  datetime.date(2008, 6, 27): 106.145,\n", "  datetime.date(2008, 6, 30): 106.215,\n", "  datetime.date(2008, 7, 1): 106.125,\n", "  datetime.date(2008, 7, 2): 105.9375,\n", "  datetime.date(2008, 7, 3): 106.73,\n", "  datetime.date(2008, 7, 4): 106.795,\n", "  datetime.date(2008, 7, 7): 107.1875,\n", "  datetime.date(2008, 7, 8): 107.5075,\n", "  datetime.date(2008, 7, 9): 106.7675,\n", "  datetime.date(2008, 7, 10): 107.0725,\n", "  datetime.date(2008, 7, 11): 106.285,\n", "  datetime.date(2008, 7, 14): 106.145,\n", "  datetime.date(2008, 7, 15): 104.7225,\n", "  datetime.date(2008, 7, 16): 105.135,\n", "  datetime.date(2008, 7, 17): 106.2775,\n", "  datetime.date(2008, 7, 18): 106.96,\n", "  datetime.date(2008, 7, 21): 106.4475,\n", "  datetime.date(2008, 7, 22): 107.335,\n", "  datetime.date(2008, 7, 23): 107.8875,\n", "  datetime.date(2008, 7, 24): 107.33,\n", "  datetime.date(2008, 7, 25): 107.835,\n", "  datetime.date(2008, 7, 28): 107.46,\n", "  datetime.date(2008, 7, 29): 108.1125,\n", "  datetime.date(2008, 7, 30): 108.1325,\n", "  datetime.date(2008, 7, 31): 107.92,\n", "  datetime.date(2008, 8, 1): 107.6875,\n", "  datetime.date(2008, 8, 4): 108.27,\n", "  datetime.date(2008, 8, 5): 108.345,\n", "  datetime.date(2008, 8, 6): 109.795,\n", "  datetime.date(2008, 8, 7): 109.4325,\n", "  datetime.date(2008, 8, 8): 110.17,\n", "  datetime.date(2008, 8, 11): 110.0625,\n", "  datetime.date(2008, 8, 12): 109.275,\n", "  datetime.date(2008, 8, 13): 109.53,\n", "  datetime.date(2008, 8, 14): 109.7325,\n", "  datetime.date(2008, 8, 15): 110.495,\n", "  datetime.date(2008, 8, 18): 110.1325,\n", "  datetime.date(2008, 8, 19): 109.72,\n", "  datetime.date(2008, 8, 20): 109.8575,\n", "  datetime.date(2008, 8, 21): 108.435,\n", "  datetime.date(2008, 8, 22): 110.08,\n", "  datetime.date(2008, 8, 25): 109.305,\n", "  datetime.date(2008, 8, 26): 109.605,\n", "  datetime.date(2008, 8, 27): 109.505,\n", "  datetime.date(2008, 8, 28): 109.495,\n", "  datetime.date(2008, 8, 29): 108.815,\n", "  datetime.date(2008, 9, 1): 108.125,\n", "  datetime.date(2008, 9, 2): 108.61,\n", "  datetime.date(2008, 9, 3): 108.295,\n", "  datetime.date(2008, 9, 4): 107.08,\n", "  datetime.date(2008, 9, 5): 107.74,\n", "  datetime.date(2008, 9, 8): 108.275,\n", "  datetime.date(2008, 9, 9): 106.805,\n", "  datetime.date(2008, 9, 10): 107.7125,\n", "  datetime.date(2008, 9, 11): 107.16,\n", "  datetime.date(2008, 9, 12): 107.945,\n", "  datetime.date(2008, 9, 15): 104.66,\n", "  datetime.date(2008, 9, 16): 105.65,\n", "  datetime.date(2008, 9, 17): 104.66,\n", "  datetime.date(2008, 9, 18): 105.44,\n", "  datetime.date(2008, 9, 19): 107.4325,\n", "  datetime.date(2008, 9, 22): 105.515,\n", "  datetime.date(2008, 9, 23): 105.565,\n", "  datetime.date(2008, 9, 24): 106.11,\n", "  datetime.date(2008, 9, 25): 106.565,\n", "  datetime.date(2008, 9, 26): 106.01,\n", "  datetime.date(2008, 9, 29): 104.175,\n", "  datetime.date(2008, 9, 30): 106.1125,\n", "  datetime.date(2008, 10, 1): 105.72,\n", "  datetime.date(2008, 10, 2): 105.3275,\n", "  datetime.date(2008, 10, 3): 105.3625,\n", "  datetime.date(2008, 10, 6): 101.805,\n", "  datetime.date(2008, 10, 7): 101.4725,\n", "  datetime.date(2008, 10, 8): 99.1325,\n", "  datetime.date(2008, 10, 9): 99.815,\n", "  datetime.date(2008, 10, 10): 100.725,\n", "  datetime.date(2008, 10, 13): 102.0125,\n", "  datetime.date(2008, 10, 14): 102.08,\n", "  datetime.date(2008, 10, 15): 99.955,\n", "  datetime.date(2008, 10, 16): 101.575,\n", "  datetime.date(2008, 10, 17): 101.65,\n", "  datetime.date(2008, 10, 20): 101.875,\n", "  datetime.date(2008, 10, 21): 100.135,\n", "  datetime.date(2008, 10, 22): 97.67,\n", "  datetime.date(2008, 10, 23): 97.355,\n", "  datetime.date(2008, 10, 24): 94.3225,\n", "  datetime.date(2008, 10, 27): 92.77,\n", "  datetime.date(2008, 10, 28): 98.0375,\n", "  datetime.date(2008, 10, 29): 97.3825,\n", "  datetime.date(2008, 10, 30): 98.61,\n", "  datetime.date(2008, 10, 31): 98.47,\n", "  datetime.date(2008, 11, 3): 99.12,\n", "  datetime.date(2008, 11, 4): 99.7025,\n", "  datetime.date(2008, 11, 5): 97.9425,\n", "  datetime.date(2008, 11, 6): 97.735,\n", "  datetime.date(2008, 11, 7): 98.2375,\n", "  datetime.date(2008, 11, 10): 98.0025,\n", "  datetime.date(2008, 11, 11): 97.6475,\n", "  datetime.date(2008, 11, 12): 94.995,\n", "  datetime.date(2008, 11, 13): 97.695,\n", "  datetime.date(2008, 11, 14): 97.13,\n", "  datetime.date(2008, 11, 17): 96.435,\n", "  datetime.date(2008, 11, 18): 97.02,\n", "  datetime.date(2008, 11, 19): 95.74,\n", "  datetime.date(2008, 11, 20): 93.695,\n", "  datetime.date(2008, 11, 21): 95.96,\n", "  datetime.date(2008, 11, 24): 97.35,\n", "  datetime.date(2008, 11, 25): 95.23,\n", "  datetime.date(2008, 11, 26): 95.67,\n", "  datetime.date(2008, 11, 27): 95.3425,\n", "  datetime.date(2008, 11, 28): 95.55,\n", "  datetime.date(2008, 12, 1): 93.1925,\n", "  datetime.date(2008, 12, 2): 93.175,\n", "  datetime.date(2008, 12, 3): 93.2975,\n", "  datetime.date(2008, 12, 4): 92.235,\n", "  datetime.date(2008, 12, 5): 92.865,\n", "  datetime.date(2008, 12, 8): 92.8025,\n", "  datetime.date(2008, 12, 9): 92.1325,\n", "  datetime.date(2008, 12, 10): 92.765,\n", "  datetime.date(2008, 12, 11): 91.4625,\n", "  datetime.date(2008, 12, 12): 91.1475,\n", "  datetime.date(2008, 12, 15): 90.655,\n", "  datetime.date(2008, 12, 16): 89.055,\n", "  datetime.date(2008, 12, 17): 87.235,\n", "  datetime.date(2008, 12, 18): 89.4425,\n", "  datetime.date(2008, 12, 19): 89.315,\n", "  datetime.date(2008, 12, 22): 90.245,\n", "  datetime.date(2008, 12, 23): 90.985,\n", "  datetime.date(2008, 12, 24): 90.45,\n", "  datetime.date(2008, 12, 26): 90.8125,\n", "  datetime.date(2008, 12, 29): 90.6525,\n", "  datetime.date(2008, 12, 30): 90.3225,\n", "  datetime.date(2008, 12, 31): 90.66,\n", "  datetime.date(2009, 1, 2): 92.055,\n", "  datetime.date(2009, 1, 5): 93.435,\n", "  datetime.date(2009, 1, 6): 93.6625,\n", "  datetime.date(2009, 1, 7): 92.65,\n", "  datetime.date(2009, 1, 8): 91.185,\n", "  datetime.date(2009, 1, 9): 90.39,\n", "  datetime.date(2009, 1, 12): 89.22,\n", "  datetime.date(2009, 1, 13): 89.385,\n", "  datetime.date(2009, 1, 14): 89.055,\n", "  datetime.date(2009, 1, 15): 89.8475,\n", "  datetime.date(2009, 1, 16): 90.72,\n", "  datetime.date(2009, 1, 19): 90.6375,\n", "  datetime.date(2009, 1, 20): 89.76,\n", "  datetime.date(2009, 1, 21): 89.49,\n", "  datetime.date(2009, 1, 22): 88.905,\n", "  datetime.date(2009, 1, 23): 88.745,\n", "  datetime.date(2009, 1, 26): 89.0875,\n", "  datetime.date(2009, 1, 27): 88.97,\n", "  datetime.date(2009, 1, 28): 90.2975,\n", "  datetime.date(2009, 1, 29): 90.0375,\n", "  datetime.date(2009, 1, 30): 89.9275,\n", "  datetime.date(2009, 2, 2): 89.45,\n", "  datetime.date(2009, 2, 3): 89.44,\n", "  datetime.date(2009, 2, 4): 89.43,\n", "  datetime.date(2009, 2, 5): 91.23,\n", "  datetime.date(2009, 2, 6): 91.8925,\n", "  datetime.date(2009, 2, 9): 91.455,\n", "  datetime.date(2009, 2, 10): 90.475,\n", "  datetime.date(2009, 2, 11): 90.395,\n", "  datetime.date(2009, 2, 12): 90.94,\n", "  datetime.date(2009, 2, 13): 91.8675,\n", "  datetime.date(2009, 2, 16): 91.74,\n", "  datetime.date(2009, 2, 17): 92.4125,\n", "  datetime.date(2009, 2, 18): 93.785,\n", "  datetime.date(2009, 2, 19): 94.1925,\n", "  datetime.date(2009, 2, 20): 93.3425,\n", "  datetime.date(2009, 2, 23): 94.61,\n", "  datetime.date(2009, 2, 24): 96.645,\n", "  datetime.date(2009, 2, 25): 97.39,\n", "  datetime.date(2009, 2, 26): 98.515,\n", "  datetime.date(2009, 2, 27): 97.64,\n", "  datetime.date(2009, 3, 2): 97.4475,\n", "  datetime.date(2009, 3, 3): 98.17,\n", "  datetime.date(2009, 3, 4): 99.155,\n", "  datetime.date(2009, 3, 5): 98.0675,\n", "  datetime.date(2009, 3, 6): 98.305,\n", "  datetime.date(2009, 3, 9): 98.8475,\n", "  datetime.date(2009, 3, 10): 98.665,\n", "  datetime.date(2009, 3, 11): 97.275,\n", "  datetime.date(2009, 3, 12): 97.72,\n", "  datetime.date(2009, 3, 13): 97.9625,\n", "  datetime.date(2009, 3, 16): 98.18,\n", "  datetime.date(2009, 3, 17): 98.6,\n", "  datetime.date(2009, 3, 18): 96.2425,\n", "  datetime.date(2009, 3, 19): 94.515,\n", "  datetime.date(2009, 3, 20): 95.945,\n", "  datetime.date(2009, 3, 23): 96.955,\n", "  datetime.date(2009, 3, 24): 97.865,\n", "  datetime.date(2009, 3, 25): 97.535,\n", "  datetime.date(2009, 3, 26): 98.71,\n", "  datetime.date(2009, 3, 27): 97.87,\n", "  datetime.date(2009, 3, 30): 97.255,\n", "  datetime.date(2009, 3, 31): 98.9525,\n", "  datetime.date(2009, 4, 1): 98.52,\n", "  datetime.date(2009, 4, 2): 99.525,\n", "  datetime.date(2009, 4, 3): 100.315,\n", "  datetime.date(2009, 4, 6): 100.99,\n", "  datetime.date(2009, 4, 7): 100.41,\n", "  datetime.date(2009, 4, 8): 99.755,\n", "  datetime.date(2009, 4, 9): 100.42,\n", "  datetime.date(2009, 4, 10): 100.335,\n", "  datetime.date(2009, 4, 13): 100.1,\n", "  datetime.date(2009, 4, 14): 98.9775,\n", "  datetime.date(2009, 4, 15): 99.375,\n", "  datetime.date(2009, 4, 16): 99.275,\n", "  datetime.date(2009, 4, 17): 99.115,\n", "  datetime.date(2009, 4, 20): 97.89,\n", "  datetime.date(2009, 4, 21): 98.73,\n", "  datetime.date(2009, 4, 22): 98.015,\n", "  datetime.date(2009, 4, 23): 97.9575,\n", "  datetime.date(2009, 4, 24): 97.165,\n", "  datetime.date(2009, 4, 27): 96.78,\n", "  datetime.date(2009, 4, 28): 96.45,\n", "  datetime.date(2009, 4, 29): 97.655,\n", "  datetime.date(2009, 4, 30): 98.63,\n", "  datetime.date(2009, 5, 1): 99.1125,\n", "  datetime.date(2009, 5, 4): 98.795,\n", "  datetime.date(2009, 5, 5): 98.815,\n", "  datetime.date(2009, 5, 6): 98.3125,\n", "  datetime.date(2009, 5, 7): 99.1125,\n", "  datetime.date(2009, 5, 8): 98.48,\n", "  datetime.date(2009, 5, 11): 97.485,\n", "  datetime.date(2009, 5, 12): 96.445,\n", "  datetime.date(2009, 5, 13): 95.31,\n", "  datetime.date(2009, 5, 14): 95.8,\n", "  datetime.date(2009, 5, 15): 95.2,\n", "  datetime.date(2009, 5, 18): 96.305,\n", "  datetime.date(2009, 5, 19): 95.9675,\n", "  datetime.date(2009, 5, 20): 94.88,\n", "  datetime.date(2009, 5, 21): 94.4125,\n", "  datetime.date(2009, 5, 22): 94.775,\n", "  datetime.date(2009, 5, 25): 94.83,\n", "  datetime.date(2009, 5, 26): 95.0275,\n", "  datetime.date(2009, 5, 27): 95.34,\n", "  datetime.date(2009, 5, 28): 96.8625,\n", "  datetime.date(2009, 5, 29): 95.3375,\n", "  datetime.date(2009, 6, 1): 96.5725,\n", "  datetime.date(2009, 6, 2): 95.77,\n", "  datetime.date(2009, 6, 3): 95.985,\n", "  datetime.date(2009, 6, 4): 96.57,\n", "  datetime.date(2009, 6, 5): 98.6725,\n", "  datetime.date(2009, 6, 8): 98.4875,\n", "  datetime.date(2009, 6, 9): 97.3875,\n", "  datetime.date(2009, 6, 10): 98.12,\n", "  datetime.date(2009, 6, 11): 97.64,\n", "  datetime.date(2009, 6, 12): 98.415,\n", "  datetime.date(2009, 6, 15): 97.8175,\n", "  datetime.date(2009, 6, 16): 96.39,\n", "  datetime.date(2009, 6, 17): 95.75,\n", "  datetime.date(2009, 6, 18): 96.5025,\n", "  datetime.date(2009, 6, 19): 96.285,\n", "  datetime.date(2009, 6, 22): 95.865,\n", "  datetime.date(2009, 6, 23): 95.22,\n", "  datetime.date(2009, 6, 24): 95.65,\n", "  datetime.date(2009, 6, 25): 95.9525,\n", "  datetime.date(2009, 6, 26): 95.1975,\n", "  datetime.date(2009, 6, 29): 96.0675,\n", "  datetime.date(2009, 6, 30): 96.355,\n", "  datetime.date(2009, 7, 1): 96.655,\n", "  datetime.date(2009, 7, 2): 95.9375,\n", "  datetime.date(2009, 7, 3): 96.04,\n", "  datetime.date(2009, 7, 6): 95.355,\n", "  datetime.date(2009, 7, 7): 94.8975,\n", "  datetime.date(2009, 7, 8): 92.885,\n", "  datetime.date(2009, 7, 9): 92.995,\n", "  datetime.date(2009, 7, 10): 92.565,\n", "  datetime.date(2009, 7, 13): 92.9725,\n", "  datetime.date(2009, 7, 14): 93.51,\n", "  datetime.date(2009, 7, 15): 94.2325,\n", "  datetime.date(2009, 7, 16): 93.93,\n", "  datetime.date(2009, 7, 17): 94.18,\n", "  datetime.date(2009, 7, 20): 94.1925,\n", "  datetime.date(2009, 7, 21): 93.745,\n", "  datetime.date(2009, 7, 22): 93.6675,\n", "  datetime.date(2009, 7, 23): 94.93,\n", "  datetime.date(2009, 7, 24): 94.82,\n", "  datetime.date(2009, 7, 27): 95.19,\n", "  datetime.date(2009, 7, 28): 94.55,\n", "  datetime.date(2009, 7, 29): 94.99,\n", "  datetime.date(2009, 7, 30): 95.56,\n", "  datetime.date(2009, 7, 31): 94.6825,\n", "  datetime.date(2009, 8, 3): 95.27,\n", "  datetime.date(2009, 8, 4): 95.2275,\n", "  datetime.date(2009, 8, 5): 94.96,\n", "  datetime.date(2009, 8, 6): 95.4575,\n", "  datetime.date(2009, 8, 7): 97.5525,\n", "  datetime.date(2009, 8, 10): 97.1525,\n", "  datetime.date(2009, 8, 11): 95.99,\n", "  datetime.date(2009, 8, 12): 96.055,\n", "  datetime.date(2009, 8, 13): 95.48,\n", "  datetime.date(2009, 8, 14): 94.915,\n", "  datetime.date(2009, 8, 17): 94.505,\n", "  datetime.date(2009, 8, 18): 94.685,\n", "  datetime.date(2009, 8, 19): 94.08,\n", "  datetime.date(2009, 8, 20): 94.195,\n", "  datetime.date(2009, 8, 21): 94.395,\n", "  datetime.date(2009, 8, 24): 94.5625,\n", "  datetime.date(2009, 8, 25): 94.185,\n", "  datetime.date(2009, 8, 26): 94.25,\n", "  datetime.date(2009, 8, 27): 93.52,\n", "  datetime.date(2009, 8, 28): 93.6025,\n", "  datetime.date(2009, 8, 31): 93.1225,\n", "  datetime.date(2009, 9, 1): 92.9275,\n", "  datetime.date(2009, 9, 2): 92.2075,\n", "  datetime.date(2009, 9, 3): 92.6325,\n", "  datetime.date(2009, 9, 4): 93.005,\n", "  datetime.date(2009, 9, 7): 93.0775,\n", "  datetime.date(2009, 9, 8): 92.3225,\n", "  datetime.date(2009, 9, 9): 92.04,\n", "  datetime.date(2009, 9, 10): 91.7375,\n", "  datetime.date(2009, 9, 11): 90.715,\n", "  datetime.date(2009, 9, 14): 90.9425,\n", "  datetime.date(2009, 9, 15): 91.045,\n", "  datetime.date(2009, 9, 16): 90.9275,\n", "  datetime.date(2009, 9, 17): 91.0875,\n", "  datetime.date(2009, 9, 18): 91.29,\n", "  datetime.date(2009, 9, 21): 91.925,\n", "  datetime.date(2009, 9, 22): 91.0975,\n", "  datetime.date(2009, 9, 23): 91.28,\n", "  datetime.date(2009, 9, 24): 91.265,\n", "  datetime.date(2009, 9, 25): 89.61,\n", "  datetime.date(2009, 9, 28): 89.635,\n", "  datetime.date(2009, 9, 29): 90.0875,\n", "  datetime.date(2009, 9, 30): 89.705,\n", "  datetime.date(2009, 10, 1): 89.6075,\n", "  datetime.date(2009, 10, 2): 89.81,\n", "  datetime.date(2009, 10, 5): 89.545,\n", "  datetime.date(2009, 10, 6): 88.825,\n", "  datetime.date(2009, 10, 7): 88.6025,\n", "  datetime.date(2009, 10, 8): 88.3875,\n", "  datetime.date(2009, 10, 9): 89.785,\n", "  datetime.date(2009, 10, 12): 89.8325,\n", "  datetime.date(2009, 10, 13): 89.71,\n", "  datetime.date(2009, 10, 14): 89.45,\n", "  datetime.date(2009, 10, 15): 90.555,\n", "  datetime.date(2009, 10, 16): 90.89,\n", "  datetime.date(2009, 10, 19): 90.5475,\n", "  datetime.date(2009, 10, 20): 90.7725,\n", "  datetime.date(2009, 10, 21): 90.975,\n", "  datetime.date(2009, 10, 22): 91.2925,\n", "  datetime.date(2009, 10, 23): 92.04,\n", "  datetime.date(2009, 10, 26): 92.1725,\n", "  datetime.date(2009, 10, 27): 91.795,\n", "  datetime.date(2009, 10, 28): 90.76,\n", "  datetime.date(2009, 10, 29): 91.41,\n", "  datetime.date(2009, 10, 30): 90.09,\n", "  datetime.date(2009, 11, 2): 90.205,\n", "  datetime.date(2009, 11, 3): 90.335,\n", "  datetime.date(2009, 11, 4): 90.7275,\n", "  datetime.date(2009, 11, 5): 90.71,\n", "  datetime.date(2009, 11, 6): 89.8775,\n", "  datetime.date(2009, 11, 9): 89.915,\n", "  datetime.date(2009, 11, 10): 89.805,\n", "  datetime.date(2009, 11, 11): 89.8875,\n", "  datetime.date(2009, 11, 12): 90.3725,\n", "  datetime.date(2009, 11, 13): 89.665,\n", "  datetime.date(2009, 11, 16): 89.0475,\n", "  datetime.date(2009, 11, 17): 89.255,\n", "  datetime.date(2009, 11, 18): 89.3125,\n", "  datetime.date(2009, 11, 19): 88.975,\n", "  datetime.date(2009, 11, 20): 88.885,\n", "  datetime.date(2009, 11, 23): 88.9675,\n", "  datetime.date(2009, 11, 24): 88.495,\n", "  datetime.date(2009, 11, 25): 87.3475,\n", "  datetime.date(2009, 11, 26): 86.5775,\n", "  datetime.date(2009, 11, 27): 86.535,\n", "  datetime.date(2009, 11, 30): 86.4,\n", "  datetime.date(2009, 12, 1): 86.6925,\n", "  datetime.date(2009, 12, 2): 87.39,\n", "  datetime.date(2009, 12, 3): 88.2675,\n", "  datetime.date(2009, 12, 4): 90.545,\n", "  datetime.date(2009, 12, 7): 89.51,\n", "  datetime.date(2009, 12, 8): 88.44,\n", "  datetime.date(2009, 12, 9): 87.865,\n", "  datetime.date(2009, 12, 10): 88.2,\n", "  datetime.date(2009, 12, 11): 89.1125,\n", "  datetime.date(2009, 12, 14): 88.625,\n", "  datetime.date(2009, 12, 15): 89.61,\n", "  datetime.date(2009, 12, 16): 89.78,\n", "  datetime.date(2009, 12, 17): 89.965,\n", "  datetime.date(2009, 12, 18): 90.39,\n", "  datetime.date(2009, 12, 21): 91.1775,\n", "  datetime.date(2009, 12, 22): 91.84,\n", "  datetime.date(2009, 12, 23): 91.6325,\n", "  datetime.date(2009, 12, 24): 91.555,\n", "  datetime.date(2009, 12, 28): 91.6325,\n", "  datetime.date(2009, 12, 29): 92.0,\n", "  datetime.date(2009, 12, 30): 92.4425,\n", "  datetime.date(2009, 12, 31): 93.025,\n", "  datetime.date(2010, 1, 4): 92.5175,\n", "  datetime.date(2010, 1, 5): 91.705,\n", "  datetime.date(2010, 1, 6): 92.32,\n", "  datetime.date(2010, 1, 7): 93.375,\n", "  datetime.date(2010, 1, 8): 92.65,\n", "  datetime.date(2010, 1, 11): 92.085,\n", "  datetime.date(2010, 1, 12): 90.98,\n", "  datetime.date(2010, 1, 13): 91.375,\n", "  datetime.date(2010, 1, 14): 91.21,\n", "  datetime.date(2010, 1, 15): 90.77,\n", "  datetime.date(2010, 1, 18): 90.79,\n", "  datetime.date(2010, 1, 19): 91.15,\n", "  datetime.date(2010, 1, 20): 91.2375,\n", "  datetime.date(2010, 1, 21): 90.435,\n", "  datetime.date(2010, 1, 22): 89.8275,\n", "  datetime.date(2010, 1, 25): 90.28,\n", "  datetime.date(2010, 1, 26): 89.6525,\n", "  datetime.date(2010, 1, 27): 90.0025,\n", "  datetime.date(2010, 1, 28): 89.92,\n", "  datetime.date(2010, 1, 29): 90.27,\n", "  datetime.date(2010, 2, 1): 90.6125,\n", "  datetime.date(2010, 2, 2): 90.385,\n", "  datetime.date(2010, 2, 3): 90.985,\n", "  datetime.date(2010, 2, 4): 89.0375,\n", "  datetime.date(2010, 2, 5): 89.2475,\n", "  datetime.date(2010, 2, 8): 89.2525,\n", "  datetime.date(2010, 2, 9): 89.6925,\n", "  datetime.date(2010, 2, 10): 89.94,\n", "  datetime.date(2010, 2, 11): 89.765,\n", "  datetime.date(2010, 2, 12): 89.955,\n", "  datetime.date(2010, 2, 15): 90.01,\n", "  datetime.date(2010, 2, 16): 90.14,\n", "  datetime.date(2010, 2, 17): 91.2575,\n", "  datetime.date(2010, 2, 18): 91.81,\n", "  datetime.date(2010, 2, 19): 91.51,\n", "  datetime.date(2010, 2, 22): 91.1475,\n", "  datetime.date(2010, 2, 23): 90.2225,\n", "  datetime.date(2010, 2, 24): 90.145,\n", "  datetime.date(2010, 2, 25): 89.0725,\n", "  datetime.date(2010, 2, 26): 88.9675,\n", "  datetime.date(2010, 3, 1): 89.1225,\n", "  datetime.date(2010, 3, 2): 88.85,\n", "  datetime.date(2010, 3, 3): 88.46,\n", "  datetime.date(2010, 3, 4): 89.02,\n", "  datetime.date(2010, 3, 5): 90.28,\n", "  datetime.date(2010, 3, 8): 90.3075,\n", "  datetime.date(2010, 3, 9): 89.97,\n", "  datetime.date(2010, 3, 10): 90.5125,\n", "  datetime.date(2010, 3, 11): 90.51,\n", "  datetime.date(2010, 3, 12): 90.56,\n", "  datetime.date(2010, 3, 15): 90.525,\n", "  datetime.date(2010, 3, 16): 90.3075,\n", "  datetime.date(2010, 3, 17): 90.305,\n", "  datetime.date(2010, 3, 18): 90.38,\n", "  datetime.date(2010, 3, 19): 90.5425,\n", "  datetime.date(2010, 3, 22): 90.1375,\n", "  datetime.date(2010, 3, 23): 90.4,\n", "  datetime.date(2010, 3, 24): 92.3025,\n", "  datetime.date(2010, 3, 25): 92.7325,\n", "  datetime.date(2010, 3, 26): 92.515,\n", "  datetime.date(2010, 3, 29): 92.4625,\n", "  datetime.date(2010, 3, 30): 92.76,\n", "  datetime.date(2010, 3, 31): 93.465,\n", "  datetime.date(2010, 4, 1): 93.825,\n", "  datetime.date(2010, 4, 2): 94.6075,\n", "  datetime.date(2010, 4, 5): 94.3675,\n", "  datetime.date(2010, 4, 6): 93.7925,\n", "  datetime.date(2010, 4, 7): 93.355,\n", "  datetime.date(2010, 4, 8): 93.3825,\n", "  datetime.date(2010, 4, 9): 93.17,\n", "  datetime.date(2010, 4, 12): 93.2425,\n", "  datetime.date(2010, 4, 13): 93.2,\n", "  datetime.date(2010, 4, 14): 93.225,\n", "  datetime.date(2010, 4, 15): 93.0275,\n", "  datetime.date(2010, 4, 16): 92.175,\n", "  datetime.date(2010, 4, 19): 92.3975,\n", "  datetime.date(2010, 4, 20): 93.2175,\n", "  datetime.date(2010, 4, 21): 93.19,\n", "  datetime.date(2010, 4, 22): 93.4875,\n", "  datetime.date(2010, 4, 23): 93.97,\n", "  datetime.date(2010, 4, 26): 93.96,\n", "  datetime.date(2010, 4, 27): 93.265,\n", "  datetime.date(2010, 4, 28): 94.0275,\n", "  datetime.date(2010, 4, 29): 94.025,\n", "  datetime.date(2010, 4, 30): 93.855,\n", "  datetime.date(2010, 5, 3): 94.54,\n", "  datetime.date(2010, 5, 4): 94.5375,\n", "  datetime.date(2010, 5, 5): 93.81,\n", "  datetime.date(2010, 5, 6): 90.61,\n", "  datetime.date(2010, 5, 7): 91.6025,\n", "  datetime.date(2010, 5, 10): 93.29,\n", "  datetime.date(2010, 5, 11): 92.655,\n", "  datetime.date(2010, 5, 12): 93.25,\n", "  datetime.date(2010, 5, 13): 92.7525,\n", "  datetime.date(2010, 5, 14): 92.455,\n", "  datetime.date(2010, 5, 17): 92.585,\n", "  datetime.date(2010, 5, 18): 92.2375,\n", "  datetime.date(2010, 5, 19): 91.7,\n", "  datetime.date(2010, 5, 20): 89.6875,\n", "  datetime.date(2010, 5, 21): 89.9825,\n", "  datetime.date(2010, 5, 24): 90.2925,\n", "  datetime.date(2010, 5, 25): 90.225,\n", "  datetime.date(2010, 5, 26): 89.92,\n", "  datetime.date(2010, 5, 27): 91.04,\n", "  datetime.date(2010, 5, 28): 91.06,\n", "  datetime.date(2010, 5, 31): 91.26,\n", "  datetime.date(2010, 6, 1): 90.935,\n", "  datetime.date(2010, 6, 2): 92.135,\n", "  datetime.date(2010, 6, 3): 92.71,\n", "  datetime.date(2010, 6, 4): 91.9125,\n", "  datetime.date(2010, 6, 7): 91.3725,\n", "  datetime.date(2010, 6, 8): 91.455,\n", "  datetime.date(2010, 6, 9): 91.3025,\n", "  datetime.date(2010, 6, 10): 91.33,\n", "  datetime.date(2010, 6, 11): 91.65,\n", "  datetime.date(2010, 6, 14): 91.5725,\n", "  datetime.date(2010, 6, 15): 91.46,\n", "  datetime.date(2010, 6, 16): 91.4425,\n", "  datetime.date(2010, 6, 17): 91.0025,\n", "  datetime.date(2010, 6, 18): 90.7025,\n", "  datetime.date(2010, 6, 21): 91.105,\n", "  datetime.date(2010, 6, 22): 90.57,\n", "  datetime.date(2010, 6, 23): 89.82,\n", "  datetime.date(2010, 6, 24): 89.61,\n", "  datetime.date(2010, 6, 25): 89.23,\n", "  datetime.date(2010, 6, 28): 89.3725,\n", "  datetime.date(2010, 6, 29): 88.6,\n", "  datetime.date(2010, 6, 30): 88.4375,\n", "  datetime.date(2010, 7, 1): 87.605,\n", "  datetime.date(2010, 7, 2): 87.75,\n", "  datetime.date(2010, 7, 5): 87.7675,\n", "  datetime.date(2010, 7, 6): 87.5225,\n", "  datetime.date(2010, 7, 7): 87.7025,\n", "  datetime.date(2010, 7, 8): 88.35,\n", "  datetime.date(2010, 7, 9): 88.6125,\n", "  datetime.date(2010, 7, 12): 88.62,\n", "  datetime.date(2010, 7, 13): 88.73,\n", "  datetime.date(2010, 7, 14): 88.41,\n", "  datetime.date(2010, 7, 15): 87.4025,\n", "  datetime.date(2010, 7, 16): 86.5775,\n", "  datetime.date(2010, 7, 19): 86.6925,\n", "  datetime.date(2010, 7, 20): 87.5075,\n", "  datetime.date(2010, 7, 21): 87.0525,\n", "  datetime.date(2010, 7, 22): 86.9525,\n", "  datetime.date(2010, 7, 23): 87.4125,\n", "  datetime.date(2010, 7, 26): 86.88,\n", "  datetime.date(2010, 7, 27): 87.9,\n", "  datetime.date(2010, 7, 28): 87.465,\n", "  datetime.date(2010, 7, 29): 86.785,\n", "  datetime.date(2010, 7, 30): 86.4675,\n", "  datetime.date(2010, 8, 2): 86.5,\n", "  datetime.date(2010, 8, 3): 85.7925,\n", "  datetime.date(2010, 8, 4): 86.27,\n", "  datetime.date(2010, 8, 5): 85.8275,\n", "  datetime.date(2010, 8, 6): 85.515,\n", "  datetime.date(2010, 8, 9): 85.9375,\n", "  datetime.date(2010, 8, 10): 85.4425,\n", "  datetime.date(2010, 8, 11): 85.3175,\n", "  datetime.date(2010, 8, 12): 85.89,\n", "  datetime.date(2010, 8, 13): 86.175,\n", "  datetime.date(2010, 8, 16): 85.3325,\n", "  datetime.date(2010, 8, 17): 85.525,\n", "  datetime.date(2010, 8, 18): 85.46,\n", "  datetime.date(2010, 8, 19): 85.39,\n", "  datetime.date(2010, 8, 20): 85.615,\n", "  datetime.date(2010, 8, 23): 85.1575,\n", "  datetime.date(2010, 8, 24): 83.9125,\n", "  datetime.date(2010, 8, 25): 84.57,\n", "  datetime.date(2010, 8, 26): 84.4475,\n", "  datetime.date(2010, 8, 27): 85.2125,\n", "  datetime.date(2010, 8, 30): 84.6175,\n", "  datetime.date(2010, 8, 31): 84.1975,\n", "  datetime.date(2010, 9, 1): 84.4425,\n", "  datetime.date(2010, 9, 2): 84.2725,\n", "  datetime.date(2010, 9, 3): 84.3175,\n", "  datetime.date(2010, 9, 6): 84.21,\n", "  datetime.date(2010, 9, 7): 83.8375,\n", "  datetime.date(2010, 9, 8): 83.8775,\n", "  datetime.date(2010, 9, 9): 83.78,\n", "  datetime.date(2010, 9, 10): 84.145,\n", "  datetime.date(2010, 9, 13): 83.705,\n", "  datetime.date(2010, 9, 14): 83.035,\n", "  datetime.date(2010, 9, 15): 85.7425,\n", "  datetime.date(2010, 9, 16): 85.7775,\n", "  datetime.date(2010, 9, 17): 85.855,\n", "  datetime.date(2010, 9, 20): 85.6975,\n", "  datetime.date(2010, 9, 21): 85.0925,\n", "  datetime.date(2010, 9, 22): 84.4925,\n", "  datetime.date(2010, 9, 23): 84.385,\n", "  datetime.date(2010, 9, 24): 84.21,\n", "  datetime.date(2010, 9, 27): 84.29,\n", "  datetime.date(2010, 9, 28): 83.87,\n", "  datetime.date(2010, 9, 29): 83.7075,\n", "  datetime.date(2010, 9, 30): 83.525,\n", "  datetime.date(2010, 10, 1): 83.22,\n", "  datetime.date(2010, 10, 4): 83.3575,\n", "  datetime.date(2010, 10, 5): 83.2225,\n", "  datetime.date(2010, 10, 6): 82.935,\n", "  datetime.date(2010, 10, 7): 82.4075,\n", "  datetime.date(2010, 10, 8): 81.9275,\n", "  datetime.date(2010, 10, 11): 82.0575,\n", "  datetime.date(2010, 10, 12): 81.7175,\n", "  datetime.date(2010, 10, 13): 81.81,\n", "  datetime.date(2010, 10, 14): 81.4825,\n", "  datetime.date(2010, 10, 15): 81.455,\n", "  datetime.date(2010, 10, 18): 81.2725,\n", "  datetime.date(2010, 10, 19): 81.585,\n", "  datetime.date(2010, 10, 20): 81.09,\n", "  datetime.date(2010, 10, 21): 81.3275,\n", "  datetime.date(2010, 10, 22): 81.38,\n", "  datetime.date(2010, 10, 25): 80.815,\n", "  datetime.date(2010, 10, 26): 81.4375,\n", "  datetime.date(2010, 10, 27): 81.75,\n", "  datetime.date(2010, 10, 28): 81.0225,\n", "  datetime.date(2010, 10, 29): 80.4025,\n", "  datetime.date(2010, 11, 1): 80.51,\n", "  datetime.date(2010, 11, 2): 80.625,\n", "  datetime.date(2010, 11, 3): 81.075,\n", "  datetime.date(2010, 11, 4): 80.7425,\n", "  datetime.date(2010, 11, 5): 81.255,\n", "  datetime.date(2010, 11, 8): 81.1825,\n", "  datetime.date(2010, 11, 9): 81.695,\n", "  datetime.date(2010, 11, 10): 82.2775,\n", "  datetime.date(2010, 11, 11): 82.4825,\n", "  datetime.date(2010, 11, 12): 82.525,\n", "  datetime.date(2010, 11, 15): 83.0725,\n", "  datetime.date(2010, 11, 16): 83.2825,\n", "  datetime.date(2010, 11, 17): 83.1825,\n", "  datetime.date(2010, 11, 18): 83.52,\n", "  datetime.date(2010, 11, 19): 83.5525,\n", "  datetime.date(2010, 11, 22): 83.335,\n", "  datetime.date(2010, 11, 23): 83.1625,\n", "  datetime.date(2010, 11, 24): 83.54,\n", "  datetime.date(2010, 11, 25): 83.5975,\n", "  datetime.date(2010, 11, 26): 84.11,\n", "  datetime.date(2010, 11, 29): 84.2575,\n", "  datetime.date(2010, 11, 30): 83.69,\n", "  datetime.date(2010, 12, 1): 84.1925,\n", "  datetime.date(2010, 12, 2): 83.82,\n", "  datetime.date(2010, 12, 3): 82.5325,\n", "  datetime.date(2010, 12, 6): 82.6575,\n", "  datetime.date(2010, 12, 7): 83.4925,\n", "  datetime.date(2010, 12, 8): 84.0275,\n", "  datetime.date(2010, 12, 9): 83.76,\n", "  datetime.date(2010, 12, 10): 83.9475,\n", "  datetime.date(2010, 12, 13): 83.3875,\n", "  datetime.date(2010, 12, 14): 83.6625,\n", "  datetime.date(2010, 12, 15): 84.2475,\n", "  datetime.date(2010, 12, 16): 83.9125,\n", "  datetime.date(2010, 12, 17): 83.99,\n", "  datetime.date(2010, 12, 20): 83.7725,\n", "  datetime.date(2010, 12, 21): 83.7475,\n", "  datetime.date(2010, 12, 22): 83.5675,\n", "  datetime.date(2010, 12, 23): 82.9075,\n", "  datetime.date(2010, 12, 24): 82.8875,\n", "  datetime.date(2010, 12, 27): 82.81,\n", "  datetime.date(2010, 12, 28): 82.3875,\n", "  datetime.date(2010, 12, 29): 81.61,\n", "  datetime.date(2010, 12, 30): 81.5275,\n", "  datetime.date(2010, 12, 31): 81.1375,\n", "  datetime.date(2011, 1, 3): 81.735,\n", "  datetime.date(2011, 1, 4): 82.0325,\n", "  datetime.date(2011, 1, 5): 83.2475,\n", "  datetime.date(2011, 1, 6): 83.3275,\n", "  datetime.date(2011, 1, 7): 83.1575,\n", "  datetime.date(2011, 1, 10): 82.7075,\n", "  datetime.date(2011, 1, 11): 83.245,\n", "  datetime.date(2011, 1, 12): 83.0025,\n", "  datetime.date(2011, 1, 13): 82.8175,\n", "  datetime.date(2011, 1, 14): 82.87,\n", "  datetime.date(2011, 1, 17): 82.6725,\n", "  datetime.date(2011, 1, 18): 82.5575,\n", "  datetime.date(2011, 1, 19): 82.0175,\n", "  datetime.date(2011, 1, 20): 83.0025,\n", "  datetime.date(2011, 1, 21): 82.565,\n", "  datetime.date(2011, 1, 24): 82.525,\n", "  datetime.date(2011, 1, 25): 82.25,\n", "  datetime.date(2011, 1, 26): 82.1725,\n", "  datetime.date(2011, 1, 27): 82.925,\n", "  datetime.date(2011, 1, 28): 82.1275,\n", "  datetime.date(2011, 1, 31): 82.0425,\n", "  datetime.date(2011, 2, 1): 81.3575,\n", "  datetime.date(2011, 2, 2): 81.5475,\n", "  datetime.date(2011, 2, 3): 81.6275,\n", "  datetime.date(2011, 2, 4): 82.18,\n", "  datetime.date(2011, 2, 7): 82.3275,\n", "  datetime.date(2011, 2, 8): 82.3625,\n", "  datetime.date(2011, 2, 9): 82.36,\n", "  datetime.date(2011, 2, 10): 83.225,\n", "  datetime.date(2011, 2, 11): 83.4375,\n", "  datetime.date(2011, 2, 14): 83.325,\n", "  datetime.date(2011, 2, 15): 83.7675,\n", "  datetime.date(2011, 2, 16): 83.68,\n", "  datetime.date(2011, 2, 17): 83.3125,\n", "  datetime.date(2011, 2, 18): 83.1825,\n", "  datetime.date(2011, 2, 21): 83.14,\n", "  datetime.date(2011, 2, 22): 82.765,\n", "  datetime.date(2011, 2, 23): 82.515,\n", "  datetime.date(2011, 2, 24): 81.8925,\n", "  datetime.date(2011, 2, 25): 81.685,\n", "  datetime.date(2011, 2, 28): 81.7825,\n", "  datetime.date(2011, 3, 1): 81.8575,\n", "  datetime.date(2011, 3, 2): 81.8775,\n", "  datetime.date(2011, 3, 3): 82.4425,\n", "  datetime.date(2011, 3, 4): 82.3125,\n", "  datetime.date(2011, 3, 7): 82.24,\n", "  datetime.date(2011, 3, 8): 82.6675,\n", "  datetime.date(2011, 3, 9): 82.7375,\n", "  datetime.date(2011, 3, 10): 82.99,\n", "  datetime.date(2011, 3, 11): 81.8425,\n", "  datetime.date(2011, 3, 14): 81.6325,\n", "  datetime.date(2011, 3, 15): 80.7225,\n", "  datetime.date(2011, 3, 16): 79.5925,\n", "  datetime.date(2011, 3, 17): 78.8975,\n", "  datetime.date(2011, 3, 18): 80.5825,\n", "  datetime.date(2011, 3, 21): 81.02,\n", "  datetime.date(2011, 3, 22): 80.97,\n", "  datetime.date(2011, 3, 23): 80.9175,\n", "  datetime.date(2011, 3, 24): 80.9775,\n", "  datetime.date(2011, 3, 25): 81.345,\n", "  datetime.date(2011, 3, 28): 81.6925,\n", "  datetime.date(2011, 3, 29): 82.475,\n", "  datetime.date(2011, 3, 30): 82.89,\n", "  datetime.date(2011, 3, 31): 83.1325,\n", "  datetime.date(2011, 4, 1): 84.0625,\n", "  datetime.date(2011, 4, 4): 84.0575,\n", "  datetime.date(2011, 4, 5): 84.865,\n", "  datetime.date(2011, 4, 6): 85.485,\n", "  datetime.date(2011, 4, 7): 84.9125,\n", "  datetime.date(2011, 4, 8): 84.77,\n", "  datetime.date(2011, 4, 11): 84.6025,\n", "  datetime.date(2011, 4, 12): 83.58,\n", "  datetime.date(2011, 4, 13): 83.8375,\n", "  datetime.date(2011, 4, 14): 83.5,\n", "  datetime.date(2011, 4, 15): 83.1375,\n", "  datetime.date(2011, 4, 18): 82.66,\n", "  datetime.date(2011, 4, 19): 82.585,\n", "  datetime.date(2011, 4, 20): 82.56,\n", "  datetime.date(2011, 4, 21): 81.8525,\n", "  datetime.date(2011, 4, 22): 81.88,\n", "  datetime.date(2011, 4, 25): 81.8375,\n", "  datetime.date(2011, 4, 26): 81.555,\n", "  datetime.date(2011, 4, 27): 82.155,\n", "  datetime.date(2011, 4, 28): 81.535,\n", "  datetime.date(2011, 4, 29): 81.1925,\n", "  datetime.date(2011, 5, 2): 81.2225,\n", "  datetime.date(2011, 5, 3): 80.9275,\n", "  datetime.date(2011, 5, 4): 80.6175,\n", "  datetime.date(2011, 5, 5): 80.0825,\n", "  datetime.date(2011, 5, 6): 80.6325,\n", "  datetime.date(2011, 5, 9): 80.3575,\n", "  datetime.date(2011, 5, 10): 80.88,\n", "  datetime.date(2011, 5, 11): 81.045,\n", "  datetime.date(2011, 5, 12): 80.94,\n", "  datetime.date(2011, 5, 13): 80.795,\n", "  datetime.date(2011, 5, 16): 80.79,\n", "  datetime.date(2011, 5, 17): 81.41,\n", "  datetime.date(2011, 5, 18): 81.6775,\n", "  datetime.date(2011, 5, 19): 81.61,\n", "  datetime.date(2011, 5, 20): 81.705,\n", "  datetime.date(2011, 5, 23): 82.005,\n", "  datetime.date(2011, 5, 24): 81.9525,\n", "  datetime.date(2011, 5, 25): 81.9725,\n", "  datetime.date(2011, 5, 26): 81.2875,\n", "  datetime.date(2011, 5, 27): 80.805,\n", "  datetime.date(2011, 5, 30): 80.9375,\n", "  datetime.date(2011, 5, 31): 81.5225,\n", "  datetime.date(2011, 6, 1): 80.9475,\n", "  datetime.date(2011, 6, 2): 80.8925,\n", "  datetime.date(2011, 6, 3): 80.285,\n", "  datetime.date(2011, 6, 6): 80.1025,\n", "  datetime.date(2011, 6, 7): 80.095,\n", "  datetime.date(2011, 6, 8): 79.8975,\n", "  datetime.date(2011, 6, 9): 80.365,\n", "  datetime.date(2011, 6, 10): 80.3275,\n", "  datetime.date(2011, 6, 13): 80.24,\n", "  datetime.date(2011, 6, 14): 80.485,\n", "  datetime.date(2011, 6, 15): 80.9575,\n", "  datetime.date(2011, 6, 16): 80.6175,\n", "  datetime.date(2011, 6, 17): 80.04,\n", "  datetime.date(2011, 6, 20): 80.2575,\n", "  datetime.date(2011, 6, 21): 80.2125,\n", "  datetime.date(2011, 6, 22): 80.295,\n", "  datetime.date(2011, 6, 23): 80.5125,\n", "  datetime.date(2011, 6, 24): 80.43,\n", "  datetime.date(2011, 6, 27): 80.8925,\n", "  datetime.date(2011, 6, 28): 81.12,\n", "  datetime.date(2011, 6, 29): 80.7825,\n", "  datetime.date(2011, 6, 30): 80.5675,\n", "  datetime.date(2011, 7, 1): 80.83,\n", "  datetime.date(2011, 7, 4): 80.8,\n", "  datetime.date(2011, 7, 5): 81.07,\n", "  datetime.date(2011, 7, 6): 80.915,\n", "  datetime.date(2011, 7, 7): 81.245,\n", "  datetime.date(2011, 7, 8): 80.595,\n", "  datetime.date(2011, 7, 11): 80.2575,\n", "  datetime.date(2011, 7, 12): 79.24,\n", "  datetime.date(2011, 7, 13): 78.975,\n", "  datetime.date(2011, 7, 14): 79.1375,\n", "  datetime.date(2011, 7, 15): 79.13,\n", "  datetime.date(2011, 7, 18): 79.04,\n", "  datetime.date(2011, 7, 19): 79.1825,\n", "  datetime.date(2011, 7, 20): 78.7775,\n", "  datetime.date(2011, 7, 21): 78.2975,\n", "  datetime.date(2011, 7, 22): 78.5375,\n", "  datetime.date(2011, 7, 25): 78.29,\n", "  datetime.date(2011, 7, 26): 77.88,\n", "  datetime.date(2011, 7, 27): 77.98,\n", "  datetime.date(2011, 7, 28): 77.6725,\n", "  datetime.date(2011, 7, 29): 76.7875,\n", "  datetime.date(2011, 8, 1): 77.2125,\n", "  datetime.date(2011, 8, 2): 77.155,\n", "  datetime.date(2011, 8, 3): 77.0575,\n", "  datetime.date(2011, 8, 4): 78.8875,\n", "  datetime.date(2011, 8, 5): 78.4025,\n", "  datetime.date(2011, 8, 8): 77.76,\n", "  datetime.date(2011, 8, 9): 76.9625,\n", "  datetime.date(2011, 8, 10): 76.8575,\n", "  datetime.date(2011, 8, 11): 76.8425,\n", "  datetime.date(2011, 8, 12): 76.7225,\n", "  datetime.date(2011, 8, 15): 76.8425,\n", "  datetime.date(2011, 8, 16): 76.7975,\n", "  datetime.date(2011, 8, 17): 76.595,\n", "  datetime.date(2011, 8, 18): 76.585,\n", "  datetime.date(2011, 8, 19): 76.545,\n", "  datetime.date(2011, 8, 22): 76.795,\n", "  datetime.date(2011, 8, 23): 76.6575,\n", "  datetime.date(2011, 8, 24): 76.9775,\n", "  datetime.date(2011, 8, 25): 77.46,\n", "  datetime.date(2011, 8, 26): 76.6625,\n", "  datetime.date(2011, 8, 29): 76.835,\n", "  datetime.date(2011, 8, 30): 76.7375,\n", "  datetime.date(2011, 8, 31): 76.6625,\n", "  datetime.date(2011, 9, 1): 76.9275,\n", "  datetime.date(2011, 9, 2): 76.8025,\n", "  datetime.date(2011, 9, 5): 76.8925,\n", "  datetime.date(2011, 9, 6): 77.66,\n", "  datetime.date(2011, 9, 7): 77.26,\n", "  datetime.date(2011, 9, 8): 77.5025,\n", "  datetime.date(2011, 9, 9): 77.61,\n", "  datetime.date(2011, 9, 12): 77.2175,\n", "  datetime.date(2011, 9, 13): 76.945,\n", "  datetime.date(2011, 9, 14): 76.6275,\n", "  datetime.date(2011, 9, 15): 76.7,\n", "  datetime.date(2011, 9, 16): 76.795,\n", "  datetime.date(2011, 9, 19): 76.5925,\n", "  datetime.date(2011, 9, 20): 76.45,\n", "  datetime.date(2011, 9, 21): 76.455,\n", "  datetime.date(2011, 9, 22): 76.2425,\n", "  datetime.date(2011, 9, 23): 76.61,\n", "  datetime.date(2011, 9, 26): 76.36,\n", "  datetime.date(2011, 9, 27): 76.815,\n", "  datetime.date(2011, 9, 28): 76.61,\n", "  datetime.date(2011, 9, 29): 76.835,\n", "  datetime.date(2011, 9, 30): 77.0625,\n", "  datetime.date(2011, 10, 3): 76.63,\n", "  datetime.date(2011, 10, 4): 76.8075,\n", "  ...}}"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["fx_converter.pricing_data"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}