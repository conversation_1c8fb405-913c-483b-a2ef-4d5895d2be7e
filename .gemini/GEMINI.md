# Project Overview

This project is a full-stack trading application consisting of a Python backend, a Svelte frontend, and a comprehensive quantitative finance library.

## `trading_backend`

A FastAPI application that provides a REST API and WebSocket interface for real-time trading. It manages trading logic, user authentication, and communication with the frontend.

### Key Components

*   **`app/communicator.py`**: A singleton that manages the FastAPI application and WebSocket connections. It defines all API endpoints and the WebSocket logic. It also sets up logging with a custom filter to add `client_id` to log records.
*   **`app/manager.py`**: A singleton that manages `ClientInstance` objects. It's responsible for creating, removing, and retrieving client instances.
*   **`app/client_instance.py`**: Represents a single client's trading environment. It contains an instance of the `TradingEngine` and the `InteractiveBrokersAPI`. It handles all the trading logic for a specific client.
*   **`app/models.py`**: Contains all the Pydantic models used for API requests and responses.
*   **`app/configuration/settings.py`**: Defines a `Settings` class that reads configuration from `config.ini`.
*   **`run_server.py`**: Starts the `uvicorn` server to run the FastAPI application.

## `frontend`

A SvelteKit web application that provides the user interface for the trading platform. It includes a dashboard and a simulated trading view.

### Key Components

*   **`src/lib/api.ts`**: Contains functions for making API calls to the backend.
*   **`src/lib/components`**:
    *   `DashboardPage.svelte`: The main component for the dashboard. It handles client connection, displays logs, and has a button to manage symbols.
    *   `ErrorBoundary.svelte`: A component to catch and display errors gracefully.
    *   `OrderModal.svelte`: A modal for creating and submitting new orders.
    *   `SymbolModal.svelte`: A modal for managing the symbol database.
    *   `TradingPage.svelte`: The main component for the trading view. It displays positions and P&L.
*   **`src/lib/stores`**:
    *   `client.ts`: A Svelte store for managing the client's connection state.
    *   `logs.ts`: A Svelte store for managing log messages received from the backend via WebSocket.
    *   `positions.ts`: A Svelte store for managing the trading positions.
    *   `symbols.ts`: A Svelte store for managing the symbol database.
*   **`src/routes`**: Defines the application's routes.
    *   `+layout.svelte`: The main layout of the application, including the sidebar navigation.
    *   `+page.svelte`: The root page, which redirects to `/dashboard`.
*   **`svelte.config.js`**: Configures SvelteKit to use the static adapter for SPA deployment.

## `mattlibrary`

A modular Python library for quantitative financial analysis, with a strong focus on backtesting trading strategies.

### Key Modules

*   **`backtesting`**: Contains the `BacktestEngine` for running backtests and `BacktestConfiguration` for configuring them.
*   **`datamanagement`**: Provides clients for various data sources, including `ClickHouse`, `Dukascopy`, `InteractiveBrokers`, and `Polygon.io`. It also includes a `PolarsDataFrameManager` for efficient data storage.
*   **`trading`**: The core module of the library. It includes the `TradingEngine`, base classes for strategies and orders, position and trade management, and an execution engine for simulations.
*   **`visualizations`**: Provides utilities for creating charts and plots using `Bokeh` and `Plotly`.
*   **`optimization`**: Includes an `Optimizer` class for running parameter optimization sweeps.
*   **`logging`**: Provides standardized logging, including an `ExcelLogger`.
*   **`helpers`**: Contains various utility functions.

## Building and Running

### Backend

To run the backend server:

1.  **Activate the virtual environment:**
    ```bash
    source .venv/bin/activate
    ```

2.  **Start the server:**
    ```bash
    python trading_backend/run_server.py
    ```

The backend will be available at `http://localhost:8000`.

### Frontend

To run the frontend development server:

1.  **Navigate to the frontend directory:**
    ```bash
    cd frontend
    ```

2.  **Install dependencies:**
    ```bash
    npm install
    ```

3.  **Start the development server:**
    ```bash
    npm run dev
    ```

The frontend will be available at `http://localhost:5173`.